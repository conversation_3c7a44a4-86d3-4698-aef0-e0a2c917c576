{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowJs": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "baseUrl": ".", "paths": {"@taraka/shared": ["./packages/shared/src"], "@taraka/shared/*": ["./packages/shared/src/*"], "@taraka/astrology": ["./packages/astrology/src"], "@taraka/astrology/*": ["./packages/astrology/src/*"], "@taraka/ui": ["./packages/ui/src"], "@taraka/ui/*": ["./packages/ui/src/*"]}}, "include": ["apps/**/*", "packages/**/*"], "exclude": ["node_modules", "dist", "build", "**/*.test.*", "**/*.spec.*"], "references": [{"path": "./packages/shared"}, {"path": "./packages/astrology"}, {"path": "./packages/ui"}, {"path": "./apps/web"}, {"path": "./apps/mobile"}]}