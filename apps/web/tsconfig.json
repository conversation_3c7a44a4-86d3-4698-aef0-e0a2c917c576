{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@taraka/shared": ["../../packages/shared/src"], "@taraka/shared/*": ["../../packages/shared/src/*"], "@taraka/astrology": ["../../packages/astrology/src"], "@taraka/astrology/*": ["../../packages/astrology/src/*"], "@taraka/ui": ["../../packages/ui/src"], "@taraka/ui/*": ["../../packages/ui/src/*"]}}, "include": ["src"], "references": [{"path": "../../packages/shared"}, {"path": "../../packages/astrology"}, {"path": "../../packages/ui"}]}