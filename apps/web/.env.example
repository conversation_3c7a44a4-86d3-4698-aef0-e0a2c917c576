# Supabase Configuration for Taraka Astrology App
# Copy this file to .env.local and replace with your actual credentials

# Your Supabase Project URL
# Found in: Supabase Dashboard → Settings → API → Project URL
# Format: https://your-project-ref.supabase.co
VITE_SUPABASE_URL=https://your-project-ref.supabase.co

# Your Supabase Anon/Public Key
# Found in: Supabase Dashboard → Settings → API → Project API keys → anon public
# This is a long JWT token starting with eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.your-anon-key-here

# Optional: Environment identifier
NODE_ENV=development

# ⚠️  IMPORTANT NOTES:
# 1. Never commit .env.local to git (it's in .gitignore)
# 2. Use the ANON key, NOT the service_role key
# 3. The anon key is safe to use in frontend code
# 4. Your project URL should NOT have a trailing slash
