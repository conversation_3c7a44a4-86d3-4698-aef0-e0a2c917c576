import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { <PERSON><PERSON>, Card, ChartRenderer } from '@taraka/ui';
import { ChartBuilder } from '@taraka/astrology';
import { BirthData, NatalChart } from '@taraka/shared';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { UserProfile } from './components/profile/UserProfile';
import { useAuth } from './hooks/useAuth';

// Demo birth data
const demoBirthData: BirthData = {
  date: new Date('1990-07-15'),
  time: { hour: 14, minute: 30 },
  location: {
    name: 'New York, NY',
    coordinates: { latitude: 40.7128, longitude: -74.0060 },
    timezone: 'America/New_York'
  }
};

function HomePage() {
  const [chart, setChart] = useState<NatalChart | null>(null);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  const generateChart = async () => {
    setLoading(true);
    try {
      const newChart = await ChartBuilder.buildChart(demoBirthData, 'western');
      setChart(newChart);
    } catch (error) {
      console.error('Error generating chart:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen mystical-gradient">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-4">
            🌌 Taraka
          </h1>
          <p className="text-xl text-purple-200 max-w-2xl mx-auto">
            Discover your cosmic blueprint through personalized astrology across global traditions
          </p>
        </header>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {/* User Profile */}
          {user && (
            <div className="mb-8">
              <UserProfile />
            </div>
          )}

          <Card variant="mystical" className="mb-8">
            <div className="text-center">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Generate Your Natal Chart
              </h2>
              <p className="text-gray-600 mb-6">
                Experience the mystical art of astrology with our demo chart generator
              </p>
              <Button
                variant="mystical"
                size="lg"
                onClick={generateChart}
                disabled={loading}
              >
                {loading ? 'Calculating...' : 'Generate Demo Chart'}
              </Button>
            </div>
          </Card>

          {/* Chart Display */}
          {chart && (
            <Card variant="elevated" className="text-center">
              <h3 className="text-xl font-semibold mb-6">Your Natal Chart</h3>
              <div className="flex justify-center mb-6">
                <ChartRenderer chart={chart} size={300} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong>Rising Sign:</strong> {chart.ascendant.sign}
                </div>
                <div>
                  <strong>System:</strong> {chart.system}
                </div>
                <div>
                  <strong>Planets:</strong> {chart.planets.length}
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <HomePage />
            </ProtectedRoute>
          }
        />
      </Routes>
    </Router>
  );
}

export default App;
