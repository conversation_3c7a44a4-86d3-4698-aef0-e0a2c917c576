import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { <PERSON><PERSON>, Card, ChartRenderer } from '@taraka/ui';
import { ChartBuilder } from '@taraka/astrology';
import { BirthData, NatalChart } from '@taraka/shared';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { UserProfile } from './components/profile/UserProfile';
import { AuthCallback } from './pages/auth/AuthCallback';
import { OnboardingFlow } from './components/onboarding/OnboardingFlow';
import { useAuth } from './hooks/useAuth';
import { useProfile } from './hooks/useProfile';

// Demo birth data
const demoBirthData: BirthData = {
  date: new Date('1990-07-15'),
  time: { hour: 14, minute: 30 },
  location: {
    name: 'New York, NY',
    coordinates: { latitude: 40.7128, longitude: -74.0060 },
    timezone: 'America/New_York'
  }
};

function HomePage() {
  const [chart, setChart] = useState<NatalChart | null>(null);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { profile } = useProfile();

  // Show onboarding if user hasn't completed it
  if (profile && !profile.onboarding_completed) {
    return <OnboardingFlow />;
  }

  const generateChart = async () => {
    if (!profile?.birth_date || !profile?.birth_time || !profile?.birth_coordinates) {
      console.error('Birth data incomplete');
      return;
    }

    setLoading(true);
    try {
      // Convert profile birth data to BirthData format
      const birthData: BirthData = {
        date: new Date(profile.birth_date + 'T' + profile.birth_time),
        time: {
          hour: parseInt(profile.birth_time.split(':')[0]),
          minute: parseInt(profile.birth_time.split(':')[1]),
        },
        location: {
          name: profile.birth_location || 'Unknown',
          coordinates: {
            latitude: profile.birth_coordinates.lat,
            longitude: profile.birth_coordinates.lng,
          },
          timezone: profile.timezone || 'UTC',
        },
      };

      const system = profile.preferences?.astrology_system || 'western';
      const newChart = await ChartBuilder.buildChart(birthData, system);
      setChart(newChart);
    } catch (error) {
      console.error('Error generating chart:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen mystical-gradient">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="text-center mb-12">
          <h1 className="text-5xl font-bold text-white mb-4">
            🌌 Taraka
          </h1>
          <p className="text-xl text-purple-200 max-w-2xl mx-auto">
            Discover your cosmic blueprint through personalized astrology across global traditions
          </p>
        </header>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {/* User Profile */}
          {user && (
            <div className="mb-8">
              <UserProfile />
            </div>
          )}

          <Card variant="mystical" className="mb-8">
            <div className="text-center">
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Your Natal Chart
              </h2>
              {profile?.birth_date && (
                <div className="text-gray-600 mb-4">
                  <p>Born: {new Date(profile.birth_date).toLocaleDateString()} at {profile.birth_time}</p>
                  <p>Location: {profile.birth_location}</p>
                  <p>System: {profile.preferences?.astrology_system || 'western'} • Mode: {profile.preferences?.prediction_mode || 'reflective'}</p>
                </div>
              )}
              <p className="text-gray-600 mb-6">
                Generate your personalized natal chart based on your birth data
              </p>
              <Button
                variant="mystical"
                size="lg"
                onClick={generateChart}
                disabled={loading || !profile?.birth_date}
              >
                {loading ? 'Calculating Your Chart...' : 'Generate My Natal Chart'}
              </Button>
            </div>
          </Card>

          {/* Chart Display */}
          {chart && (
            <Card variant="elevated" className="text-center">
              <h3 className="text-xl font-semibold mb-6">Your Natal Chart</h3>
              <div className="flex justify-center mb-6">
                <ChartRenderer chart={chart} size={300} />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong>Rising Sign:</strong> {chart.ascendant.sign}
                </div>
                <div>
                  <strong>System:</strong> {chart.system}
                </div>
                <div>
                  <strong>Planets:</strong> {chart.planets.length}
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <HomePage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/auth/callback"
          element={<AuthCallback />}
        />
      </Routes>
    </Router>
  );
}

export default App;
