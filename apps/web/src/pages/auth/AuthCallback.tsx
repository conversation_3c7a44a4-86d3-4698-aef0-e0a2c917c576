import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loading } from '@taraka/ui';
import { supabase } from '../../lib/supabase';

export const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Handle the OAuth callback
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth callback error:', error);
          setError(error.message);
          // Redirect to home page with error after 3 seconds
          setTimeout(() => navigate('/?error=auth_failed'), 3000);
          return;
        }

        if (data.session) {
          // Successfully authenticated, redirect to home
          navigate('/', { replace: true });
        } else {
          // No session found, redirect to home
          navigate('/', { replace: true });
        }
      } catch (err) {
        console.error('Unexpected error during auth callback:', err);
        setError('An unexpected error occurred during authentication');
        setTimeout(() => navigate('/?error=auth_failed'), 3000);
      }
    };

    handleAuthCallback();
  }, [navigate]);

  if (error) {
    return (
      <div className="min-h-screen mystical-gradient flex items-center justify-center px-4">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-white mb-4">Authentication Error</h1>
          <p className="text-red-200 mb-4">{error}</p>
          <p className="text-purple-200 text-sm">Redirecting you back...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen mystical-gradient flex items-center justify-center px-4">
      <div className="text-center">
        <Loading size="lg" className="mx-auto mb-6" />
        <h1 className="text-2xl font-bold text-white mb-4">
          🌟 Completing your cosmic connection...
        </h1>
        <p className="text-purple-200">
          Please wait while we finalize your authentication
        </p>
      </div>
    </div>
  );
};
