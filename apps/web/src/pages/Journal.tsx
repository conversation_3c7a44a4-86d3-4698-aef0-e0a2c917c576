import React, { useState, useEffect } from 'react';
import { Card, Button, Input } from '@taraka/ui';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay } from 'date-fns';

interface JournalEntry {
  id: string;
  date: Date;
  mood: 'amazing' | 'good' | 'neutral' | 'challenging' | 'difficult';
  energy: number; // 1-5
  reflection: string;
  tags: string[];
  moonPhase?: string;
  dominantPlanet?: string;
}

const MOODS = [
  { value: 'amazing', emoji: '🌟', label: 'Amazing', color: 'bg-yellow-100 text-yellow-700' },
  { value: 'good', emoji: '😊', label: 'Good', color: 'bg-green-100 text-green-700' },
  { value: 'neutral', emoji: '😐', label: 'Neutral', color: 'bg-gray-100 text-gray-700' },
  { value: 'challenging', emoji: '😔', label: 'Challenging', color: 'bg-orange-100 text-orange-700' },
  { value: 'difficult', emoji: '😰', label: 'Difficult', color: 'bg-red-100 text-red-700' },
];

const SUGGESTED_TAGS = [
  'love', 'career', 'family', 'creativity', 'spirituality', 'health', 
  'relationships', 'growth', 'challenges', 'insights', 'dreams', 'intuition'
];

export const Journal: React.FC = () => {
  const [entries, setEntries] = useState<JournalEntry[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [currentEntry, setCurrentEntry] = useState<Partial<JournalEntry>>({
    mood: 'neutral',
    energy: 3,
    reflection: '',
    tags: [],
  });
  const [isEditing, setIsEditing] = useState(false);
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');

  // Mock data for demonstration
  useEffect(() => {
    const mockEntries: JournalEntry[] = [
      {
        id: '1',
        date: new Date(),
        mood: 'good',
        energy: 4,
        reflection: 'Feeling aligned with my purpose today. The morning meditation really helped center me.',
        tags: ['spirituality', 'growth'],
        moonPhase: 'Waxing Crescent',
        dominantPlanet: 'Venus',
      },
      {
        id: '2',
        date: new Date(Date.now() - 86400000), // Yesterday
        mood: 'challenging',
        energy: 2,
        reflection: 'Work was stressful, but I learned something important about setting boundaries.',
        tags: ['career', 'challenges', 'growth'],
        moonPhase: 'New Moon',
        dominantPlanet: 'Saturn',
      },
    ];
    setEntries(mockEntries);
  }, []);

  const getCurrentEntry = () => {
    return entries.find(entry => isSameDay(entry.date, selectedDate));
  };

  const handleSaveEntry = () => {
    const entry: JournalEntry = {
      id: getCurrentEntry()?.id || Date.now().toString(),
      date: selectedDate,
      mood: currentEntry.mood as any,
      energy: currentEntry.energy || 3,
      reflection: currentEntry.reflection || '',
      tags: currentEntry.tags || [],
      moonPhase: 'Waxing Gibbous', // This would be calculated
      dominantPlanet: 'Mercury', // This would be calculated
    };

    setEntries(prev => {
      const existing = prev.findIndex(e => e.id === entry.id);
      if (existing >= 0) {
        const updated = [...prev];
        updated[existing] = entry;
        return updated;
      }
      return [...prev, entry];
    });

    setIsEditing(false);
  };

  const handleEditEntry = () => {
    const existing = getCurrentEntry();
    if (existing) {
      setCurrentEntry(existing);
    } else {
      setCurrentEntry({
        mood: 'neutral',
        energy: 3,
        reflection: '',
        tags: [],
      });
    }
    setIsEditing(true);
  };

  const toggleTag = (tag: string) => {
    setCurrentEntry(prev => ({
      ...prev,
      tags: prev.tags?.includes(tag) 
        ? prev.tags.filter(t => t !== tag)
        : [...(prev.tags || []), tag]
    }));
  };

  const renderCalendarView = () => {
    const monthStart = startOfMonth(selectedDate);
    const monthEnd = endOfMonth(selectedDate);
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

    return (
      <div className="grid grid-cols-7 gap-2">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="p-2 text-center font-medium text-gray-600 text-sm">
            {day}
          </div>
        ))}
        {days.map(day => {
          const entry = entries.find(e => isSameDay(e.date, day));
          const isSelected = isSameDay(day, selectedDate);
          const mood = MOODS.find(m => m.value === entry?.mood);
          
          return (
            <button
              key={day.toISOString()}
              onClick={() => setSelectedDate(day)}
              className={`p-3 rounded-lg border-2 transition-all ${
                isSelected 
                  ? 'border-purple-500 bg-purple-50' 
                  : 'border-gray-200 hover:border-purple-300'
              }`}
            >
              <div className="text-sm font-medium">{format(day, 'd')}</div>
              {entry && (
                <div className="mt-1">
                  <span className="text-lg">{mood?.emoji}</span>
                </div>
              )}
            </button>
          );
        })}
      </div>
    );
  };

  const existingEntry = getCurrentEntry();

  return (
    <div className="min-h-screen mystical-gradient py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold cosmic-text mb-2">🌙 Cosmic Journal</h1>
          <p className="text-gray-600">Track your moods and connect with celestial influences</p>
        </div>

        {/* View Toggle */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-lg">
            <button
              onClick={() => setViewMode('calendar')}
              className={`px-4 py-2 rounded-md font-medium transition-all ${
                viewMode === 'calendar' ? 'bg-purple-600 text-white' : 'text-gray-600'
              }`}
            >
              📅 Calendar
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-4 py-2 rounded-md font-medium transition-all ${
                viewMode === 'list' ? 'bg-purple-600 text-white' : 'text-gray-600'
              }`}
            >
              📝 List
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Calendar/List View */}
          <div className="lg:col-span-2">
            <Card variant="elevated">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-800">
                  {format(selectedDate, 'MMMM yyyy')}
                </h2>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() - 1))}
                  >
                    ←
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedDate(new Date())}
                  >
                    Today
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedDate(new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1))}
                  >
                    →
                  </Button>
                </div>
              </div>

              {viewMode === 'calendar' ? renderCalendarView() : (
                <div className="space-y-4">
                  {entries.map(entry => (
                    <div
                      key={entry.id}
                      className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                      onClick={() => setSelectedDate(entry.date)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{format(entry.date, 'MMM d, yyyy')}</span>
                        <span className="text-2xl">{MOODS.find(m => m.value === entry.mood)?.emoji}</span>
                      </div>
                      <p className="text-gray-600 text-sm line-clamp-2">{entry.reflection}</p>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          </div>

          {/* Entry Details */}
          <div>
            <Card variant="elevated">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800">
                  {format(selectedDate, 'MMM d, yyyy')}
                </h3>
                <Button
                  variant={isEditing ? 'secondary' : 'mystical'}
                  size="sm"
                  onClick={isEditing ? () => setIsEditing(false) : handleEditEntry}
                >
                  {isEditing ? 'Cancel' : existingEntry ? 'Edit' : 'Add Entry'}
                </Button>
              </div>

              {isEditing ? (
                <div className="space-y-4">
                  {/* Mood Selection */}
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">Mood</label>
                    <div className="grid grid-cols-2 gap-2">
                      {MOODS.map(mood => (
                        <label
                          key={mood.value}
                          className={`p-3 border-2 rounded-lg cursor-pointer transition-all text-center ${
                            currentEntry.mood === mood.value
                              ? 'border-purple-500 bg-purple-50'
                              : 'border-gray-200 hover:border-purple-300'
                          }`}
                        >
                          <input
                            type="radio"
                            name="mood"
                            value={mood.value}
                            checked={currentEntry.mood === mood.value}
                            onChange={(e) => setCurrentEntry(prev => ({ ...prev, mood: e.target.value as any }))}
                            className="sr-only"
                          />
                          <div className="text-lg mb-1">{mood.emoji}</div>
                          <div className="text-xs font-medium">{mood.label}</div>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Energy Level */}
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                      Energy Level: {currentEntry.energy}/5
                    </label>
                    <input
                      type="range"
                      min="1"
                      max="5"
                      value={currentEntry.energy}
                      onChange={(e) => setCurrentEntry(prev => ({ ...prev, energy: parseInt(e.target.value) }))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>

                  {/* Reflection */}
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">Reflection</label>
                    <textarea
                      value={currentEntry.reflection}
                      onChange={(e) => setCurrentEntry(prev => ({ ...prev, reflection: e.target.value }))}
                      placeholder="How are you feeling? What insights did you gain today?"
                      className="w-full p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      rows={4}
                    />
                  </div>

                  {/* Tags */}
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-2 block">Tags</label>
                    <div className="flex flex-wrap gap-2">
                      {SUGGESTED_TAGS.map(tag => (
                        <button
                          key={tag}
                          type="button"
                          onClick={() => toggleTag(tag)}
                          className={`px-3 py-1 rounded-full text-xs font-medium transition-all ${
                            currentEntry.tags?.includes(tag)
                              ? 'bg-purple-600 text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-purple-100'
                          }`}
                        >
                          {tag}
                        </button>
                      ))}
                    </div>
                  </div>

                  <Button variant="mystical" onClick={handleSaveEntry} className="w-full">
                    Save Entry
                  </Button>
                </div>
              ) : existingEntry ? (
                <div className="space-y-4">
                  <div className="text-center">
                    <span className="text-4xl">{MOODS.find(m => m.value === existingEntry.mood)?.emoji}</span>
                    <p className="text-lg font-medium mt-2">{MOODS.find(m => m.value === existingEntry.mood)?.label}</p>
                    <p className="text-sm text-gray-600">Energy: {existingEntry.energy}/5</p>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-800 mb-2">Reflection</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">{existingEntry.reflection}</p>
                  </div>

                  {existingEntry.tags.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-800 mb-2">Tags</h4>
                      <div className="flex flex-wrap gap-2">
                        {existingEntry.tags.map(tag => (
                          <span key={tag} className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                    <h4 className="font-medium text-blue-800 mb-1">🌙 Cosmic Context</h4>
                    <p className="text-blue-700 text-sm">
                      Moon Phase: {existingEntry.moonPhase}<br/>
                      Dominant Planet: {existingEntry.dominantPlanet}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">📝</div>
                  <p className="text-gray-600 mb-4">No entry for this date</p>
                  <Button variant="mystical" onClick={handleEditEntry}>
                    Create Entry
                  </Button>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
