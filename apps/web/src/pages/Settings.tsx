import React, { useState } from 'react';
import { Card, Button, Input } from '@taraka/ui';
import { useAuth } from '../hooks/useAuth';
import { useProfile } from '../hooks/useProfile';
import { LocationSearch } from '../components/onboarding/LocationSearch';
import { Navigation } from '../components/layout/Navigation';

export const Settings: React.FC = () => {
  const { user, signOut } = useAuth();
  const { profile, updateProfile, updatePreferences, loading } = useProfile();
  const [activeTab, setActiveTab] = useState<'profile' | 'preferences' | 'privacy'>('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    birth_date: profile?.birth_date || '',
    birth_time: profile?.birth_time || '',
    birth_location: profile?.birth_location || '',
  });

  const handleSaveProfile = async () => {
    try {
      await updateProfile(formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  const handlePreferenceChange = async (key: string, value: any) => {
    try {
      await updatePreferences({
        ...profile?.preferences,
        [key]: value,
      });
    } catch (error) {
      console.error('Failed to update preferences:', error);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: '👤' },
    { id: 'preferences', label: 'Preferences', icon: '⚙️' },
    { id: 'privacy', label: 'Privacy', icon: '🔒' },
  ];

  return (
    <>
      <Navigation />
      <div className="min-h-screen mystical-gradient pt-20 pb-20 lg:pb-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold cosmic-text mb-2">Settings</h1>
            <p className="text-purple-200">Customize your cosmic experience</p>
          </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-lg">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-6 py-3 rounded-md font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-purple-600 text-white shadow-md'
                    : 'text-gray-600 hover:text-purple-600'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="space-y-6">
            <Card variant="elevated">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-800">Birth Information</h2>
                <Button
                  variant={isEditing ? 'secondary' : 'mystical'}
                  onClick={() => setIsEditing(!isEditing)}
                >
                  {isEditing ? 'Cancel' : 'Edit'}
                </Button>
              </div>

              {isEditing ? (
                <div className="space-y-4">
                  <Input
                    label="Birth Date"
                    type="date"
                    value={formData.birth_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, birth_date: e.target.value }))}
                  />
                  <Input
                    label="Birth Time"
                    type="time"
                    value={formData.birth_time}
                    onChange={(e) => setFormData(prev => ({ ...prev, birth_time: e.target.value }))}
                  />
                  <LocationSearch
                    onLocationSelect={(location) => {
                      setFormData(prev => ({ ...prev, birth_location: location.name }));
                    }}
                    selectedLocation={formData.birth_location}
                  />
                  <div className="flex gap-3 pt-4">
                    <Button variant="mystical" onClick={handleSaveProfile} disabled={loading}>
                      Save Changes
                    </Button>
                    <Button variant="ghost" onClick={() => setIsEditing(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Birth Date</label>
                    <p className="text-gray-900">{profile?.birth_date || 'Not set'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Birth Time</label>
                    <p className="text-gray-900">{profile?.birth_time || 'Not set'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Birth Location</label>
                    <p className="text-gray-900">{profile?.birth_location || 'Not set'}</p>
                  </div>
                </div>
              )}
            </Card>

            <Card variant="elevated">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">Account Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-gray-700">Email</label>
                  <p className="text-gray-900">{user?.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Name</label>
                  <p className="text-gray-900">{user?.user_metadata?.name || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Member Since</label>
                  <p className="text-gray-900">
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Last Sign In</label>
                  <p className="text-gray-900">
                    {user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'Unknown'}
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Preferences Tab */}
        {activeTab === 'preferences' && (
          <div className="space-y-6">
            <Card variant="elevated">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">Astrology Preferences</h2>

              <div className="space-y-6">
                {/* Prediction Mode */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-3 block">
                    Interpretation Style
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[
                      { value: 'reflective', title: 'Reflective', desc: 'Modern, introspective approach' },
                      { value: 'traditional', title: 'Traditional', desc: 'Classical astrological wisdom' },
                    ].map((mode) => (
                      <label
                        key={mode.value}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          profile?.preferences?.prediction_mode === mode.value
                            ? 'border-purple-500 bg-purple-50'
                            : 'border-gray-200 hover:border-purple-300'
                        }`}
                      >
                        <input
                          type="radio"
                          name="predictionMode"
                          value={mode.value}
                          checked={profile?.preferences?.prediction_mode === mode.value}
                          onChange={(e) => handlePreferenceChange('prediction_mode', e.target.value)}
                          className="sr-only"
                        />
                        <h4 className="font-semibold text-gray-800">{mode.title}</h4>
                        <p className="text-sm text-gray-600">{mode.desc}</p>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Astrology System */}
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-3 block">
                    Astrological System
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {[
                      { value: 'western', title: 'Western', desc: 'Tropical zodiac' },
                      { value: 'vedic', title: 'Vedic', desc: 'Sidereal zodiac' },
                      { value: 'mayan', title: 'Mayan', desc: 'Tzolk\'in calendar' },
                    ].map((system) => (
                      <label
                        key={system.value}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          profile?.preferences?.astrology_system === system.value
                            ? 'border-purple-500 bg-purple-50'
                            : 'border-gray-200 hover:border-purple-300'
                        }`}
                      >
                        <input
                          type="radio"
                          name="astrologySystem"
                          value={system.value}
                          checked={profile?.preferences?.astrology_system === system.value}
                          onChange={(e) => handlePreferenceChange('astrology_system', e.target.value)}
                          className="sr-only"
                        />
                        <h4 className="font-semibold text-gray-800">{system.title}</h4>
                        <p className="text-sm text-gray-600">{system.desc}</p>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </Card>

            <Card variant="elevated">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">Notifications</h2>
              <div className="space-y-4">
                {[
                  { key: 'daily_insights', label: 'Daily Insights', desc: 'Receive personalized daily guidance' },
                  { key: 'weekly_reports', label: 'Weekly Reports', desc: 'Get comprehensive weekly forecasts' },
                  { key: 'moon_phases', label: 'Moon Phases', desc: 'Notifications for lunar events' },
                  { key: 'planet_transits', label: 'Major Transits', desc: 'Important planetary movements' },
                ].map((notification) => (
                  <label key={notification.key} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={profile?.preferences?.notifications?.[notification.key] || false}
                      onChange={(e) => handlePreferenceChange('notifications', {
                        ...profile?.preferences?.notifications,
                        [notification.key]: e.target.checked,
                      })}
                      className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <div>
                      <div className="font-medium text-gray-800">{notification.label}</div>
                      <div className="text-sm text-gray-600">{notification.desc}</div>
                    </div>
                  </label>
                ))}
              </div>
            </Card>
          </div>
        )}

        {/* Privacy Tab */}
        {activeTab === 'privacy' && (
          <div className="space-y-6">
            <Card variant="elevated">
              <h2 className="text-xl font-semibold text-gray-800 mb-6">Privacy & Security</h2>

              <div className="space-y-6">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <h3 className="font-semibold text-blue-800 mb-2">🔒 Your Data is Secure</h3>
                  <p className="text-blue-700 text-sm">
                    Your birth data and personal information are encrypted and stored securely.
                    We never share your data with third parties.
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-4">Data Management</h3>
                  <div className="space-y-3">
                    <Button variant="secondary" className="w-full justify-start">
                      📥 Export My Data
                    </Button>
                    <Button variant="secondary" className="w-full justify-start">
                      🗑️ Delete Account
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-800 mb-4">Session Management</h3>
                  <Button variant="outline" onClick={signOut} className="w-full">
                    🚪 Sign Out
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}
        </div>
      </div>
    </>
  );
};
