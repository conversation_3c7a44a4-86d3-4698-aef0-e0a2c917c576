import React, { useState } from 'react';
import { Card, Button, Input } from '@taraka/ui';

interface GlossaryItem {
  term: string;
  category: 'planets' | 'signs' | 'houses' | 'aspects' | 'concepts';
  definition: string;
  symbol?: string;
  keywords: string[];
  relatedTerms: string[];
}

const GLOSSARY_DATA: GlossaryItem[] = [
  {
    term: 'Sun',
    category: 'planets',
    symbol: '☉',
    definition: 'Represents your core identity, ego, and life force. The Sun shows how you express your essential self and what drives your sense of purpose.',
    keywords: ['identity', 'ego', 'vitality', 'leadership', 'self-expression'],
    relatedTerms: ['Leo', 'Fifth House', 'Solar Return'],
  },
  {
    term: 'Moon',
    category: 'planets',
    symbol: '☽',
    definition: 'Governs your emotional nature, instincts, and subconscious patterns. The Moon reveals your inner world and how you process feelings.',
    keywords: ['emotions', 'intuition', 'subconscious', 'nurturing', 'habits'],
    relatedTerms: ['Cancer', 'Fourth House', 'Lunar Phases'],
  },
  {
    term: 'Ascendant',
    category: 'concepts',
    definition: 'Also called the Rising Sign, this is the zodiac sign that was rising on the eastern horizon at the time of your birth. It represents your outer personality and how others perceive you.',
    keywords: ['rising sign', 'first impression', 'personality', 'appearance'],
    relatedTerms: ['First House', 'Descendant', 'Midheaven'],
  },
  {
    term: 'Aries',
    category: 'signs',
    symbol: '♈',
    definition: 'The first sign of the zodiac, ruled by Mars. Aries energy is pioneering, bold, and action-oriented.',
    keywords: ['leadership', 'courage', 'initiative', 'independence', 'energy'],
    relatedTerms: ['Mars', 'First House', 'Cardinal Signs'],
  },
  {
    term: 'Conjunction',
    category: 'aspects',
    definition: 'When two planets are in the same degree or very close together (0-8 degrees apart). This creates a blending of energies.',
    keywords: ['unity', 'blending', 'intensity', 'focus'],
    relatedTerms: ['Opposition', 'Orb', 'Planetary Aspects'],
  },
  {
    term: 'First House',
    category: 'houses',
    definition: 'The house of self, identity, and first impressions. It represents how you present yourself to the world.',
    keywords: ['self', 'identity', 'appearance', 'first impressions'],
    relatedTerms: ['Ascendant', 'Aries', 'Angular Houses'],
  },
];

const CATEGORIES = [
  { id: 'all', label: 'All', icon: '🌟' },
  { id: 'planets', label: 'Planets', icon: '🪐' },
  { id: 'signs', label: 'Zodiac Signs', icon: '♈' },
  { id: 'houses', label: 'Houses', icon: '🏠' },
  { id: 'aspects', label: 'Aspects', icon: '🔗' },
  { id: 'concepts', label: 'Concepts', icon: '💭' },
];

const LEARNING_MODULES = [
  {
    title: 'Astrology Basics',
    description: 'Learn the fundamental concepts of astrology',
    lessons: ['What is Astrology?', 'The Zodiac Wheel', 'Planets and Their Meanings'],
    duration: '30 min',
    difficulty: 'Beginner',
  },
  {
    title: 'Understanding Your Chart',
    description: 'Dive deep into natal chart interpretation',
    lessons: ['Reading Chart Symbols', 'Planet-Sign Combinations', 'House Meanings'],
    duration: '45 min',
    difficulty: 'Intermediate',
  },
  {
    title: 'Planetary Aspects',
    description: 'Master the relationships between planets',
    lessons: ['Major Aspects', 'Orbs and Influence', 'Aspect Patterns'],
    duration: '60 min',
    difficulty: 'Advanced',
  },
];

export const Learn: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTerm, setSelectedTerm] = useState<GlossaryItem | null>(null);

  const filteredGlossary = GLOSSARY_DATA.filter(item => {
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    const matchesSearch = item.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.definition.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="min-h-screen mystical-gradient py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold cosmic-text mb-2">🎓 Astrology Learning Center</h1>
          <p className="text-gray-600">Expand your cosmic knowledge and understanding</p>
        </div>

        {/* Learning Modules */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">📚 Learning Modules</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {LEARNING_MODULES.map((module, index) => (
              <Card key={index} variant="elevated" className="hover:shadow-lg transition-shadow">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">{module.title}</h3>
                  <p className="text-gray-600 text-sm mb-3">{module.description}</p>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>⏱️ {module.duration}</span>
                    <span className={`px-2 py-1 rounded-full ${
                      module.difficulty === 'Beginner' ? 'bg-green-100 text-green-700' :
                      module.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-red-100 text-red-700'
                    }`}>
                      {module.difficulty}
                    </span>
                  </div>
                </div>
                <div className="mb-4">
                  <h4 className="font-medium text-gray-700 mb-2">Lessons:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {module.lessons.map((lesson, i) => (
                      <li key={i} className="flex items-center">
                        <span className="w-2 h-2 bg-purple-400 rounded-full mr-2"></span>
                        {lesson}
                      </li>
                    ))}
                  </ul>
                </div>
                <Button variant="mystical" className="w-full">
                  Start Learning
                </Button>
              </Card>
            ))}
          </div>
        </div>

        {/* Glossary Section */}
        <div>
          <h2 className="text-2xl font-semibold text-gray-800 mb-6">📖 Astrology Glossary</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Search and Filters */}
            <div className="lg:col-span-1">
              <Card variant="elevated" className="sticky top-8">
                <div className="space-y-4">
                  <Input
                    label="Search Terms"
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search glossary..."
                  />
                  
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-3 block">Categories</label>
                    <div className="space-y-2">
                      {CATEGORIES.map(category => (
                        <button
                          key={category.id}
                          onClick={() => setSelectedCategory(category.id)}
                          className={`w-full text-left px-3 py-2 rounded-lg transition-all ${
                            selectedCategory === category.id
                              ? 'bg-purple-600 text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-purple-100'
                          }`}
                        >
                          <span className="mr-2">{category.icon}</span>
                          {category.label}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Glossary Content */}
            <div className="lg:col-span-2">
              {selectedTerm ? (
                <Card variant="elevated">
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-3">
                      {selectedTerm.symbol && (
                        <span className="text-3xl">{selectedTerm.symbol}</span>
                      )}
                      <h3 className="text-2xl font-bold text-gray-800">{selectedTerm.term}</h3>
                    </div>
                    <Button variant="ghost" onClick={() => setSelectedTerm(null)}>
                      ✕
                    </Button>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Definition</h4>
                      <p className="text-gray-700 leading-relaxed">{selectedTerm.definition}</p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">Keywords</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedTerm.keywords.map((keyword, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm"
                          >
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>

                    {selectedTerm.relatedTerms.length > 0 && (
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-2">Related Terms</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedTerm.relatedTerms.map((term, index) => (
                            <button
                              key={index}
                              onClick={() => {
                                const relatedItem = GLOSSARY_DATA.find(item => item.term === term);
                                if (relatedItem) setSelectedTerm(relatedItem);
                              }}
                              className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm hover:bg-blue-200 transition-colors"
                            >
                              {term}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
              ) : (
                <div className="space-y-4">
                  {filteredGlossary.map((item, index) => (
                    <Card
                      key={index}
                      variant="elevated"
                      className="cursor-pointer hover:shadow-lg transition-shadow"
                      onClick={() => setSelectedTerm(item)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          {item.symbol && (
                            <span className="text-2xl">{item.symbol}</span>
                          )}
                          <div>
                            <h3 className="text-lg font-semibold text-gray-800">{item.term}</h3>
                            <p className="text-gray-600 text-sm line-clamp-2">{item.definition}</p>
                          </div>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          item.category === 'planets' ? 'bg-blue-100 text-blue-700' :
                          item.category === 'signs' ? 'bg-green-100 text-green-700' :
                          item.category === 'houses' ? 'bg-yellow-100 text-yellow-700' :
                          item.category === 'aspects' ? 'bg-purple-100 text-purple-700' :
                          'bg-gray-100 text-gray-700'
                        }`}>
                          {item.category}
                        </span>
                      </div>
                    </Card>
                  ))}

                  {filteredGlossary.length === 0 && (
                    <div className="text-center py-12">
                      <div className="text-4xl mb-4">🔍</div>
                      <p className="text-gray-600">No terms found matching your search.</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
