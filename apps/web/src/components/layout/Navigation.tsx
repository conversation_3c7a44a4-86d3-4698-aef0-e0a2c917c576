import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@taraka/ui';
import { useAuth } from '../../hooks/useAuth';

interface NavItem {
  path: string;
  label: string;
  icon: string;
  description: string;
}

const NAV_ITEMS: NavItem[] = [
  { path: '/', label: 'Dashboard', icon: '🏠', description: 'Your cosmic overview' },
  { path: '/journal', label: 'Journal', icon: '📝', description: 'Track your cosmic journey' },
  { path: '/learn', label: 'Learn', icon: '🎓', description: 'Expand your knowledge' },
  { path: '/settings', label: 'Settings', icon: '⚙️', description: 'Customize your experience' },
];

export const Navigation: React.FC = () => {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  if (!user) return null;

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden lg:flex fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 w-full">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <div className="text-2xl">🌌</div>
              <span className="text-xl font-bold cosmic-text">Taraka</span>
            </Link>

            {/* Navigation Items */}
            <div className="flex items-center space-x-1">
              {NAV_ITEMS.map((item) => {
                const isActive = location.pathname === item.path;
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`px-4 py-2 rounded-lg font-medium transition-all group relative ${
                      isActive
                        ? 'bg-purple-100 text-purple-700'
                        : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
                    }`}
                  >
                    <span className="mr-2">{item.icon}</span>
                    {item.label}
                    
                    {/* Tooltip */}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                      {item.description}
                    </div>
                  </Link>
                );
              })}
            </div>

            {/* User Menu */}
            <div className="flex items-center space-x-3">
              <div className="text-sm text-gray-600">
                {user.user_metadata?.name || user.email?.split('@')[0]}
              </div>
              <Button variant="ghost" size="sm" onClick={signOut}>
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        {/* Mobile Header */}
        <header className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200">
          <div className="flex items-center justify-between h-16 px-4">
            <Link to="/" className="flex items-center space-x-2">
              <div className="text-2xl">🌌</div>
              <span className="text-xl font-bold cosmic-text">Taraka</span>
            </Link>
            
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-lg text-gray-600 hover:text-purple-600 hover:bg-purple-50"
            >
              {isMobileMenuOpen ? '✕' : '☰'}
            </button>
          </div>
        </header>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-40 bg-black/50" onClick={() => setIsMobileMenuOpen(false)} />
        )}

        {/* Mobile Menu */}
        <div className={`fixed top-16 right-0 bottom-0 w-80 bg-white z-50 transform transition-transform ${
          isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
        }`}>
          <div className="p-6 space-y-6">
            {/* User Info */}
            <div className="pb-6 border-b border-gray-200">
              <div className="text-lg font-semibold text-gray-800">
                {user.user_metadata?.name || 'Cosmic Explorer'}
              </div>
              <div className="text-sm text-gray-600">{user.email}</div>
            </div>

            {/* Navigation Items */}
            <div className="space-y-2">
              {NAV_ITEMS.map((item) => {
                const isActive = location.pathname === item.path;
                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className={`block p-4 rounded-lg transition-all ${
                      isActive
                        ? 'bg-purple-100 text-purple-700'
                        : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-xl">{item.icon}</span>
                      <div>
                        <div className="font-medium">{item.label}</div>
                        <div className="text-sm opacity-75">{item.description}</div>
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>

            {/* Sign Out */}
            <div className="pt-6 border-t border-gray-200">
              <Button variant="outline" onClick={signOut} className="w-full">
                🚪 Sign Out
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Navigation for Mobile */}
        <nav className="fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 lg:hidden">
          <div className="grid grid-cols-4 h-16">
            {NAV_ITEMS.map((item) => {
              const isActive = location.pathname === item.path;
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex flex-col items-center justify-center space-y-1 ${
                    isActive ? 'text-purple-600' : 'text-gray-600'
                  }`}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span className="text-xs font-medium">{item.label}</span>
                </Link>
              );
            })}
          </div>
        </nav>
      </div>

      {/* Spacer for fixed navigation */}
      <div className="h-16 lg:h-16" />
      <div className="h-16 lg:hidden" /> {/* Bottom nav spacer for mobile */}
    </>
  );
};
