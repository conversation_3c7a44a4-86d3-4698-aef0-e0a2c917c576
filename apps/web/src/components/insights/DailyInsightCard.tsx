import React, { useState } from 'react';
import { Card, Button } from '@taraka/ui';
import { DailyInsight } from '@taraka/shared';
import { format } from 'date-fns';

interface DailyInsightCardProps {
  insight: DailyInsight;
  onSaveReflection?: (reflection: string) => void;
}

export const DailyInsightCard: React.FC<DailyInsightCardProps> = ({
  insight,
  onSaveReflection,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [reflection, setReflection] = useState('');
  const [showReflectionInput, setShowReflectionInput] = useState(false);

  const getThemeEmoji = (theme: string): string => {
    const emojis: Record<string, string> = {
      love: '💕',
      career: '🎯',
      emotions: '🌙',
      growth: '🌱',
      creativity: '🎨',
      challenge: '⚡',
    };
    return emojis[theme] || '✨';
  };

  const getThemeColor = (theme: string): string => {
    const colors: Record<string, string> = {
      love: 'bg-pink-100 text-pink-700',
      career: 'bg-blue-100 text-blue-700',
      emotions: 'bg-purple-100 text-purple-700',
      growth: 'bg-green-100 text-green-700',
      creativity: 'bg-orange-100 text-orange-700',
      challenge: 'bg-red-100 text-red-700',
    };
    return colors[theme] || 'bg-gray-100 text-gray-700';
  };

  const getInfluenceColor = (influence: string): string => {
    const colors: Record<string, string> = {
      positive: 'text-green-600',
      challenging: 'text-red-600',
      neutral: 'text-blue-600',
    };
    return colors[influence] || 'text-gray-600';
  };

  const handleSaveReflection = () => {
    if (reflection.trim() && onSaveReflection) {
      onSaveReflection(reflection);
      setShowReflectionInput(false);
      setReflection('');
    }
  };

  return (
    <Card variant="mystical" className="w-full max-w-2xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-bold text-gray-800">{insight.content.title}</h2>
          <p className="text-sm text-gray-600">
            {format(insight.date, 'EEEE, MMMM d, yyyy')} • {insight.mode} • {insight.system}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {insight.themes.map((theme, index) => (
            <span
              key={index}
              className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getThemeColor(theme)}`}
            >
              {getThemeEmoji(theme)}
              {theme}
            </span>
          ))}
        </div>
      </div>

      {/* Summary */}
      <div className="mb-4">
        <p className="text-gray-700 leading-relaxed">{insight.content.summary}</p>
      </div>

      {/* Expand/Collapse Button */}
      <div className="mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="text-purple-600 hover:text-purple-700"
        >
          {showDetails ? 'Show Less' : 'Read More'} {showDetails ? '▲' : '▼'}
        </Button>
      </div>

      {/* Detailed Content */}
      {showDetails && (
        <div className="space-y-6">
          {/* Detailed Description */}
          <div>
            <h3 className="font-semibold text-gray-800 mb-2">Detailed Insight</h3>
            <p className="text-gray-700 leading-relaxed">{insight.content.detailed}</p>
          </div>

          {/* Reflection Prompt (Reflective Mode Only) */}
          {insight.mode === 'reflective' && insight.content.reflection_prompt && (
            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
              <h3 className="font-semibold text-purple-800 mb-2">💭 Reflection Prompt</h3>
              <p className="text-purple-700 mb-3">{insight.content.reflection_prompt}</p>
              
              {!showReflectionInput ? (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setShowReflectionInput(true)}
                >
                  Write Reflection
                </Button>
              ) : (
                <div className="space-y-3">
                  <textarea
                    value={reflection}
                    onChange={(e) => setReflection(e.target.value)}
                    placeholder="Share your thoughts and reflections..."
                    className="w-full p-3 border border-purple-300 rounded-md resize-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    rows={4}
                  />
                  <div className="flex gap-2">
                    <Button
                      variant="mystical"
                      size="sm"
                      onClick={handleSaveReflection}
                      disabled={!reflection.trim()}
                    >
                      Save Reflection
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setShowReflectionInput(false);
                        setReflection('');
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Active Transits */}
          {insight.transits.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-800 mb-3">🌟 Active Cosmic Influences</h3>
              <div className="space-y-2">
                {insight.transits.slice(0, 5).map((transit, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-sm font-medium text-gray-800">
                        {transit.planet} {transit.aspect} {transit.target}
                      </div>
                      {transit.exact && (
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">
                          Exact
                        </span>
                      )}
                    </div>
                    <div className={`text-sm font-medium ${getInfluenceColor(transit.influence)}`}>
                      {transit.influence}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Cosmic Advice */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-800 mb-2">🌌 Cosmic Guidance</h3>
            <div className="text-blue-700 text-sm space-y-2">
              {insight.mode === 'reflective' ? (
                <div>
                  <p>• Take time for introspection and self-awareness today</p>
                  <p>• Trust your intuition and inner wisdom</p>
                  <p>• Practice self-compassion in all your endeavors</p>
                </div>
              ) : (
                <div>
                  <p>• Pay attention to the planetary influences affecting you</p>
                  <p>• Use this cosmic energy to your advantage</p>
                  <p>• Consider timing for important decisions</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          Generated on {format(insight.createdAt, 'MMM d, yyyy \'at\' h:mm a')}
        </p>
      </div>
    </Card>
  );
};
