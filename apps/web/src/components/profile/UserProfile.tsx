import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@taraka/ui';
import { useAuth } from '../../hooks/useAuth';
import { useProfile } from '../../hooks/useProfile';

export const UserProfile: React.FC = () => {
  const { user, signOut } = useAuth();
  const { profile, loading } = useProfile();

  const handleSignOut = async () => {
    await signOut();
  };

  if (loading) {
    return (
      <Card className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/3"></div>
      </Card>
    );
  }

  return (
    <Card variant="mystical">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Profile</h3>
        <Button variant="ghost" size="sm" onClick={handleSignOut}>
          Sign Out
        </Button>
      </div>
      
      <div className="space-y-2 text-sm">
        <div>
          <span className="font-medium">Email:</span> {user?.email}
        </div>
        
        {profile?.name && (
          <div>
            <span className="font-medium">Name:</span> {profile.name}
          </div>
        )}
        
        {profile?.birth_date && (
          <div>
            <span className="font-medium">Birth Date:</span> {profile.birth_date}
          </div>
        )}
        
        {profile?.birth_location && (
          <div>
            <span className="font-medium">Birth Location:</span> {profile.birth_location}
          </div>
        )}
        
        <div className="pt-2">
          <span className="font-medium">Onboarding:</span>{' '}
          <span className={profile?.onboarding_completed ? 'text-green-600' : 'text-orange-600'}>
            {profile?.onboarding_completed ? 'Completed' : 'Pending'}
          </span>
        </div>
      </div>
    </Card>
  );
};
