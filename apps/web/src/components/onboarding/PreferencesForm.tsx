import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@taraka/ui';
import { useProfile } from '../../hooks/useProfile';
import { DEFAULT_PREFERENCES } from '@taraka/shared';

interface PreferencesFormProps {
  onComplete: () => void;
}

export const PreferencesForm: React.FC<PreferencesFormProps> = ({ onComplete }) => {
  const { updatePreferences, loading } = useProfile();
  const [preferences, setPreferences] = useState(DEFAULT_PREFERENCES);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await updatePreferences(preferences);
      onComplete();
    } catch (error) {
      console.error('Failed to save preferences:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen mystical-gradient flex items-center justify-center px-4">
      <Card variant="elevated" className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold cosmic-text mb-2">
            🎯 Customize Your Experience
          </h1>
          <p className="text-gray-600">
            Choose how you'd like to explore your cosmic insights
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Prediction Mode */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              🔮 Interpretation Style
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                preferences.predictionMode === 'reflective' 
                  ? 'border-purple-500 bg-purple-50' 
                  : 'border-gray-200 hover:border-purple-300'
              }`}>
                <input
                  type="radio"
                  name="predictionMode"
                  value="reflective"
                  checked={preferences.predictionMode === 'reflective'}
                  onChange={(e) => setPreferences(prev => ({ 
                    ...prev, 
                    predictionMode: e.target.value as 'reflective' | 'traditional' 
                  }))}
                  className="sr-only"
                />
                <div className="text-center">
                  <div className="text-2xl mb-2">🌙</div>
                  <h4 className="font-semibold text-gray-800 mb-1">Reflective</h4>
                  <p className="text-sm text-gray-600">
                    Introspective, modern interpretations focused on personal growth and self-discovery
                  </p>
                </div>
              </label>

              <label className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                preferences.predictionMode === 'traditional' 
                  ? 'border-purple-500 bg-purple-50' 
                  : 'border-gray-200 hover:border-purple-300'
              }`}>
                <input
                  type="radio"
                  name="predictionMode"
                  value="traditional"
                  checked={preferences.predictionMode === 'traditional'}
                  onChange={(e) => setPreferences(prev => ({ 
                    ...prev, 
                    predictionMode: e.target.value as 'reflective' | 'traditional' 
                  }))}
                  className="sr-only"
                />
                <div className="text-center">
                  <div className="text-2xl mb-2">⭐</div>
                  <h4 className="font-semibold text-gray-800 mb-1">Traditional</h4>
                  <p className="text-sm text-gray-600">
                    Classical astrological interpretations based on ancient wisdom and established meanings
                  </p>
                </div>
              </label>
            </div>
          </div>

          {/* Astrology System */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              🌍 Astrological System
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { 
                  value: 'western', 
                  emoji: '♈', 
                  title: 'Western (Tropical)', 
                  description: 'Based on the seasons and the relationship between Earth and Sun' 
                },
                { 
                  value: 'vedic', 
                  emoji: '🕉️', 
                  title: 'Vedic (Sidereal)', 
                  description: 'Ancient Indian system based on fixed star positions' 
                },
                { 
                  value: 'mayan', 
                  emoji: '🌞', 
                  title: 'Mayan (Tzolk\'in)', 
                  description: 'Sacred 260-day calendar system from Mesoamerican tradition' 
                },
              ].map((system) => (
                <label key={system.value} className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  preferences.astrologySystem === system.value 
                    ? 'border-purple-500 bg-purple-50' 
                    : 'border-gray-200 hover:border-purple-300'
                }`}>
                  <input
                    type="radio"
                    name="astrologySystem"
                    value={system.value}
                    checked={preferences.astrologySystem === system.value}
                    onChange={(e) => setPreferences(prev => ({ 
                      ...prev, 
                      astrologySystem: e.target.value as 'western' | 'vedic' | 'mayan' 
                    }))}
                    className="sr-only"
                  />
                  <div className="text-center">
                    <div className="text-2xl mb-2">{system.emoji}</div>
                    <h4 className="font-semibold text-gray-800 mb-1">{system.title}</h4>
                    <p className="text-xs text-gray-600">{system.description}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Notifications */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              🔔 Cosmic Notifications
            </h3>
            <div className="space-y-3">
              {[
                { key: 'dailyInsights', label: 'Daily Insights', description: 'Receive personalized daily astrological guidance' },
                { key: 'weeklyReports', label: 'Weekly Reports', description: 'Get comprehensive weekly cosmic forecasts' },
                { key: 'moonPhases', label: 'Moon Phases', description: 'Notifications for new moons, full moons, and lunar events' },
              ].map((notification) => (
                <label key={notification.key} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={preferences.notifications[notification.key as keyof typeof preferences.notifications]}
                    onChange={(e) => setPreferences(prev => ({
                      ...prev,
                      notifications: {
                        ...prev.notifications,
                        [notification.key]: e.target.checked,
                      },
                    }))}
                    className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <div>
                    <div className="font-medium text-gray-800">{notification.label}</div>
                    <div className="text-sm text-gray-600">{notification.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <Button
            type="submit"
            variant="mystical"
            className="w-full"
            disabled={isSubmitting || loading}
          >
            {isSubmitting ? 'Saving Preferences...' : 'Complete Setup ✨'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            You can change these preferences anytime in your profile settings
          </p>
        </div>
      </Card>
    </div>
  );
};
