import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Input } from '@taraka/ui';
import { useProfile } from '../../hooks/useProfile';
import { LocationSearch } from './LocationSearch';

interface BirthDataFormProps {
  onComplete: () => void;
}

interface BirthFormData {
  birthDate: string;
  birthTime: string;
  birthLocation: string;
  coordinates: { lat: number; lng: number } | null;
  timezone: string;
}

export const BirthDataForm: React.FC<BirthDataFormProps> = ({ onComplete }) => {
  const { updateBirthData, loading } = useProfile();
  const [formData, setFormData] = useState<BirthFormData>({
    birthDate: '',
    birthTime: '',
    birthLocation: '',
    coordinates: null,
    timezone: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.birthDate) {
      newErrors.birthDate = 'Birth date is required';
    }

    if (!formData.birthTime) {
      newErrors.birthTime = 'Birth time is required for accurate charts';
    }

    if (!formData.birthLocation) {
      newErrors.birthLocation = 'Birth location is required';
    }

    if (!formData.coordinates) {
      newErrors.coordinates = 'Please select a location from the search results';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await updateBirthData({
        birth_date: formData.birthDate,
        birth_time: formData.birthTime,
        birth_location: formData.birthLocation,
        birth_coordinates: formData.coordinates!,
        timezone: formData.timezone || 'UTC',
      });

      onComplete();
    } catch (error) {
      console.error('Failed to save birth data:', error);
      setErrors({ submit: 'Failed to save birth data. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLocationSelect = (location: {
    name: string;
    coordinates: { lat: number; lng: number };
    timezone: string;
  }) => {
    setFormData(prev => ({
      ...prev,
      birthLocation: location.name,
      coordinates: location.coordinates,
      timezone: location.timezone,
    }));
    setErrors(prev => ({ ...prev, birthLocation: '', coordinates: '' }));
  };

  return (
    <div className="min-h-screen mystical-gradient flex items-center justify-center px-4">
      <Card variant="elevated" className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold cosmic-text mb-2">
            ✨ Your Cosmic Blueprint
          </h1>
          <p className="text-gray-600">
            Enter your birth details to generate your personalized natal chart
          </p>
        </div>

        {errors.submit && (
          <div className="bg-red-50 text-red-700 p-3 rounded-md mb-6 text-sm border border-red-200">
            {errors.submit}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Birth Date"
              type="date"
              value={formData.birthDate}
              onChange={(e) => setFormData(prev => ({ ...prev, birthDate: e.target.value }))}
              error={errors.birthDate}
              required
            />

            <Input
              label="Birth Time"
              type="time"
              value={formData.birthTime}
              onChange={(e) => setFormData(prev => ({ ...prev, birthTime: e.target.value }))}
              error={errors.birthTime}
              required
            />
          </div>

          <LocationSearch
            onLocationSelect={handleLocationSelect}
            error={errors.birthLocation || errors.coordinates}
            selectedLocation={formData.birthLocation}
          />

          {formData.coordinates && (
            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
              <h4 className="font-medium text-purple-800 mb-2">Selected Location:</h4>
              <p className="text-sm text-purple-700">
                📍 {formData.birthLocation}
              </p>
              <p className="text-xs text-purple-600 mt-1">
                Coordinates: {formData.coordinates.lat.toFixed(4)}, {formData.coordinates.lng.toFixed(4)}
              </p>
              {formData.timezone && (
                <p className="text-xs text-purple-600">
                  Timezone: {formData.timezone}
                </p>
              )}
            </div>
          )}

          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-800 mb-2">💡 Why do we need this?</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Date & Time:</strong> Determines planetary positions at your birth</li>
              <li>• <strong>Location:</strong> Calculates your rising sign and house positions</li>
              <li>• <strong>Accuracy:</strong> Even a few minutes can change your chart significantly</li>
            </ul>
          </div>

          <Button
            type="submit"
            variant="mystical"
            className="w-full"
            disabled={isSubmitting || loading}
          >
            {isSubmitting ? 'Creating Your Chart...' : 'Generate My Natal Chart ✨'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Your birth data is private and secure. We use it only to calculate your astrological charts.
          </p>
        </div>
      </Card>
    </div>
  );
};
