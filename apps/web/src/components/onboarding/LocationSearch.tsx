import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@taraka/ui';

interface LocationResult {
  name: string;
  coordinates: { lat: number; lng: number };
  timezone: string;
  country: string;
  region?: string;
}

interface LocationSearchProps {
  onLocationSelect: (location: LocationResult) => void;
  error?: string;
  selectedLocation?: string;
}

export const LocationSearch: React.FC<LocationSearchProps> = ({
  onLocationSelect,
  error,
  selectedLocation,
}) => {
  const [query, setQuery] = useState(selectedLocation || '');
  const [results, setResults] = useState<LocationResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const resultsRef = useRef<HTMLDivElement>(null);

  // Real geocoding using OpenStreetMap Nominatim API (free)
  const searchLocations = async (searchQuery: string): Promise<LocationResult[]> => {
    if (!searchQuery || searchQuery.length < 2) return [];

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery)}&limit=8&addressdetails=1&extratags=1`
      );

      if (!response.ok) throw new Error('Search failed');

      const data = await response.json();

      return data.map((item: any) => {
        // Extract location components
        const address = item.address || {};
        const city = address.city || address.town || address.village || address.municipality;
        const state = address.state || address.province || address.region;
        const country = address.country;

        // Build display name
        let displayName = '';
        if (city) displayName += city;
        if (state && state !== city) displayName += (displayName ? ', ' : '') + state;
        if (country) displayName += (displayName ? ', ' : '') + country;

        // Fallback to OSM display name if we can't build a good one
        if (!displayName) displayName = item.display_name;

        // Estimate timezone based on coordinates (simplified)
        const timezone = getTimezoneFromCoordinates(parseFloat(item.lat), parseFloat(item.lon));

        return {
          name: displayName,
          coordinates: {
            lat: parseFloat(item.lat),
            lng: parseFloat(item.lon)
          },
          timezone,
          country: country || 'Unknown',
          region: state,
        };
      }).filter(location => location.name && location.coordinates.lat && location.coordinates.lng);

    } catch (error) {
      console.error('Location search error:', error);
      return [];
    }
  };

  // Simple timezone estimation based on longitude
  const getTimezoneFromCoordinates = (lat: number, lng: number): string => {
    // This is a simplified approach - in production you'd use a proper timezone API
    const timezoneMap: { [key: string]: string } = {
      // Major timezone mappings based on longitude ranges
      'America/New_York': 'UTC-5',
      'America/Chicago': 'UTC-6',
      'America/Denver': 'UTC-7',
      'America/Los_Angeles': 'UTC-8',
      'Europe/London': 'UTC+0',
      'Europe/Paris': 'UTC+1',
      'Europe/Moscow': 'UTC+3',
      'Asia/Dubai': 'UTC+4',
      'Asia/Kolkata': 'UTC+5:30',
      'Asia/Dhaka': 'UTC+6',
      'Asia/Bangkok': 'UTC+7',
      'Asia/Shanghai': 'UTC+8',
      'Asia/Tokyo': 'UTC+9',
      'Australia/Sydney': 'UTC+10',
    };

    // Rough timezone estimation based on longitude
    if (lng >= -180 && lng < -150) return 'Pacific/Honolulu';
    if (lng >= -150 && lng < -120) return 'America/Anchorage';
    if (lng >= -120 && lng < -105) return 'America/Los_Angeles';
    if (lng >= -105 && lng < -90) return 'America/Denver';
    if (lng >= -90 && lng < -75) return 'America/Chicago';
    if (lng >= -75 && lng < -60) return 'America/New_York';
    if (lng >= -60 && lng < -30) return 'America/Sao_Paulo';
    if (lng >= -30 && lng < 15) return 'Europe/London';
    if (lng >= 15 && lng < 45) return 'Europe/Paris';
    if (lng >= 45 && lng < 75) return 'Asia/Dubai';
    if (lng >= 75 && lng < 90) return 'Asia/Kolkata';
    if (lng >= 90 && lng < 105) return 'Asia/Dhaka';
    if (lng >= 105 && lng < 120) return 'Asia/Bangkok';
    if (lng >= 120 && lng < 135) return 'Asia/Shanghai';
    if (lng >= 135 && lng < 150) return 'Asia/Tokyo';
    if (lng >= 150 && lng <= 180) return 'Australia/Sydney';

    return 'UTC';
  };

  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (query.length >= 2) {
      setIsSearching(true);
      searchTimeoutRef.current = setTimeout(async () => {
        try {
          const searchResults = await searchLocations(query);
          setResults(searchResults);
          setIsSearching(false);
          setShowResults(true);
        } catch (error) {
          console.error('Search error:', error);
          setResults([]);
          setIsSearching(false);
          setShowResults(false);
        }
      }, 500); // Increased delay to be respectful to the API
    } else {
      setResults([]);
      setShowResults(false);
      setIsSearching(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query]);

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (resultsRef.current && !resultsRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLocationClick = (location: LocationResult) => {
    setQuery(location.name);
    setShowResults(false);
    onLocationSelect(location);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    if (e.target.value !== selectedLocation) {
      // Clear selection if user types something different
      setShowResults(true);
    }
  };

  return (
    <div className="relative" ref={resultsRef}>
      <Input
        label="Birth Location"
        type="text"
        value={query}
        onChange={handleInputChange}
        placeholder="Search for your birth city..."
        error={error}
        required
      />

      {isSearching && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 p-3">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
            <span className="ml-2 text-sm text-gray-600">Searching...</span>
          </div>
        </div>
      )}

      {showResults && results.length > 0 && !isSearching && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
          {results.map((location, index) => (
            <button
              key={index}
              type="button"
              className="w-full text-left px-4 py-3 hover:bg-purple-50 border-b border-gray-100 last:border-b-0 transition-colors"
              onClick={() => handleLocationClick(location)}
            >
              <div className="font-medium text-gray-900">{location.name}</div>
              <div className="text-sm text-gray-500">
                {location.country} • {location.timezone}
              </div>
            </button>
          ))}
        </div>
      )}

      {showResults && results.length === 0 && !isSearching && query.length >= 2 && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 p-4">
          <div className="text-center text-gray-500">
            <p className="text-sm">No locations found for "{query}"</p>
            <p className="text-xs mt-1">Try searching for a major city near your birth location</p>
          </div>
        </div>
      )}
    </div>
  );
};
