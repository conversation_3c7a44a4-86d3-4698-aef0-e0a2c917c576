import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@taraka/ui';

interface LocationResult {
  name: string;
  coordinates: { lat: number; lng: number };
  timezone: string;
  country: string;
  region?: string;
}

interface LocationSearchProps {
  onLocationSelect: (location: LocationResult) => void;
  error?: string;
  selectedLocation?: string;
}

export const LocationSearch: React.FC<LocationSearchProps> = ({
  onLocationSelect,
  error,
  selectedLocation,
}) => {
  const [query, setQuery] = useState(selectedLocation || '');
  const [results, setResults] = useState<LocationResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const resultsRef = useRef<HTMLDivElement>(null);

  // Mock location data - in production, you'd use a real geocoding API
  const mockLocations: LocationResult[] = [
    {
      name: 'New York, NY, USA',
      coordinates: { lat: 40.7128, lng: -74.0060 },
      timezone: 'America/New_York',
      country: 'United States',
      region: 'New York',
    },
    {
      name: 'Los Angeles, CA, USA',
      coordinates: { lat: 34.0522, lng: -118.2437 },
      timezone: 'America/Los_Angeles',
      country: 'United States',
      region: 'California',
    },
    {
      name: 'London, England, UK',
      coordinates: { lat: 51.5074, lng: -0.1278 },
      timezone: 'Europe/London',
      country: 'United Kingdom',
      region: 'England',
    },
    {
      name: 'Paris, France',
      coordinates: { lat: 48.8566, lng: 2.3522 },
      timezone: 'Europe/Paris',
      country: 'France',
    },
    {
      name: 'Tokyo, Japan',
      coordinates: { lat: 35.6762, lng: 139.6503 },
      timezone: 'Asia/Tokyo',
      country: 'Japan',
    },
    {
      name: 'Sydney, Australia',
      coordinates: { lat: -33.8688, lng: 151.2093 },
      timezone: 'Australia/Sydney',
      country: 'Australia',
    },
    {
      name: 'Mumbai, India',
      coordinates: { lat: 19.0760, lng: 72.8777 },
      timezone: 'Asia/Kolkata',
      country: 'India',
    },
    {
      name: 'São Paulo, Brazil',
      coordinates: { lat: -23.5505, lng: -46.6333 },
      timezone: 'America/Sao_Paulo',
      country: 'Brazil',
    },
    {
      name: 'Cairo, Egypt',
      coordinates: { lat: 30.0444, lng: 31.2357 },
      timezone: 'Africa/Cairo',
      country: 'Egypt',
    },
    {
      name: 'Vancouver, BC, Canada',
      coordinates: { lat: 49.2827, lng: -123.1207 },
      timezone: 'America/Vancouver',
      country: 'Canada',
      region: 'British Columbia',
    },
  ];

  const searchLocations = (searchQuery: string): LocationResult[] => {
    if (!searchQuery || searchQuery.length < 2) return [];
    
    const lowercaseQuery = searchQuery.toLowerCase();
    return mockLocations.filter(location =>
      location.name.toLowerCase().includes(lowercaseQuery) ||
      location.country.toLowerCase().includes(lowercaseQuery) ||
      location.region?.toLowerCase().includes(lowercaseQuery)
    ).slice(0, 5); // Limit to 5 results
  };

  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (query.length >= 2) {
      setIsSearching(true);
      searchTimeoutRef.current = setTimeout(() => {
        const searchResults = searchLocations(query);
        setResults(searchResults);
        setIsSearching(false);
        setShowResults(true);
      }, 300);
    } else {
      setResults([]);
      setShowResults(false);
      setIsSearching(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query]);

  // Close results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (resultsRef.current && !resultsRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLocationClick = (location: LocationResult) => {
    setQuery(location.name);
    setShowResults(false);
    onLocationSelect(location);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    if (e.target.value !== selectedLocation) {
      // Clear selection if user types something different
      setShowResults(true);
    }
  };

  return (
    <div className="relative" ref={resultsRef}>
      <Input
        label="Birth Location"
        type="text"
        value={query}
        onChange={handleInputChange}
        placeholder="Search for your birth city..."
        error={error}
        required
      />

      {isSearching && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 p-3">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
            <span className="ml-2 text-sm text-gray-600">Searching...</span>
          </div>
        </div>
      )}

      {showResults && results.length > 0 && !isSearching && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
          {results.map((location, index) => (
            <button
              key={index}
              type="button"
              className="w-full text-left px-4 py-3 hover:bg-purple-50 border-b border-gray-100 last:border-b-0 transition-colors"
              onClick={() => handleLocationClick(location)}
            >
              <div className="font-medium text-gray-900">{location.name}</div>
              <div className="text-sm text-gray-500">
                {location.country} • {location.timezone}
              </div>
            </button>
          ))}
        </div>
      )}

      {showResults && results.length === 0 && !isSearching && query.length >= 2 && (
        <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 p-4">
          <div className="text-center text-gray-500">
            <p className="text-sm">No locations found for "{query}"</p>
            <p className="text-xs mt-1">Try searching for a major city near your birth location</p>
          </div>
        </div>
      )}
    </div>
  );
};
