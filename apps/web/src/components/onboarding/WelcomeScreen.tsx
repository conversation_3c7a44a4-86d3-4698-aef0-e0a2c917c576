import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@taraka/ui';
import { useAuth } from '../../hooks/useAuth';

interface WelcomeScreenProps {
  onContinue: () => void;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onContinue }) => {
  const { user } = useAuth();

  return (
    <div className="min-h-screen mystical-gradient flex items-center justify-center px-4">
      <Card variant="elevated" className="w-full max-w-2xl text-center">
        <div className="mb-8">
          <div className="text-6xl mb-4">🌌</div>
          <h1 className="text-4xl font-bold cosmic-text mb-4">
            Welcome to Taraka
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            Hello {user?.user_metadata?.name || user?.email?.split('@')[0]}!
          </p>
          <p className="text-gray-500">
            Your personalized astrology journey begins here
          </p>
        </div>

        <div className="space-y-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl mb-2">📊</div>
              <h3 className="font-semibold text-gray-800 mb-1">Natal Charts</h3>
              <p className="text-sm text-gray-600">
                Generate accurate birth charts across multiple astrological systems
              </p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-2">🔮</div>
              <h3 className="font-semibold text-gray-800 mb-1">Daily Insights</h3>
              <p className="text-sm text-gray-600">
                Receive personalized predictions in reflective or traditional modes
              </p>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-2">📝</div>
              <h3 className="font-semibold text-gray-800 mb-1">Cosmic Journal</h3>
              <p className="text-sm text-gray-600">
                Track your moods and connect them to celestial influences
              </p>
            </div>
          </div>

          <div className="bg-purple-50 p-6 rounded-lg border border-purple-200">
            <h3 className="font-semibold text-purple-800 mb-3">
              🌟 What makes Taraka special?
            </h3>
            <ul className="text-sm text-purple-700 space-y-2 text-left">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>Multi-Cultural Approach:</strong> Western, Vedic, and Mayan astrology systems</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>Dual Perspectives:</strong> Choose between reflective and traditional interpretations</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>Privacy First:</strong> Your data is secure and never shared</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span><strong>Accurate Calculations:</strong> Precise astronomical data for reliable insights</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="space-y-4">
          <Button
            onClick={onContinue}
            variant="mystical"
            size="lg"
            className="w-full"
          >
            Begin My Cosmic Journey ✨
          </Button>
          
          <p className="text-xs text-gray-500">
            This will take about 2 minutes to set up your personalized experience
          </p>
        </div>
      </Card>
    </div>
  );
};
