import React, { useState } from 'react';
import { BirthDataForm } from './BirthDataForm';
import { PreferencesForm } from './PreferencesForm';
import { WelcomeScreen } from './WelcomeScreen';
import { useProfile } from '../../hooks/useProfile';

type OnboardingStep = 'welcome' | 'birth-data' | 'preferences' | 'complete';

export const OnboardingFlow: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('welcome');
  const { completeOnboarding } = useProfile();

  const handleStepComplete = async (step: OnboardingStep) => {
    switch (step) {
      case 'welcome':
        setCurrentStep('birth-data');
        break;
      case 'birth-data':
        setCurrentStep('preferences');
        break;
      case 'preferences':
        await completeOnboarding();
        setCurrentStep('complete');
        break;
      default:
        break;
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 'welcome':
        return <WelcomeScreen onContinue={() => handleStepComplete('welcome')} />;
      case 'birth-data':
        return <BirthDataForm onComplete={() => handleStepComplete('birth-data')} />;
      case 'preferences':
        return <PreferencesForm onComplete={() => handleStepComplete('preferences')} />;
      case 'complete':
        return (
          <div className="min-h-screen mystical-gradient flex items-center justify-center px-4">
            <div className="text-center">
              <div className="text-6xl mb-6">🌟</div>
              <h1 className="text-3xl font-bold text-white mb-4">
                Welcome to Your Cosmic Journey!
              </h1>
              <p className="text-purple-200 mb-8">
                Your natal chart is ready. Redirecting you to explore your cosmic blueprint...
              </p>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return <>{renderStep()}</>;
};
