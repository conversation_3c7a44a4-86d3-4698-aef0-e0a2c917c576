import React from 'react';
import { Loading } from '@taraka/ui';
import { useAuth } from '../../hooks/useAuth';
import { AuthForm } from './AuthForm';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading } = useAuth();
  const [authMode, setAuthMode] = React.useState<'signin' | 'signup'>('signin');

  if (loading) {
    return (
      <div className="min-h-screen mystical-gradient flex items-center justify-center">
        <div className="text-center">
          <Loading size="lg" className="mx-auto mb-4" />
          <p className="text-white text-lg">Connecting to the cosmos...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <AuthForm 
        mode={authMode} 
        onToggleMode={() => setAuthMode(mode => mode === 'signin' ? 'signup' : 'signin')} 
      />
    );
  }

  return <>{children}</>;
};
