import { createClient } from '@supabase/supabase-js';

// These will be set via environment variables
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase URL or Anon Key is missing. Please check your environment variables.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Database types (will be generated from Supabase later)
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          user_id: string;
          name: string | null;
          birth_date: string | null;
          birth_time: string | null;
          birth_location: string | null;
          birth_coordinates: { lat: number; lng: number } | null;
          timezone: string | null;
          preferences: {
            prediction_mode: 'reflective' | 'traditional';
            astrology_system: 'western' | 'vedic' | 'mayan';
            theme: 'light' | 'dark' | 'auto';
            notifications: {
              daily_insights: boolean;
              weekly_reports: boolean;
              moon_phases: boolean;
            };
            privacy: {
              share_charts: boolean;
              public_profile: boolean;
            };
          } | null;
          onboarding_completed: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name?: string | null;
          birth_date?: string | null;
          birth_time?: string | null;
          birth_location?: string | null;
          birth_coordinates?: { lat: number; lng: number } | null;
          timezone?: string | null;
          preferences?: any;
          onboarding_completed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string | null;
          birth_date?: string | null;
          birth_time?: string | null;
          birth_location?: string | null;
          birth_coordinates?: { lat: number; lng: number } | null;
          timezone?: string | null;
          preferences?: any;
          onboarding_completed?: boolean;
          updated_at?: string;
        };
      };
      natal_charts: {
        Row: {
          id: string;
          user_id: string;
          profile_id: string;
          system: 'western' | 'vedic' | 'mayan';
          chart_data: any;
          calculated_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          profile_id: string;
          system: 'western' | 'vedic' | 'mayan';
          chart_data: any;
          calculated_at?: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          profile_id?: string;
          system?: 'western' | 'vedic' | 'mayan';
          chart_data?: any;
          calculated_at?: string;
        };
      };
      daily_insights: {
        Row: {
          id: string;
          user_id: string;
          date: string;
          mode: 'reflective' | 'traditional';
          system: 'western' | 'vedic' | 'mayan';
          themes: string[];
          content: {
            title: string;
            summary: string;
            detailed: string;
            reflection_prompt?: string;
          };
          transits: any[];
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          date: string;
          mode: 'reflective' | 'traditional';
          system: 'western' | 'vedic' | 'mayan';
          themes: string[];
          content: any;
          transits?: any[];
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          date?: string;
          mode?: 'reflective' | 'traditional';
          system?: 'western' | 'vedic' | 'mayan';
          themes?: string[];
          content?: any;
          transits?: any[];
        };
      };
      mood_entries: {
        Row: {
          id: string;
          user_id: string;
          date: string;
          mood: number;
          emotions: string[];
          notes: string | null;
          linked_insight: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          date: string;
          mood: number;
          emotions: string[];
          notes?: string | null;
          linked_insight?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          date?: string;
          mood?: number;
          emotions?: string[];
          notes?: string | null;
          linked_insight?: string | null;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
