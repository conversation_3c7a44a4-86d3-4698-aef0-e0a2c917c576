{"hash": "685b0c96", "configHash": "58673b81", "lockfileHash": "161319f1", "browserHash": "e42ddc84", "optimized": {"react": {"src": "../../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "1fcd5553", "needsInterop": true}, "react-dom": {"src": "../../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "c5f96718", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "40218fbd", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "5bb8cfb3", "needsInterop": true}, "clsx": {"src": "../../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "aa936a7b", "needsInterop": false}, "react-dom/client": {"src": "../../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "da050307", "needsInterop": true}, "react-router-dom": {"src": "../../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "983c1ee6", "needsInterop": false}, "zod": {"src": "../../../../../node_modules/zod/dist/esm/index.js", "file": "zod.js", "fileHash": "533fe4b3", "needsInterop": false}, "date-fns": {"src": "../../../../../node_modules/date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "a9dbbc5f", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../../../../node_modules/@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "5298adf6", "needsInterop": false}, "astronomy-engine": {"src": "../../../../../node_modules/astronomy-engine/esm/astronomy.js", "file": "astronomy-engine.js", "fileHash": "e3cea1e3", "needsInterop": false}}, "chunks": {"browser-PETKTULP": {"file": "browser-PETKTULP.js"}, "browser-7BJAGV4S": {"file": "browser-7BJAGV4S.js"}, "chunk-NZRNCPSJ": {"file": "chunk-NZRNCPSJ.js"}, "chunk-2NXSZULB": {"file": "chunk-2NXSZULB.js"}, "chunk-Q7T37UUF": {"file": "chunk-Q7T37UUF.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}