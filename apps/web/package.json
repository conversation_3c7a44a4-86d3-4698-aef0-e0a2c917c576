{"name": "@taraka/web", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@taraka/shared": "workspace:*", "@taraka/astrology": "workspace:*", "@taraka/ui": "workspace:*", "@supabase/supabase-js": "^2.38.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.16.0", "framer-motion": "^10.16.0", "date-fns": "^2.30.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.1.0", "vite": "^6.3.5"}}