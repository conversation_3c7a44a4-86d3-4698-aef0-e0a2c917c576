{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@taraka/shared": ["../../packages/shared/src"], "@taraka/shared/*": ["../../packages/shared/src/*"], "@taraka/astrology": ["../../packages/astrology/src"], "@taraka/astrology/*": ["../../packages/astrology/src/*"], "@taraka/ui": ["../../packages/ui/src"], "@taraka/ui/*": ["../../packages/ui/src/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "references": [{"path": "../../packages/shared"}, {"path": "../../packages/astrology"}, {"path": "../../packages/ui"}]}