{"name": "@taraka/mobile", "version": "0.1.0", "main": "index.ts", "scripts": {"dev": "expo start", "start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:ios": "expo build:ios", "build:android": "expo build:android", "type-check": "tsc --noEmit"}, "dependencies": {"@taraka/shared": "workspace:*", "@taraka/astrology": "workspace:*", "@taraka/ui": "workspace:*", "@supabase/supabase-js": "^2.38.0", "expo": "~53.0.9", "expo-status-bar": "~2.2.3", "expo-router": "~4.0.9", "@react-navigation/native": "^6.1.0", "@react-navigation/stack": "^6.3.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-safe-area-context": "4.14.0", "react-native-screens": "4.4.0", "date-fns": "^2.30.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "^5.1.0"}, "private": true}