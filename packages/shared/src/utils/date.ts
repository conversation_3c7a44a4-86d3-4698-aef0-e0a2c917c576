import { format, parseISO, isValid } from 'date-fns';

export const formatDate = (date: Date | string, formatString = 'yyyy-MM-dd'): string => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return isValid(dateObj) ? format(dateObj, formatString) : '';
};

export const formatTime = (date: Date | string): string => {
  return formatDate(date, 'HH:mm');
};

export const formatDateTime = (date: Date | string): string => {
  return formatDate(date, 'yyyy-MM-dd HH:mm');
};

export const isValidDate = (date: unknown): date is Date => {
  return date instanceof Date && isValid(date);
};

export const parseDateTime = (dateString: string): Date | null => {
  const parsed = parseISO(dateString);
  return isValid(parsed) ? parsed : null;
};
