export const ZODIAC_SIGNS = [
  '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 
  '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON>git<PERSON><PERSON>', 'Capricorn', 'Aquarius', '<PERSON><PERSON><PERSON>'
] as const;

export const PLANETS = [
  'Sun', '<PERSON>', 'Mercury', 'Venus', 'Mars',
  'Jupiter', 'Saturn', 'Uranus', 'Neptune', 'Pluto'
] as const;

export const HOUSES = Array.from({ length: 12 }, (_, i) => i + 1) as const;

export const ASPECTS = [
  'conjunction', 'opposition', 'trine', 'square', 
  'sextile', 'quincunx', 'semisextile', 'semisquare'
] as const;

export const ASTROLOGY_SYSTEMS = ['western', 'vedic', 'mayan'] as const;
export const PREDICTION_MODES = ['reflective', 'traditional'] as const;
export const CHART_LAYOUTS = ['wheel', 'square', 'grid'] as const;
