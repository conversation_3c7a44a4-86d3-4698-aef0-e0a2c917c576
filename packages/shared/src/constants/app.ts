export const APP_NAME = 'Taraka';
export const APP_VERSION = '0.1.0';

export const THEME_MODES = ['light', 'dark', 'auto'] as const;

export const INSIGHT_THEMES = [
  { id: 'love', name: 'Love & Relationships', description: 'Romantic connections and partnerships' },
  { id: 'career', name: 'Career & Purpose', description: 'Professional growth and life direction' },
  { id: 'emotions', name: 'Emotion<PERSON> & Healing', description: 'Emotional well-being and inner work' },
  { id: 'growth', name: 'Personal Growth', description: 'Self-development and transformation' },
  { id: 'creativity', name: 'Creativity & Expression', description: 'Artistic pursuits and self-expression' },
  { id: 'challenge', name: 'Challenges & Lessons', description: 'Obstacles and learning opportunities' }
] as const;

export const DEFAULT_PREFERENCES = {
  predictionMode: 'reflective' as const,
  astrologySystem: 'western' as const,
  theme: 'auto' as const,
  notifications: {
    dailyInsights: true,
    weeklyReports: true,
    moonPhases: false,
  },
  privacy: {
    shareCharts: false,
    publicProfile: false,
  },
};
