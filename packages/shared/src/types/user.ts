import { z } from 'zod';
import { BirthData, PredictionMode, AstrologySystem } from './common';

export interface User {
  id: string;
  email: string;
  name?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile {
  userId: string;
  birthData?: BirthData;
  preferences: UserPreferences;
  onboardingCompleted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPreferences {
  predictionMode: PredictionMode;
  astrologySystem: AstrologySystem;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    dailyInsights: boolean;
    weeklyReports: boolean;
    moonPhases: boolean;
  };
  privacy: {
    shareCharts: boolean;
    publicProfile: boolean;
  };
}

// Validation schemas
export const BirthDataSchema = z.object({
  date: z.date(),
  time: z.object({
    hour: z.number().min(0).max(23),
    minute: z.number().min(0).max(59),
  }),
  location: z.object({
    name: z.string().min(1),
    coordinates: z.object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180),
    }),
    timezone: z.string(),
  }),
});

export const UserPreferencesSchema = z.object({
  predictionMode: z.enum(['reflective', 'traditional']),
  astrologySystem: z.enum(['western', 'vedic', 'mayan']),
  theme: z.enum(['light', 'dark', 'auto']),
  notifications: z.object({
    dailyInsights: z.boolean(),
    weeklyReports: z.boolean(),
    moonPhases: z.boolean(),
  }),
  privacy: z.object({
    shareCharts: z.boolean(),
    publicProfile: z.boolean(),
  }),
});
