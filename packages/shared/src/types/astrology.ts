import { BirthData, AstrologySystem, PredictionMode, InsightTheme } from './common';

export interface Planet {
  id: string;
  name: string;
  symbol: string;
  position: {
    longitude: number;
    latitude: number;
    sign: string;
    house: number;
    degree: number;
    minute: number;
  };
  retrograde: boolean;
}

export interface House {
  number: number;
  sign: string;
  cusp: number;
  ruler: string;
}

export interface Aspect {
  planet1: string;
  planet2: string;
  type: string;
  orb: number;
  exact: boolean;
}

export interface NatalChart {
  id: string;
  userId: string;
  birthData: BirthData;
  system: AstrologySystem;
  planets: Planet[];
  houses: House[];
  aspects: Aspect[];
  ascendant: {
    sign: string;
    degree: number;
  };
  midheaven: {
    sign: string;
    degree: number;
  };
  calculatedAt: Date;
}

export interface Interpretation {
  id: string;
  type: 'planet' | 'house' | 'aspect' | 'sign';
  target: string;
  mode: PredictionMode;
  system: AstrologySystem;
  content: {
    title: string;
    description: string;
    keywords: string[];
    themes: InsightTheme[];
  };
  metadata: {
    source: string;
    confidence: number;
    cultural_context?: string;
  };
}

export interface DailyInsight {
  id: string;
  userId: string;
  date: Date;
  mode: PredictionMode;
  system: AstrologySystem;
  themes: InsightTheme[];
  content: {
    title: string;
    summary: string;
    detailed: string;
    reflection_prompt?: string;
  };
  transits: {
    planet: string;
    aspect: string;
    target: string;
    influence: 'positive' | 'neutral' | 'challenging';
  }[];
  createdAt: Date;
}

export interface MoodEntry {
  id: string;
  userId: string;
  date: Date;
  mood: number; // 1-10 scale
  emotions: string[];
  notes?: string;
  linkedInsight?: string;
  createdAt: Date;
}
