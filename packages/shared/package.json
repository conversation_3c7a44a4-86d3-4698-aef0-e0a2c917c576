{"name": "@taraka/shared", "version": "0.1.0", "description": "Shared utilities and types for Taraka astrology app", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsup src/index.ts --format cjs,esm", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"date-fns": "^2.30.0", "zod": "^3.22.0"}, "devDependencies": {"tsup": "^7.2.0", "typescript": "^5.1.0"}, "peerDependencies": {"typescript": "^5.0.0"}}