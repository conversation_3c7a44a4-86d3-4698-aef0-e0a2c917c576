import { jsx as _jsx } from "react/jsx-runtime";
export const Navigation = ({ title = 'Taraka' }) => {
    return (_jsx("nav", { className: "bg-white shadow-sm border-b", children: _jsx("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: _jsx("div", { className: "flex justify-between h-16", children: _jsx("div", { className: "flex items-center", children: _jsx("h1", { className: "text-xl font-semibold text-gray-900", children: title }) }) }) }) }));
};
//# sourceMappingURL=Navigation.js.map