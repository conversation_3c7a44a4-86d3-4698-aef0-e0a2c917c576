import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export const Modal = ({ isOpen, onClose, children, title }) => {
    if (!isOpen)
        return null;
    return (_jsx("div", { className: "fixed inset-0 z-50 overflow-y-auto", children: _jsxs("div", { className: "flex items-center justify-center min-h-screen px-4", children: [_jsx("div", { className: "fixed inset-0 bg-black opacity-50", onClick: onClose }), _jsxs("div", { className: "relative bg-white rounded-lg shadow-xl max-w-md w-full", children: [title && (_jsx("div", { className: "px-6 py-4 border-b", children: _jsx("h3", { className: "text-lg font-semibold", children: title }) })), _jsx("div", { className: "px-6 py-4", children: children })] })] }) }));
};
//# sourceMappingURL=Modal.js.map