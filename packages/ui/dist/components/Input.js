import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { clsx } from 'clsx';
export const Input = ({ label, error, className, ...props }) => {
    return (_jsxs("div", { className: "space-y-1", children: [label && (_jsx("label", { className: "block text-sm font-medium text-gray-700", children: label })), _jsx("input", { className: clsx('block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm', 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500', error && 'border-red-300 focus:ring-red-500 focus:border-red-500', className), ...props }), error && (_jsx("p", { className: "text-sm text-red-600", children: error }))] }));
};
//# sourceMappingURL=Input.js.map