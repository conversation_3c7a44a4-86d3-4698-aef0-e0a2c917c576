import { jsx as _jsx } from "react/jsx-runtime";
import { clsx } from 'clsx';
export const Card = ({ children, className, variant = 'default' }) => {
    const baseClasses = 'rounded-lg p-6';
    const variantClasses = {
        default: 'bg-white border border-gray-200 shadow-sm',
        mystical: 'bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 shadow-md',
        elevated: 'bg-white shadow-lg border border-gray-100'
    };
    return (_jsx("div", { className: clsx(baseClasses, variantClasses[variant], className), children: children }));
};
//# sourceMappingURL=Card.js.map