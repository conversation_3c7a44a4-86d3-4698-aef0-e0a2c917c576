import { jsx as _jsx } from "react/jsx-runtime";
import { clsx } from 'clsx';
export const Loading = ({ size = 'md', className }) => {
    const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-8 h-8',
        lg: 'w-12 h-12'
    };
    return (_jsx("div", { className: clsx('animate-spin rounded-full border-2 border-gray-300 border-t-blue-600', sizeClasses[size], className) }));
};
//# sourceMappingURL=Loading.js.map