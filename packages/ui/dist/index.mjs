// src/components/Button.tsx
import { clsx } from "clsx";
import { jsx } from "react/jsx-runtime";
var Button = ({
  variant = "primary",
  size = "md",
  className,
  children,
  ...props
}) => {
  const baseClasses = "inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";
  const variantClasses = {
    primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",
    secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",
    mystical: "bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 focus:ring-purple-500",
    ghost: "text-gray-700 hover:bg-gray-100 focus:ring-gray-500"
  };
  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg"
  };
  return /* @__PURE__ */ jsx(
    "button",
    {
      className: clsx(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      ),
      ...props,
      children
    }
  );
};

// src/components/Input.tsx
import { clsx as clsx2 } from "clsx";
import { jsx as jsx2, jsxs } from "react/jsx-runtime";
var Input = ({
  label,
  error,
  className,
  ...props
}) => {
  return /* @__PURE__ */ jsxs("div", { className: "space-y-1", children: [
    label && /* @__PURE__ */ jsx2("label", { className: "block text-sm font-medium text-gray-700", children: label }),
    /* @__PURE__ */ jsx2(
      "input",
      {
        className: clsx2(
          "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          error && "border-red-300 focus:ring-red-500 focus:border-red-500",
          className
        ),
        ...props
      }
    ),
    error && /* @__PURE__ */ jsx2("p", { className: "text-sm text-red-600", children: error })
  ] });
};

// src/components/Card.tsx
import { clsx as clsx3 } from "clsx";
import { jsx as jsx3 } from "react/jsx-runtime";
var Card = ({
  children,
  className,
  variant = "default"
}) => {
  const baseClasses = "rounded-lg p-6";
  const variantClasses = {
    default: "bg-white border border-gray-200 shadow-sm",
    mystical: "bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 shadow-md",
    elevated: "bg-white shadow-lg border border-gray-100"
  };
  return /* @__PURE__ */ jsx3("div", { className: clsx3(baseClasses, variantClasses[variant], className), children });
};

// src/components/Modal.tsx
import { jsx as jsx4, jsxs as jsxs2 } from "react/jsx-runtime";
var Modal = ({
  isOpen,
  onClose,
  children,
  title
}) => {
  if (!isOpen)
    return null;
  return /* @__PURE__ */ jsx4("div", { className: "fixed inset-0 z-50 overflow-y-auto", children: /* @__PURE__ */ jsxs2("div", { className: "flex items-center justify-center min-h-screen px-4", children: [
    /* @__PURE__ */ jsx4(
      "div",
      {
        className: "fixed inset-0 bg-black opacity-50",
        onClick: onClose
      }
    ),
    /* @__PURE__ */ jsxs2("div", { className: "relative bg-white rounded-lg shadow-xl max-w-md w-full", children: [
      title && /* @__PURE__ */ jsx4("div", { className: "px-6 py-4 border-b", children: /* @__PURE__ */ jsx4("h3", { className: "text-lg font-semibold", children: title }) }),
      /* @__PURE__ */ jsx4("div", { className: "px-6 py-4", children })
    ] })
  ] }) });
};

// src/components/Loading.tsx
import { clsx as clsx4 } from "clsx";
import { jsx as jsx5 } from "react/jsx-runtime";
var Loading = ({
  size = "md",
  className
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12"
  };
  return /* @__PURE__ */ jsx5("div", { className: clsx4("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600", sizeClasses[size], className) });
};

// src/charts/InteractiveChart.tsx
import { useState, useRef } from "react";

// src/charts/PlanetDetails.tsx
import { jsx as jsx6, jsxs as jsxs3 } from "react/jsx-runtime";
var PlanetDetails = ({ planet, onClose }) => {
  const getPlanetMeaning = (planetName) => {
    const meanings = {
      Sun: {
        keywords: ["Identity", "Ego", "Vitality", "Leadership", "Self-expression"],
        description: "Represents your core identity, ego, and life force. The Sun shows how you express your essential self and what drives your sense of purpose."
      },
      Moon: {
        keywords: ["Emotions", "Intuition", "Subconscious", "Nurturing", "Habits"],
        description: "Governs your emotional nature, instincts, and subconscious patterns. The Moon reveals your inner world and how you process feelings."
      },
      Mercury: {
        keywords: ["Communication", "Intellect", "Learning", "Travel", "Technology"],
        description: "Rules communication, thinking, and information processing. Mercury shows how you learn, communicate, and connect with others."
      },
      Venus: {
        keywords: ["Love", "Beauty", "Harmony", "Values", "Relationships"],
        description: "Governs love, beauty, and what you value. Venus reveals your approach to relationships and what brings you pleasure and harmony."
      },
      Mars: {
        keywords: ["Action", "Energy", "Passion", "Courage", "Conflict"],
        description: "Represents your drive, ambition, and how you assert yourself. Mars shows your approach to action, competition, and pursuing desires."
      },
      Jupiter: {
        keywords: ["Expansion", "Wisdom", "Growth", "Optimism", "Philosophy"],
        description: "The planet of growth, expansion, and higher learning. Jupiter shows where you seek meaning, wisdom, and opportunities for growth."
      },
      Saturn: {
        keywords: ["Discipline", "Structure", "Responsibility", "Limitations", "Mastery"],
        description: "Represents structure, discipline, and life lessons. Saturn shows where you face challenges and develop mastery through perseverance."
      },
      Uranus: {
        keywords: ["Innovation", "Freedom", "Rebellion", "Originality", "Change"],
        description: "The planet of innovation and sudden change. Uranus shows where you seek freedom, express originality, and break from convention."
      },
      Neptune: {
        keywords: ["Spirituality", "Dreams", "Illusion", "Compassion", "Creativity"],
        description: "Governs spirituality, dreams, and imagination. Neptune shows your connection to the divine and areas of inspiration or confusion."
      },
      Pluto: {
        keywords: ["Transformation", "Power", "Regeneration", "Intensity", "Rebirth"],
        description: "The planet of transformation and rebirth. Pluto shows where you experience deep change, power dynamics, and psychological growth."
      }
    };
    return meanings[planetName] || {
      keywords: ["Mystery", "Unknown"],
      description: "This celestial body holds unique significance in your chart."
    };
  };
  const getSignMeaning = (sign) => {
    const signMeanings = {
      Aries: "Bold, pioneering, and action-oriented energy",
      Taurus: "Stable, practical, and sensual expression",
      Gemini: "Curious, communicative, and adaptable nature",
      Cancer: "Nurturing, intuitive, and emotionally sensitive",
      Leo: "Creative, confident, and dramatic expression",
      Virgo: "Analytical, perfectionist, and service-oriented",
      Libra: "Harmonious, diplomatic, and relationship-focused",
      Scorpio: "Intense, transformative, and deeply emotional",
      Sagittarius: "Adventurous, philosophical, and freedom-loving",
      Capricorn: "Ambitious, disciplined, and goal-oriented",
      Aquarius: "Independent, innovative, and humanitarian",
      Pisces: "Compassionate, intuitive, and spiritually inclined"
    };
    return signMeanings[sign] || "Unique cosmic influence";
  };
  const getHouseMeaning = (house) => {
    const houseMeanings = {
      1: "Self, identity, and first impressions",
      2: "Values, possessions, and self-worth",
      3: "Communication, siblings, and short trips",
      4: "Home, family, and emotional foundation",
      5: "Creativity, romance, and self-expression",
      6: "Work, health, and daily routines",
      7: "Partnerships, marriage, and open enemies",
      8: "Transformation, shared resources, and mysteries",
      9: "Philosophy, higher education, and long journeys",
      10: "Career, reputation, and public image",
      11: "Friends, groups, and hopes and dreams",
      12: "Spirituality, subconscious, and hidden things"
    };
    return houseMeanings[house] || "Cosmic influence area";
  };
  const meaning = getPlanetMeaning(planet.name);
  return /* @__PURE__ */ jsx6("div", { className: "w-80", children: /* @__PURE__ */ jsxs3(Card, { variant: "elevated", className: "relative", children: [
    /* @__PURE__ */ jsx6(
      Button,
      {
        variant: "ghost",
        size: "sm",
        onClick: onClose,
        className: "absolute top-2 right-2 p-1",
        children: "\u2715"
      }
    ),
    /* @__PURE__ */ jsxs3("div", { className: "mb-4", children: [
      /* @__PURE__ */ jsxs3("div", { className: "flex items-center gap-3 mb-2", children: [
        /* @__PURE__ */ jsx6("div", { className: "text-3xl", children: planet.symbol }),
        /* @__PURE__ */ jsxs3("div", { children: [
          /* @__PURE__ */ jsx6("h3", { className: "text-xl font-bold text-gray-800", children: planet.name }),
          /* @__PURE__ */ jsxs3("p", { className: "text-sm text-gray-600", children: [
            planet.position.degree,
            "\xB0",
            planet.position.minute,
            "' ",
            planet.position.sign
          ] })
        ] })
      ] }),
      planet.retrograde && /* @__PURE__ */ jsxs3("div", { className: "inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium", children: [
        /* @__PURE__ */ jsx6("span", { className: "w-2 h-2 bg-red-500 rounded-full" }),
        "Retrograde"
      ] })
    ] }),
    /* @__PURE__ */ jsxs3("div", { className: "space-y-4", children: [
      /* @__PURE__ */ jsxs3("div", { className: "grid grid-cols-2 gap-4 text-sm", children: [
        /* @__PURE__ */ jsxs3("div", { children: [
          /* @__PURE__ */ jsx6("span", { className: "font-medium text-gray-700", children: "Sign:" }),
          /* @__PURE__ */ jsx6("p", { className: "text-gray-600", children: planet.position.sign })
        ] }),
        /* @__PURE__ */ jsxs3("div", { children: [
          /* @__PURE__ */ jsx6("span", { className: "font-medium text-gray-700", children: "House:" }),
          /* @__PURE__ */ jsx6("p", { className: "text-gray-600", children: planet.position.house })
        ] })
      ] }),
      /* @__PURE__ */ jsxs3("div", { children: [
        /* @__PURE__ */ jsx6("h4", { className: "font-medium text-gray-700 mb-2", children: "Keywords" }),
        /* @__PURE__ */ jsx6("div", { className: "flex flex-wrap gap-1", children: meaning.keywords.map((keyword, index) => /* @__PURE__ */ jsx6(
          "span",
          {
            className: "px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs",
            children: keyword
          },
          index
        )) })
      ] }),
      /* @__PURE__ */ jsxs3("div", { children: [
        /* @__PURE__ */ jsx6("h4", { className: "font-medium text-gray-700 mb-2", children: "Meaning" }),
        /* @__PURE__ */ jsx6("p", { className: "text-sm text-gray-600 leading-relaxed", children: meaning.description })
      ] }),
      /* @__PURE__ */ jsxs3("div", { children: [
        /* @__PURE__ */ jsxs3("h4", { className: "font-medium text-gray-700 mb-2", children: [
          "In ",
          planet.position.sign
        ] }),
        /* @__PURE__ */ jsx6("p", { className: "text-sm text-gray-600 leading-relaxed", children: getSignMeaning(planet.position.sign) })
      ] }),
      /* @__PURE__ */ jsxs3("div", { children: [
        /* @__PURE__ */ jsxs3("h4", { className: "font-medium text-gray-700 mb-2", children: [
          "House ",
          planet.position.house
        ] }),
        /* @__PURE__ */ jsx6("p", { className: "text-sm text-gray-600 leading-relaxed", children: getHouseMeaning(planet.position.house) })
      ] }),
      /* @__PURE__ */ jsxs3("div", { className: "pt-3 border-t border-gray-200", children: [
        /* @__PURE__ */ jsx6("h4", { className: "font-medium text-gray-700 mb-2", children: "Technical Details" }),
        /* @__PURE__ */ jsxs3("div", { className: "text-xs text-gray-500 space-y-1", children: [
          /* @__PURE__ */ jsxs3("div", { children: [
            "Longitude: ",
            planet.position.longitude.toFixed(2),
            "\xB0"
          ] }),
          /* @__PURE__ */ jsxs3("div", { children: [
            "Latitude: ",
            planet.position.latitude.toFixed(2),
            "\xB0"
          ] }),
          planet.retrograde && /* @__PURE__ */ jsx6("div", { className: "text-red-600", children: "\u26A0\uFE0F This planet appears to move backward from Earth's perspective" })
        ] })
      ] })
    ] })
  ] }) });
};

// src/charts/InteractiveChart.tsx
import { jsx as jsx7, jsxs as jsxs4 } from "react/jsx-runtime";
var InteractiveChart = ({
  chart,
  size = 400,
  onPlanetClick
}) => {
  const [selectedPlanet, setSelectedPlanet] = useState(null);
  const [hoveredPlanet, setHoveredPlanet] = useState(null);
  const svgRef = useRef(null);
  const center = size / 2;
  const outerRadius = size * 0.45;
  const innerRadius = size * 0.25;
  const getPlanetPosition = (planet) => {
    const angle = (planet.position.longitude - 90) * (Math.PI / 180);
    const radius = outerRadius - 30;
    return {
      x: center + Math.cos(angle) * radius,
      y: center + Math.sin(angle) * radius,
      angle: planet.position.longitude
    };
  };
  const getHousePosition = (houseNumber) => {
    const angle = ((houseNumber - 1) * 30 - 90) * (Math.PI / 180);
    const radius = (outerRadius + innerRadius) / 2;
    return {
      x: center + Math.cos(angle) * radius,
      y: center + Math.sin(angle) * radius
    };
  };
  const handlePlanetClick = (planet) => {
    setSelectedPlanet(planet);
    onPlanetClick?.(planet);
  };
  const generateZodiacWheel = () => {
    const signs = [
      "Aries",
      "Taurus",
      "Gemini",
      "Cancer",
      "Leo",
      "Virgo",
      "Libra",
      "Scorpio",
      "Sagittarius",
      "Capricorn",
      "Aquarius",
      "Pisces"
    ];
    return signs.map((sign, index) => {
      const startAngle = index * 30 - 90;
      const endAngle = (index + 1) * 30 - 90;
      const startRad = startAngle * (Math.PI / 180);
      const endRad = endAngle * (Math.PI / 180);
      const x1 = center + Math.cos(startRad) * innerRadius;
      const y1 = center + Math.sin(startRad) * innerRadius;
      const x2 = center + Math.cos(endRad) * innerRadius;
      const y2 = center + Math.sin(endRad) * innerRadius;
      const x3 = center + Math.cos(endRad) * outerRadius;
      const y3 = center + Math.sin(endRad) * outerRadius;
      const x4 = center + Math.cos(startRad) * outerRadius;
      const y4 = center + Math.sin(startRad) * outerRadius;
      const largeArcFlag = 0;
      const pathData = [
        `M ${x1} ${y1}`,
        `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        `L ${x3} ${y3}`,
        `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
        "Z"
      ].join(" ");
      const textAngle = startAngle + 15;
      const textRad = textAngle * (Math.PI / 180);
      const textRadius = (innerRadius + outerRadius) / 2;
      const textX = center + Math.cos(textRad) * textRadius;
      const textY = center + Math.sin(textRad) * textRadius;
      return /* @__PURE__ */ jsxs4("g", { children: [
        /* @__PURE__ */ jsx7(
          "path",
          {
            d: pathData,
            fill: index % 2 === 0 ? "#f8fafc" : "#f1f5f9",
            stroke: "#e2e8f0",
            strokeWidth: "1",
            className: "transition-colors hover:fill-purple-50"
          }
        ),
        /* @__PURE__ */ jsx7(
          "text",
          {
            x: textX,
            y: textY,
            textAnchor: "middle",
            dominantBaseline: "middle",
            className: "text-xs font-medium fill-gray-600",
            transform: `rotate(${textAngle > 90 && textAngle < 270 ? textAngle + 180 : textAngle} ${textX} ${textY})`,
            children: sign
          }
        )
      ] }, sign);
    });
  };
  const generateHouseLines = () => {
    return Array.from({ length: 12 }, (_, i) => {
      const angle = (i * 30 - 90) * (Math.PI / 180);
      const x1 = center + Math.cos(angle) * innerRadius;
      const y1 = center + Math.sin(angle) * innerRadius;
      const x2 = center + Math.cos(angle) * outerRadius;
      const y2 = center + Math.sin(angle) * outerRadius;
      const housePos = getHousePosition(i + 1);
      return /* @__PURE__ */ jsxs4("g", { children: [
        /* @__PURE__ */ jsx7(
          "line",
          {
            x1,
            y1,
            x2,
            y2,
            stroke: "#cbd5e1",
            strokeWidth: "1"
          }
        ),
        /* @__PURE__ */ jsx7(
          "text",
          {
            x: housePos.x,
            y: housePos.y,
            textAnchor: "middle",
            dominantBaseline: "middle",
            className: "text-xs font-bold fill-gray-700",
            children: i + 1
          }
        )
      ] }, i);
    });
  };
  const generateAspectLines = () => {
    return chart.aspects.map((aspect, index) => {
      const planet1 = chart.planets.find((p) => p.id === aspect.planet1);
      const planet2 = chart.planets.find((p) => p.id === aspect.planet2);
      if (!planet1 || !planet2)
        return null;
      const pos1 = getPlanetPosition(planet1);
      const pos2 = getPlanetPosition(planet2);
      const aspectColors = {
        conjunction: "#ef4444",
        opposition: "#dc2626",
        trine: "#10b981",
        square: "#f59e0b",
        sextile: "#3b82f6",
        quincunx: "#8b5cf6"
      };
      const color = aspectColors[aspect.type] || "#6b7280";
      return /* @__PURE__ */ jsx7(
        "line",
        {
          x1: pos1.x,
          y1: pos1.y,
          x2: pos2.x,
          y2: pos2.y,
          stroke: color,
          strokeWidth: "1",
          strokeOpacity: "0.6",
          strokeDasharray: aspect.exact ? "none" : "3,3"
        },
        index
      );
    });
  };
  return /* @__PURE__ */ jsxs4("div", { className: "relative", children: [
    /* @__PURE__ */ jsxs4(
      "svg",
      {
        ref: svgRef,
        width: size,
        height: size,
        className: "drop-shadow-lg",
        style: { background: "radial-gradient(circle, #faf5ff 0%, #f3e8ff 100%)" },
        children: [
          generateZodiacWheel(),
          generateHouseLines(),
          generateAspectLines(),
          chart.planets.map((planet) => {
            const pos = getPlanetPosition(planet);
            const isSelected = selectedPlanet?.id === planet.id;
            const isHovered = hoveredPlanet?.id === planet.id;
            return /* @__PURE__ */ jsxs4("g", { children: [
              (isSelected || isHovered) && /* @__PURE__ */ jsx7(
                "circle",
                {
                  cx: pos.x,
                  cy: pos.y,
                  r: "20",
                  fill: "url(#planetGlow)",
                  opacity: "0.6",
                  className: "animate-pulse"
                }
              ),
              /* @__PURE__ */ jsx7(
                "circle",
                {
                  cx: pos.x,
                  cy: pos.y,
                  r: isSelected ? "16" : "12",
                  fill: isSelected ? "#8b5cf6" : "#6366f1",
                  stroke: planet.retrograde ? "#ef4444" : "#ffffff",
                  strokeWidth: "2",
                  className: "cursor-pointer transition-all duration-200 hover:scale-110",
                  onClick: () => handlePlanetClick(planet),
                  onMouseEnter: () => setHoveredPlanet(planet),
                  onMouseLeave: () => setHoveredPlanet(null)
                }
              ),
              /* @__PURE__ */ jsx7(
                "text",
                {
                  x: pos.x,
                  y: pos.y,
                  textAnchor: "middle",
                  dominantBaseline: "middle",
                  className: "text-sm font-bold fill-white pointer-events-none",
                  children: planet.symbol
                }
              ),
              /* @__PURE__ */ jsx7(
                "text",
                {
                  x: pos.x,
                  y: pos.y + 25,
                  textAnchor: "middle",
                  dominantBaseline: "middle",
                  className: "text-xs font-medium fill-gray-700 pointer-events-none",
                  children: planet.name
                }
              ),
              planet.retrograde && /* @__PURE__ */ jsx7(
                "text",
                {
                  x: pos.x + 18,
                  y: pos.y - 10,
                  textAnchor: "middle",
                  dominantBaseline: "middle",
                  className: "text-xs font-bold fill-red-500 pointer-events-none",
                  children: "R"
                }
              )
            ] }, planet.id);
          }),
          /* @__PURE__ */ jsx7("defs", { children: /* @__PURE__ */ jsxs4("radialGradient", { id: "planetGlow", cx: "50%", cy: "50%", r: "50%", children: [
            /* @__PURE__ */ jsx7("stop", { offset: "0%", stopColor: "#8b5cf6", stopOpacity: "0.8" }),
            /* @__PURE__ */ jsx7("stop", { offset: "100%", stopColor: "#8b5cf6", stopOpacity: "0" })
          ] }) }),
          /* @__PURE__ */ jsx7(
            "circle",
            {
              cx: center,
              cy: center,
              r: innerRadius,
              fill: "url(#centerGradient)",
              stroke: "#e2e8f0",
              strokeWidth: "2"
            }
          ),
          /* @__PURE__ */ jsxs4("g", { children: [
            /* @__PURE__ */ jsx7(
              "line",
              {
                x1: center,
                y1: center - outerRadius,
                x2: center,
                y2: center - outerRadius - 15,
                stroke: "#8b5cf6",
                strokeWidth: "3",
                markerEnd: "url(#arrowhead)"
              }
            ),
            /* @__PURE__ */ jsx7(
              "text",
              {
                x: center,
                y: center - outerRadius - 25,
                textAnchor: "middle",
                className: "text-sm font-bold fill-purple-600",
                children: "ASC"
              }
            )
          ] }),
          /* @__PURE__ */ jsxs4("defs", { children: [
            /* @__PURE__ */ jsxs4("linearGradient", { id: "centerGradient", cx: "50%", cy: "50%", r: "50%", children: [
              /* @__PURE__ */ jsx7("stop", { offset: "0%", stopColor: "#ffffff" }),
              /* @__PURE__ */ jsx7("stop", { offset: "100%", stopColor: "#f8fafc" })
            ] }),
            /* @__PURE__ */ jsx7(
              "marker",
              {
                id: "arrowhead",
                markerWidth: "10",
                markerHeight: "7",
                refX: "9",
                refY: "3.5",
                orient: "auto",
                children: /* @__PURE__ */ jsx7(
                  "polygon",
                  {
                    points: "0 0, 10 3.5, 0 7",
                    fill: "#8b5cf6"
                  }
                )
              }
            )
          ] })
        ]
      }
    ),
    selectedPlanet && /* @__PURE__ */ jsx7("div", { className: "absolute top-0 right-0 transform translate-x-full ml-4", children: /* @__PURE__ */ jsx7(
      PlanetDetails,
      {
        planet: selectedPlanet,
        onClose: () => setSelectedPlanet(null)
      }
    ) })
  ] });
};

// src/charts/WheelChart.tsx
import { jsx as jsx8, jsxs as jsxs5 } from "react/jsx-runtime";
var WheelChart = ({
  chart,
  size = 300
}) => {
  return /* @__PURE__ */ jsx8(
    "div",
    {
      className: "relative bg-gradient-to-br from-indigo-900 to-purple-900 rounded-full flex items-center justify-center text-white",
      style: { width: size, height: size },
      children: /* @__PURE__ */ jsxs5("div", { className: "text-center", children: [
        /* @__PURE__ */ jsx8("div", { className: "text-lg font-bold", children: "Wheel Chart" }),
        /* @__PURE__ */ jsxs5("div", { className: "text-sm opacity-75", children: [
          chart.ascendant.sign,
          " Rising"
        ] })
      ] })
    }
  );
};

// src/charts/SquareChart.tsx
import { jsx as jsx9, jsxs as jsxs6 } from "react/jsx-runtime";
var SquareChart = ({
  chart,
  size = 300
}) => {
  return /* @__PURE__ */ jsx9(
    "div",
    {
      className: "relative bg-gradient-to-br from-purple-900 to-indigo-900 flex items-center justify-center text-white",
      style: { width: size, height: size },
      children: /* @__PURE__ */ jsxs6("div", { className: "text-center", children: [
        /* @__PURE__ */ jsx9("div", { className: "text-lg font-bold", children: "Square Chart" }),
        /* @__PURE__ */ jsxs6("div", { className: "text-sm opacity-75", children: [
          chart.ascendant.sign,
          " Rising"
        ] })
      ] })
    }
  );
};

// src/charts/ChartRenderer.tsx
import { jsx as jsx10 } from "react/jsx-runtime";
var ChartRenderer = ({
  chart,
  layout = "wheel",
  size = 400,
  interactive = true,
  onPlanetClick
}) => {
  if (interactive && layout === "wheel") {
    return /* @__PURE__ */ jsx10(
      InteractiveChart,
      {
        chart,
        size,
        onPlanetClick
      }
    );
  }
  switch (layout) {
    case "square":
      return /* @__PURE__ */ jsx10(SquareChart, { chart, size });
    case "wheel":
    default:
      return /* @__PURE__ */ jsx10(WheelChart, { chart, size });
  }
};

// src/components/Layout.tsx
import { jsx as jsx11 } from "react/jsx-runtime";
var Layout = ({ children }) => {
  return /* @__PURE__ */ jsx11("div", { className: "min-h-screen bg-gray-50", children });
};

// src/components/Navigation.tsx
import { jsx as jsx12 } from "react/jsx-runtime";
var Navigation = ({ title = "Taraka" }) => {
  return /* @__PURE__ */ jsx12("nav", { className: "bg-white shadow-sm border-b", children: /* @__PURE__ */ jsx12("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsx12("div", { className: "flex justify-between h-16", children: /* @__PURE__ */ jsx12("div", { className: "flex items-center", children: /* @__PURE__ */ jsx12("h1", { className: "text-xl font-semibold text-gray-900", children: title }) }) }) }) });
};

// src/styles/theme.ts
var theme = {
  colors: {
    primary: {
      50: "#f0f9ff",
      100: "#e0f2fe",
      500: "#0ea5e9",
      600: "#0284c7",
      900: "#0c4a6e"
    },
    secondary: {
      50: "#fdf4ff",
      100: "#fae8ff",
      500: "#a855f7",
      600: "#9333ea",
      900: "#581c87"
    },
    mystical: {
      deep: "#1a1b3a",
      midnight: "#2d1b69",
      cosmic: "#4c1d95",
      starlight: "#8b5cf6",
      aurora: "#a78bfa"
    },
    neutral: {
      50: "#f8fafc",
      100: "#f1f5f9",
      200: "#e2e8f0",
      300: "#cbd5e1",
      400: "#94a3b8",
      500: "#64748b",
      600: "#475569",
      700: "#334155",
      800: "#1e293b",
      900: "#0f172a"
    }
  },
  spacing: {
    xs: "0.25rem",
    sm: "0.5rem",
    md: "1rem",
    lg: "1.5rem",
    xl: "2rem",
    "2xl": "3rem"
  },
  borderRadius: {
    sm: "0.25rem",
    md: "0.5rem",
    lg: "0.75rem",
    xl: "1rem",
    full: "9999px"
  },
  typography: {
    fontFamily: {
      sans: ["Inter", "system-ui", "sans-serif"],
      serif: ["Crimson Text", "serif"],
      mono: ["JetBrains Mono", "monospace"]
    },
    fontSize: {
      xs: "0.75rem",
      sm: "0.875rem",
      base: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
      "4xl": "2.25rem"
    }
  }
};
export {
  Button,
  Card,
  ChartRenderer,
  Input,
  InteractiveChart,
  Layout,
  Loading,
  Modal,
  Navigation,
  PlanetDetails,
  SquareChart,
  WheelChart,
  theme
};
