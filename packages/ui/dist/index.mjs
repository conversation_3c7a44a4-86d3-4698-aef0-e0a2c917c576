// src/components/Button.tsx
import { clsx } from "clsx";
import { jsx } from "react/jsx-runtime";
var Button = ({
  variant = "primary",
  size = "md",
  className,
  children,
  ...props
}) => {
  const baseClasses = "inline-flex items-center justify-center font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";
  const variantClasses = {
    primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",
    secondary: "bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500",
    mystical: "bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 focus:ring-purple-500",
    ghost: "text-gray-700 hover:bg-gray-100 focus:ring-gray-500"
  };
  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg"
  };
  return /* @__PURE__ */ jsx(
    "button",
    {
      className: clsx(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      ),
      ...props,
      children
    }
  );
};

// src/components/Input.tsx
import { clsx as clsx2 } from "clsx";
import { jsx as jsx2, jsxs } from "react/jsx-runtime";
var Input = ({
  label,
  error,
  className,
  ...props
}) => {
  return /* @__PURE__ */ jsxs("div", { className: "space-y-1", children: [
    label && /* @__PURE__ */ jsx2("label", { className: "block text-sm font-medium text-gray-700", children: label }),
    /* @__PURE__ */ jsx2(
      "input",
      {
        className: clsx2(
          "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          error && "border-red-300 focus:ring-red-500 focus:border-red-500",
          className
        ),
        ...props
      }
    ),
    error && /* @__PURE__ */ jsx2("p", { className: "text-sm text-red-600", children: error })
  ] });
};

// src/components/Card.tsx
import { clsx as clsx3 } from "clsx";
import { jsx as jsx3 } from "react/jsx-runtime";
var Card = ({
  children,
  className,
  variant = "default"
}) => {
  const baseClasses = "rounded-lg p-6";
  const variantClasses = {
    default: "bg-white border border-gray-200 shadow-sm",
    mystical: "bg-gradient-to-br from-purple-50 to-indigo-50 border border-purple-200 shadow-md",
    elevated: "bg-white shadow-lg border border-gray-100"
  };
  return /* @__PURE__ */ jsx3("div", { className: clsx3(baseClasses, variantClasses[variant], className), children });
};

// src/components/Modal.tsx
import { jsx as jsx4, jsxs as jsxs2 } from "react/jsx-runtime";
var Modal = ({
  isOpen,
  onClose,
  children,
  title
}) => {
  if (!isOpen)
    return null;
  return /* @__PURE__ */ jsx4("div", { className: "fixed inset-0 z-50 overflow-y-auto", children: /* @__PURE__ */ jsxs2("div", { className: "flex items-center justify-center min-h-screen px-4", children: [
    /* @__PURE__ */ jsx4(
      "div",
      {
        className: "fixed inset-0 bg-black opacity-50",
        onClick: onClose
      }
    ),
    /* @__PURE__ */ jsxs2("div", { className: "relative bg-white rounded-lg shadow-xl max-w-md w-full", children: [
      title && /* @__PURE__ */ jsx4("div", { className: "px-6 py-4 border-b", children: /* @__PURE__ */ jsx4("h3", { className: "text-lg font-semibold", children: title }) }),
      /* @__PURE__ */ jsx4("div", { className: "px-6 py-4", children })
    ] })
  ] }) });
};

// src/components/Loading.tsx
import { clsx as clsx4 } from "clsx";
import { jsx as jsx5 } from "react/jsx-runtime";
var Loading = ({
  size = "md",
  className
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12"
  };
  return /* @__PURE__ */ jsx5("div", { className: clsx4("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600", sizeClasses[size], className) });
};

// src/charts/ChartRenderer.tsx
import { jsx as jsx6, jsxs as jsxs3 } from "react/jsx-runtime";
var ChartRenderer = ({
  chart,
  layout = "wheel",
  size = 400,
  interactive = true
}) => {
  return /* @__PURE__ */ jsxs3(
    "div",
    {
      className: "relative bg-gradient-to-br from-indigo-900 to-purple-900 rounded-full flex items-center justify-center text-white",
      style: { width: size, height: size },
      children: [
        /* @__PURE__ */ jsxs3("div", { className: "text-center", children: [
          /* @__PURE__ */ jsx6("div", { className: "text-2xl font-bold mb-2", children: chart.ascendant.sign }),
          /* @__PURE__ */ jsx6("div", { className: "text-sm opacity-75", children: "Rising Sign" }),
          /* @__PURE__ */ jsxs3("div", { className: "mt-4 text-xs", children: [
            chart.planets.length,
            " planets calculated"
          ] }),
          /* @__PURE__ */ jsxs3("div", { className: "text-xs opacity-50", children: [
            layout,
            " layout \u2022 ",
            chart.system,
            " system"
          ] })
        ] }),
        /* @__PURE__ */ jsx6("div", { className: "absolute inset-4 border border-white/20 rounded-full" }),
        /* @__PURE__ */ jsx6("div", { className: "absolute inset-8 border border-white/10 rounded-full" })
      ]
    }
  );
};

// src/charts/WheelChart.tsx
import { jsx as jsx7, jsxs as jsxs4 } from "react/jsx-runtime";
var WheelChart = ({
  chart,
  size = 300
}) => {
  return /* @__PURE__ */ jsx7(
    "div",
    {
      className: "relative bg-gradient-to-br from-indigo-900 to-purple-900 rounded-full flex items-center justify-center text-white",
      style: { width: size, height: size },
      children: /* @__PURE__ */ jsxs4("div", { className: "text-center", children: [
        /* @__PURE__ */ jsx7("div", { className: "text-lg font-bold", children: "Wheel Chart" }),
        /* @__PURE__ */ jsxs4("div", { className: "text-sm opacity-75", children: [
          chart.ascendant.sign,
          " Rising"
        ] })
      ] })
    }
  );
};

// src/charts/SquareChart.tsx
import { jsx as jsx8, jsxs as jsxs5 } from "react/jsx-runtime";
var SquareChart = ({
  chart,
  size = 300
}) => {
  return /* @__PURE__ */ jsx8(
    "div",
    {
      className: "relative bg-gradient-to-br from-purple-900 to-indigo-900 flex items-center justify-center text-white",
      style: { width: size, height: size },
      children: /* @__PURE__ */ jsxs5("div", { className: "text-center", children: [
        /* @__PURE__ */ jsx8("div", { className: "text-lg font-bold", children: "Square Chart" }),
        /* @__PURE__ */ jsxs5("div", { className: "text-sm opacity-75", children: [
          chart.ascendant.sign,
          " Rising"
        ] })
      ] })
    }
  );
};

// src/components/Layout.tsx
import { jsx as jsx9 } from "react/jsx-runtime";
var Layout = ({ children }) => {
  return /* @__PURE__ */ jsx9("div", { className: "min-h-screen bg-gray-50", children });
};

// src/components/Navigation.tsx
import { jsx as jsx10 } from "react/jsx-runtime";
var Navigation = ({ title = "Taraka" }) => {
  return /* @__PURE__ */ jsx10("nav", { className: "bg-white shadow-sm border-b", children: /* @__PURE__ */ jsx10("div", { className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8", children: /* @__PURE__ */ jsx10("div", { className: "flex justify-between h-16", children: /* @__PURE__ */ jsx10("div", { className: "flex items-center", children: /* @__PURE__ */ jsx10("h1", { className: "text-xl font-semibold text-gray-900", children: title }) }) }) }) });
};

// src/styles/theme.ts
var theme = {
  colors: {
    primary: {
      50: "#f0f9ff",
      100: "#e0f2fe",
      500: "#0ea5e9",
      600: "#0284c7",
      900: "#0c4a6e"
    },
    secondary: {
      50: "#fdf4ff",
      100: "#fae8ff",
      500: "#a855f7",
      600: "#9333ea",
      900: "#581c87"
    },
    mystical: {
      deep: "#1a1b3a",
      midnight: "#2d1b69",
      cosmic: "#4c1d95",
      starlight: "#8b5cf6",
      aurora: "#a78bfa"
    },
    neutral: {
      50: "#f8fafc",
      100: "#f1f5f9",
      200: "#e2e8f0",
      300: "#cbd5e1",
      400: "#94a3b8",
      500: "#64748b",
      600: "#475569",
      700: "#334155",
      800: "#1e293b",
      900: "#0f172a"
    }
  },
  spacing: {
    xs: "0.25rem",
    sm: "0.5rem",
    md: "1rem",
    lg: "1.5rem",
    xl: "2rem",
    "2xl": "3rem"
  },
  borderRadius: {
    sm: "0.25rem",
    md: "0.5rem",
    lg: "0.75rem",
    xl: "1rem",
    full: "9999px"
  },
  typography: {
    fontFamily: {
      sans: ["Inter", "system-ui", "sans-serif"],
      serif: ["Crimson Text", "serif"],
      mono: ["JetBrains Mono", "monospace"]
    },
    fontSize: {
      xs: "0.75rem",
      sm: "0.875rem",
      base: "1rem",
      lg: "1.125rem",
      xl: "1.25rem",
      "2xl": "1.5rem",
      "3xl": "1.875rem",
      "4xl": "2.25rem"
    }
  }
};
export {
  Button,
  Card,
  ChartRenderer,
  Input,
  Layout,
  Loading,
  Modal,
  Navigation,
  SquareChart,
  WheelChart,
  theme
};
