import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export const WheelChart = ({ chart, size = 300 }) => {
    return (_jsx("div", { className: "relative bg-gradient-to-br from-indigo-900 to-purple-900 rounded-full flex items-center justify-center text-white", style: { width: size, height: size }, children: _jsxs("div", { className: "text-center", children: [_jsx("div", { className: "text-lg font-bold", children: "Wheel Chart" }), _jsxs("div", { className: "text-sm opacity-75", children: [chart.ascendant.sign, " Rising"] })] }) }));
};
//# sourceMappingURL=WheelChart.js.map