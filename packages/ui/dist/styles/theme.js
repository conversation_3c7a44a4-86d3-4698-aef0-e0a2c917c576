export const theme = {
    colors: {
        primary: {
            50: '#f0f9ff',
            100: '#e0f2fe',
            500: '#0ea5e9',
            600: '#0284c7',
            900: '#0c4a6e',
        },
        secondary: {
            50: '#fdf4ff',
            100: '#fae8ff',
            500: '#a855f7',
            600: '#9333ea',
            900: '#581c87',
        },
        mystical: {
            deep: '#1a1b3a',
            midnight: '#2d1b69',
            cosmic: '#4c1d95',
            starlight: '#8b5cf6',
            aurora: '#a78bfa',
        },
        neutral: {
            50: '#f8fafc',
            100: '#f1f5f9',
            200: '#e2e8f0',
            300: '#cbd5e1',
            400: '#94a3b8',
            500: '#64748b',
            600: '#475569',
            700: '#334155',
            800: '#1e293b',
            900: '#0f172a',
        }
    },
    spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        '2xl': '3rem',
    },
    borderRadius: {
        sm: '0.25rem',
        md: '0.5rem',
        lg: '0.75rem',
        xl: '1rem',
        full: '9999px',
    },
    typography: {
        fontFamily: {
            sans: ['Inter', 'system-ui', 'sans-serif'],
            serif: ['Crimson Text', 'serif'],
            mono: ['JetBrains Mono', 'monospace'],
        },
        fontSize: {
            xs: '0.75rem',
            sm: '0.875rem',
            base: '1rem',
            lg: '1.125rem',
            xl: '1.25rem',
            '2xl': '1.5rem',
            '3xl': '1.875rem',
            '4xl': '2.25rem',
        }
    }
};
//# sourceMappingURL=theme.js.map