export declare const theme: {
    colors: {
        primary: {
            50: string;
            100: string;
            500: string;
            600: string;
            900: string;
        };
        secondary: {
            50: string;
            100: string;
            500: string;
            600: string;
            900: string;
        };
        mystical: {
            deep: string;
            midnight: string;
            cosmic: string;
            starlight: string;
            aurora: string;
        };
        neutral: {
            50: string;
            100: string;
            200: string;
            300: string;
            400: string;
            500: string;
            600: string;
            700: string;
            800: string;
            900: string;
        };
    };
    spacing: {
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
        '2xl': string;
    };
    borderRadius: {
        sm: string;
        md: string;
        lg: string;
        xl: string;
        full: string;
    };
    typography: {
        fontFamily: {
            sans: string[];
            serif: string[];
            mono: string[];
        };
        fontSize: {
            xs: string;
            sm: string;
            base: string;
            lg: string;
            xl: string;
            '2xl': string;
            '3xl': string;
            '4xl': string;
        };
    };
};
//# sourceMappingURL=theme.d.ts.map