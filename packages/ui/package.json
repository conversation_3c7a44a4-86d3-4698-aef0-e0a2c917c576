{"name": "@taraka/ui", "version": "0.1.0", "description": "Shared UI components for Taraka astrology app", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./styles": "./dist/styles.css"}, "scripts": {"build": "tsup src/index.ts --format cjs,esm", "build:css": "tailwindcss -i ./src/styles/globals.css -o ./dist/styles.css --minify", "dev": "concurrently \"tsup src/index.ts --format cjs,esm --dts --watch\" \"tailwindcss -i ./src/styles/globals.css -o ./dist/styles.css --watch\"", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@taraka/shared": "file:../shared", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "concurrently": "^8.2.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "tsup": "^7.2.0", "typescript": "^5.1.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.0.0"}}