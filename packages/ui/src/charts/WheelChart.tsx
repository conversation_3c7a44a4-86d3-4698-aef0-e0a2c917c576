import React from 'react';
import { <PERSON><PERSON><PERSON> } from '@taraka/shared';

interface WheelChartProps {
  chart: NatalChart;
  size?: number;
}

export const WheelChart: React.FC<WheelChartProps> = ({
  chart,
  size = 300
}) => {
  return (
    <div 
      className="relative bg-gradient-to-br from-indigo-900 to-purple-900 rounded-full flex items-center justify-center text-white"
      style={{ width: size, height: size }}
    >
      <div className="text-center">
        <div className="text-lg font-bold">Wheel Chart</div>
        <div className="text-sm opacity-75">{chart.ascendant.sign} Rising</div>
      </div>
    </div>
  );
};
