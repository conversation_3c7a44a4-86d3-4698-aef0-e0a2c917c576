import React from 'react';
import { <PERSON><PERSON><PERSON>, ChartLayout, Planet } from '@taraka/shared';
import { InteractiveChart } from './InteractiveChart';
import { Wheel<PERSON>hart } from './WheelChart';
import { SquareChart } from './SquareChart';

interface ChartRendererProps {
  chart: <PERSON><PERSON><PERSON>;
  layout?: ChartLayout;
  size?: number;
  interactive?: boolean;
  onPlanetClick?: (planet: Planet) => void;
}

export const ChartRenderer: React.FC<ChartRendererProps> = ({
  chart,
  layout = 'wheel',
  size = 400,
  interactive = true,
  onPlanetClick
}) => {
  if (interactive && layout === 'wheel') {
    return (
      <InteractiveChart
        chart={chart}
        size={size}
        onPlanetClick={onPlanetClick}
      />
    );
  }

  // Fallback to simpler chart layouts
  switch (layout) {
    case 'square':
      return <SquareChart chart={chart} size={size} />;
    case 'wheel':
    default:
      return <WheelChart chart={chart} size={size} />;
  }
};
