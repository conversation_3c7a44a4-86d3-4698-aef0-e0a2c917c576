import React, { useState, useRef, useEffect } from 'react';
import { NatalChart, Planet } from '@taraka/shared';
import { PlanetDetails } from './PlanetDetails';

interface InteractiveChartProps {
  chart: NatalChart;
  size?: number;
  onPlanetClick?: (planet: Planet) => void;
}

export const InteractiveChart: React.FC<InteractiveChartProps> = ({
  chart,
  size = 400,
  onPlanetClick,
}) => {
  const [selectedPlanet, setSelectedPlanet] = useState<Planet | null>(null);
  const [hoveredPlanet, setHoveredPlanet] = useState<Planet | null>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const center = size / 2;
  const outerRadius = size * 0.45;
  const innerRadius = size * 0.25;

  // Calculate planet positions on the wheel
  const getPlanetPosition = (planet: Planet) => {
    const angle = (planet.position.longitude - 90) * (Math.PI / 180); // -90 to start from top
    const radius = outerRadius - 30; // Place planets on outer ring
    
    return {
      x: center + Math.cos(angle) * radius,
      y: center + Math.sin(angle) * radius,
      angle: planet.position.longitude,
    };
  };

  // Calculate house positions
  const getHousePosition = (houseNumber: number) => {
    const angle = ((houseNumber - 1) * 30 - 90) * (Math.PI / 180);
    const radius = (outerRadius + innerRadius) / 2;
    
    return {
      x: center + Math.cos(angle) * radius,
      y: center + Math.sin(angle) * radius,
    };
  };

  // Handle planet click
  const handlePlanetClick = (planet: Planet) => {
    setSelectedPlanet(planet);
    onPlanetClick?.(planet);
  };

  // Generate zodiac wheel path
  const generateZodiacWheel = () => {
    const signs = [
      'Aries', 'Taurus', 'Gemini', 'Cancer',
      'Leo', 'Virgo', 'Libra', 'Scorpio',
      'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'
    ];

    return signs.map((sign, index) => {
      const startAngle = index * 30 - 90;
      const endAngle = (index + 1) * 30 - 90;
      const startRad = startAngle * (Math.PI / 180);
      const endRad = endAngle * (Math.PI / 180);

      const x1 = center + Math.cos(startRad) * innerRadius;
      const y1 = center + Math.sin(startRad) * innerRadius;
      const x2 = center + Math.cos(endRad) * innerRadius;
      const y2 = center + Math.sin(endRad) * innerRadius;
      const x3 = center + Math.cos(endRad) * outerRadius;
      const y3 = center + Math.sin(endRad) * outerRadius;
      const x4 = center + Math.cos(startRad) * outerRadius;
      const y4 = center + Math.sin(startRad) * outerRadius;

      const largeArcFlag = 0; // 30 degrees is always less than 180

      const pathData = [
        `M ${x1} ${y1}`,
        `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        `L ${x3} ${y3}`,
        `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`,
        'Z'
      ].join(' ');

      const textAngle = startAngle + 15; // Middle of the segment
      const textRad = textAngle * (Math.PI / 180);
      const textRadius = (innerRadius + outerRadius) / 2;
      const textX = center + Math.cos(textRad) * textRadius;
      const textY = center + Math.sin(textRad) * textRadius;

      return (
        <g key={sign}>
          <path
            d={pathData}
            fill={index % 2 === 0 ? '#f8fafc' : '#f1f5f9'}
            stroke="#e2e8f0"
            strokeWidth="1"
            className="transition-colors hover:fill-purple-50"
          />
          <text
            x={textX}
            y={textY}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-xs font-medium fill-gray-600"
            transform={`rotate(${textAngle > 90 && textAngle < 270 ? textAngle + 180 : textAngle} ${textX} ${textY})`}
          >
            {sign}
          </text>
        </g>
      );
    });
  };

  // Generate house lines
  const generateHouseLines = () => {
    return Array.from({ length: 12 }, (_, i) => {
      const angle = (i * 30 - 90) * (Math.PI / 180);
      const x1 = center + Math.cos(angle) * innerRadius;
      const y1 = center + Math.sin(angle) * innerRadius;
      const x2 = center + Math.cos(angle) * outerRadius;
      const y2 = center + Math.sin(angle) * outerRadius;

      const housePos = getHousePosition(i + 1);

      return (
        <g key={i}>
          <line
            x1={x1}
            y1={y1}
            x2={x2}
            y2={y2}
            stroke="#cbd5e1"
            strokeWidth="1"
          />
          <text
            x={housePos.x}
            y={housePos.y}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-xs font-bold fill-gray-700"
          >
            {i + 1}
          </text>
        </g>
      );
    });
  };

  // Generate aspect lines
  const generateAspectLines = () => {
    return chart.aspects.map((aspect, index) => {
      const planet1 = chart.planets.find(p => p.id === aspect.planet1);
      const planet2 = chart.planets.find(p => p.id === aspect.planet2);
      
      if (!planet1 || !planet2) return null;

      const pos1 = getPlanetPosition(planet1);
      const pos2 = getPlanetPosition(planet2);

      const aspectColors = {
        conjunction: '#ef4444',
        opposition: '#dc2626',
        trine: '#10b981',
        square: '#f59e0b',
        sextile: '#3b82f6',
        quincunx: '#8b5cf6',
      };

      const color = aspectColors[aspect.type as keyof typeof aspectColors] || '#6b7280';

      return (
        <line
          key={index}
          x1={pos1.x}
          y1={pos1.y}
          x2={pos2.x}
          y2={pos2.y}
          stroke={color}
          strokeWidth="1"
          strokeOpacity="0.6"
          strokeDasharray={aspect.exact ? "none" : "3,3"}
        />
      );
    });
  };

  return (
    <div className="relative">
      <svg
        ref={svgRef}
        width={size}
        height={size}
        className="drop-shadow-lg"
        style={{ background: 'radial-gradient(circle, #faf5ff 0%, #f3e8ff 100%)' }}
      >
        {/* Zodiac wheel */}
        {generateZodiacWheel()}
        
        {/* House lines */}
        {generateHouseLines()}
        
        {/* Aspect lines */}
        {generateAspectLines()}
        
        {/* Planets */}
        {chart.planets.map((planet) => {
          const pos = getPlanetPosition(planet);
          const isSelected = selectedPlanet?.id === planet.id;
          const isHovered = hoveredPlanet?.id === planet.id;
          
          return (
            <g key={planet.id}>
              {/* Planet glow effect */}
              {(isSelected || isHovered) && (
                <circle
                  cx={pos.x}
                  cy={pos.y}
                  r="20"
                  fill="url(#planetGlow)"
                  opacity="0.6"
                  className="animate-pulse"
                />
              )}
              
              {/* Planet circle */}
              <circle
                cx={pos.x}
                cy={pos.y}
                r={isSelected ? "16" : "12"}
                fill={isSelected ? "#8b5cf6" : "#6366f1"}
                stroke={planet.retrograde ? "#ef4444" : "#ffffff"}
                strokeWidth="2"
                className="cursor-pointer transition-all duration-200 hover:scale-110"
                onClick={() => handlePlanetClick(planet)}
                onMouseEnter={() => setHoveredPlanet(planet)}
                onMouseLeave={() => setHoveredPlanet(null)}
              />
              
              {/* Planet symbol */}
              <text
                x={pos.x}
                y={pos.y}
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-sm font-bold fill-white pointer-events-none"
              >
                {planet.symbol}
              </text>
              
              {/* Planet label */}
              <text
                x={pos.x}
                y={pos.y + 25}
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-xs font-medium fill-gray-700 pointer-events-none"
              >
                {planet.name}
              </text>
              
              {/* Retrograde indicator */}
              {planet.retrograde && (
                <text
                  x={pos.x + 18}
                  y={pos.y - 10}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  className="text-xs font-bold fill-red-500 pointer-events-none"
                >
                  R
                </text>
              )}
            </g>
          );
        })}
        
        {/* Gradient definitions */}
        <defs>
          <radialGradient id="planetGlow" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.8" />
            <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0" />
          </radialGradient>
        </defs>
        
        {/* Center circle */}
        <circle
          cx={center}
          cy={center}
          r={innerRadius}
          fill="url(#centerGradient)"
          stroke="#e2e8f0"
          strokeWidth="2"
        />
        
        {/* Ascendant marker */}
        <g>
          <line
            x1={center}
            y1={center - outerRadius}
            x2={center}
            y2={center - outerRadius - 15}
            stroke="#8b5cf6"
            strokeWidth="3"
            markerEnd="url(#arrowhead)"
          />
          <text
            x={center}
            y={center - outerRadius - 25}
            textAnchor="middle"
            className="text-sm font-bold fill-purple-600"
          >
            ASC
          </text>
        </g>
        
        <defs>
          <linearGradient id="centerGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" stopColor="#ffffff" />
            <stop offset="100%" stopColor="#f8fafc" />
          </linearGradient>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill="#8b5cf6"
            />
          </marker>
        </defs>
      </svg>
      
      {/* Planet details panel */}
      {selectedPlanet && (
        <div className="absolute top-0 right-0 transform translate-x-full ml-4">
          <PlanetDetails
            planet={selectedPlanet}
            onClose={() => setSelectedPlanet(null)}
          />
        </div>
      )}
    </div>
  );
};
