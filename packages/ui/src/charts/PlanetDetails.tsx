import React from 'react';
import { Planet } from '@taraka/shared';
import { Card } from '../components/Card';
import { Button } from '../components/Button';

interface PlanetDetailsProps {
  planet: Planet;
  onClose: () => void;
}

export const PlanetDetails: React.FC<PlanetDetailsProps> = ({ planet, onClose }) => {
  const getPlanetMeaning = (planetName: string): { keywords: string[]; description: string } => {
    const meanings: Record<string, { keywords: string[]; description: string }> = {
      Sun: {
        keywords: ['Identity', 'Ego', 'Vitality', 'Leadership', 'Self-expression'],
        description: 'Represents your core identity, ego, and life force. The Sun shows how you express your essential self and what drives your sense of purpose.'
      },
      Moon: {
        keywords: ['Emotions', 'Intuition', 'Subconscious', 'Nurturing', 'Habits'],
        description: 'Governs your emotional nature, instincts, and subconscious patterns. The Moon reveals your inner world and how you process feelings.'
      },
      Mercury: {
        keywords: ['Communication', 'Intellect', 'Learning', 'Travel', 'Technology'],
        description: 'Rules communication, thinking, and information processing. <PERSON> shows how you learn, communicate, and connect with others.'
      },
      Venus: {
        keywords: ['Love', 'Beauty', 'Harmony', 'Values', 'Relationships'],
        description: 'Governs love, beauty, and what you value. Venus reveals your approach to relationships and what brings you pleasure and harmony.'
      },
      Mars: {
        keywords: ['Action', 'Energy', 'Passion', 'Courage', 'Conflict'],
        description: 'Represents your drive, ambition, and how you assert yourself. Mars shows your approach to action, competition, and pursuing desires.'
      },
      Jupiter: {
        keywords: ['Expansion', 'Wisdom', 'Growth', 'Optimism', 'Philosophy'],
        description: 'The planet of growth, expansion, and higher learning. Jupiter shows where you seek meaning, wisdom, and opportunities for growth.'
      },
      Saturn: {
        keywords: ['Discipline', 'Structure', 'Responsibility', 'Limitations', 'Mastery'],
        description: 'Represents structure, discipline, and life lessons. Saturn shows where you face challenges and develop mastery through perseverance.'
      },
      Uranus: {
        keywords: ['Innovation', 'Freedom', 'Rebellion', 'Originality', 'Change'],
        description: 'The planet of innovation and sudden change. Uranus shows where you seek freedom, express originality, and break from convention.'
      },
      Neptune: {
        keywords: ['Spirituality', 'Dreams', 'Illusion', 'Compassion', 'Creativity'],
        description: 'Governs spirituality, dreams, and imagination. Neptune shows your connection to the divine and areas of inspiration or confusion.'
      },
      Pluto: {
        keywords: ['Transformation', 'Power', 'Regeneration', 'Intensity', 'Rebirth'],
        description: 'The planet of transformation and rebirth. Pluto shows where you experience deep change, power dynamics, and psychological growth.'
      }
    };

    return meanings[planetName] || {
      keywords: ['Mystery', 'Unknown'],
      description: 'This celestial body holds unique significance in your chart.'
    };
  };

  const getSignMeaning = (sign: string): string => {
    const signMeanings: Record<string, string> = {
      Aries: 'Bold, pioneering, and action-oriented energy',
      Taurus: 'Stable, practical, and sensual expression',
      Gemini: 'Curious, communicative, and adaptable nature',
      Cancer: 'Nurturing, intuitive, and emotionally sensitive',
      Leo: 'Creative, confident, and dramatic expression',
      Virgo: 'Analytical, perfectionist, and service-oriented',
      Libra: 'Harmonious, diplomatic, and relationship-focused',
      Scorpio: 'Intense, transformative, and deeply emotional',
      Sagittarius: 'Adventurous, philosophical, and freedom-loving',
      Capricorn: 'Ambitious, disciplined, and goal-oriented',
      Aquarius: 'Independent, innovative, and humanitarian',
      Pisces: 'Compassionate, intuitive, and spiritually inclined'
    };

    return signMeanings[sign] || 'Unique cosmic influence';
  };

  const getHouseMeaning = (house: number): string => {
    const houseMeanings: Record<number, string> = {
      1: 'Self, identity, and first impressions',
      2: 'Values, possessions, and self-worth',
      3: 'Communication, siblings, and short trips',
      4: 'Home, family, and emotional foundation',
      5: 'Creativity, romance, and self-expression',
      6: 'Work, health, and daily routines',
      7: 'Partnerships, marriage, and open enemies',
      8: 'Transformation, shared resources, and mysteries',
      9: 'Philosophy, higher education, and long journeys',
      10: 'Career, reputation, and public image',
      11: 'Friends, groups, and hopes and dreams',
      12: 'Spirituality, subconscious, and hidden things'
    };

    return houseMeanings[house] || 'Cosmic influence area';
  };

  const meaning = getPlanetMeaning(planet.name);

  return (
    <div className="w-80">
      <Card variant="elevated" className="relative">
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="absolute top-2 right-2 p-1"
        >
          ✕
        </Button>

        <div className="mb-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="text-3xl">{planet.symbol}</div>
            <div>
              <h3 className="text-xl font-bold text-gray-800">{planet.name}</h3>
              <p className="text-sm text-gray-600">
                {planet.position.degree}°{planet.position.minute}' {planet.position.sign}
              </p>
            </div>
          </div>

          {planet.retrograde && (
            <div className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">
              <span className="w-2 h-2 bg-red-500 rounded-full"></span>
              Retrograde
            </div>
          )}
        </div>

        <div className="space-y-4">
          {/* Position Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Sign:</span>
              <p className="text-gray-600">{planet.position.sign}</p>
            </div>
            <div>
              <span className="font-medium text-gray-700">House:</span>
              <p className="text-gray-600">{planet.position.house}</p>
            </div>
          </div>

          {/* Keywords */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Keywords</h4>
            <div className="flex flex-wrap gap-1">
              {meaning.keywords.map((keyword, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs"
                >
                  {keyword}
                </span>
              ))}
            </div>
          </div>

          {/* Description */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Meaning</h4>
            <p className="text-sm text-gray-600 leading-relaxed">
              {meaning.description}
            </p>
          </div>

          {/* Sign Influence */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">In {planet.position.sign}</h4>
            <p className="text-sm text-gray-600 leading-relaxed">
              {getSignMeaning(planet.position.sign)}
            </p>
          </div>

          {/* House Influence */}
          <div>
            <h4 className="font-medium text-gray-700 mb-2">House {planet.position.house}</h4>
            <p className="text-sm text-gray-600 leading-relaxed">
              {getHouseMeaning(planet.position.house)}
            </p>
          </div>

          {/* Technical Details */}
          <div className="pt-3 border-t border-gray-200">
            <h4 className="font-medium text-gray-700 mb-2">Technical Details</h4>
            <div className="text-xs text-gray-500 space-y-1">
              <div>Longitude: {planet.position.longitude.toFixed(2)}°</div>
              <div>Latitude: {planet.position.latitude.toFixed(2)}°</div>
              {planet.retrograde && (
                <div className="text-red-600">
                  ⚠️ This planet appears to move backward from Earth's perspective
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
