import React from 'react';
import { <PERSON><PERSON><PERSON> } from '@taraka/shared';

interface SquareChartProps {
  chart: NatalChart;
  size?: number;
}

export const SquareChart: React.FC<SquareChartProps> = ({
  chart,
  size = 300
}) => {
  return (
    <div 
      className="relative bg-gradient-to-br from-purple-900 to-indigo-900 flex items-center justify-center text-white"
      style={{ width: size, height: size }}
    >
      <div className="text-center">
        <div className="text-lg font-bold">Square Chart</div>
        <div className="text-sm opacity-75">{chart.ascendant.sign} Rising</div>
      </div>
    </div>
  );
};
