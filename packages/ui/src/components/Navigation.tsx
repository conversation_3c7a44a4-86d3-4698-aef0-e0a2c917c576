import React from 'react';

interface NavigationProps {
  title?: string;
}

export const Navigation: React.FC<NavigationProps> = ({ title = 'Taraka' }) => {
  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
          </div>
        </div>
      </div>
    </nav>
  );
};
