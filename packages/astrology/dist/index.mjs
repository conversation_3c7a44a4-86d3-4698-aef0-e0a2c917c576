// src/engines/western.ts
var WesternAstrologyEngine = class {
  static calculatePlanets(birthData) {
    return [
      {
        id: "sun",
        name: "<PERSON>",
        symbol: "\u2609",
        position: {
          longitude: 120.5,
          latitude: 0,
          sign: "<PERSON>",
          house: 5,
          degree: 0,
          minute: 30
        },
        retrograde: false
      },
      {
        id: "moon",
        name: "<PERSON>",
        symbol: "\u263D",
        position: {
          longitude: 45.2,
          latitude: 0,
          sign: "Taurus",
          house: 2,
          degree: 15,
          minute: 12
        },
        retrograde: false
      }
    ];
  }
  static calculateHouses(birthData) {
    return Array.from({ length: 12 }, (_, i) => ({
      number: i + 1,
      sign: "<PERSON><PERSON>",
      // This would be calculated based on birth time/location
      cusp: i * 30,
      ruler: "Mars"
    }));
  }
};

// src/engines/vedic.ts
var VedicAstrologyEngine = class {
  static calculatePlanets(birthData) {
    return [
      {
        id: "sun",
        name: "<PERSON><PERSON>",
        symbol: "\u2609",
        position: {
          longitude: 96.5,
          // Adjusted for ayanamsa
          latitude: 0,
          sign: "Cancer",
          // Different from Western due to sidereal calculation
          house: 4,
          degree: 6,
          minute: 30
        },
        retrograde: false
      }
    ];
  }
};

// src/engines/mayan.ts
var MayanAstrologyEngine = class {
  static calculateTzolkin(birthData) {
    return {
      daySign: "Imix",
      number: 1,
      trecena: "Imix",
      description: "The Crocodile - New beginnings and primal energy"
    };
  }
};

// src/calculations/planets.ts
var calculatePlanetPositions = (birthData) => {
  return [
    {
      id: "sun",
      name: "Sun",
      symbol: "\u2609",
      position: {
        longitude: 120.5,
        latitude: 0,
        sign: "Leo",
        house: 5,
        degree: 0,
        minute: 30
      },
      retrograde: false
    }
  ];
};

// src/calculations/houses.ts
var calculateHouses = (birthData) => {
  return Array.from({ length: 12 }, (_, i) => ({
    number: i + 1,
    sign: "Aries",
    cusp: i * 30,
    ruler: "Mars"
  }));
};

// src/calculations/aspects.ts
var calculateAspects = (planets) => {
  return [
    {
      planet1: "sun",
      planet2: "moon",
      type: "trine",
      orb: 2.5,
      exact: false
    }
  ];
};

// src/interpretations/generator.ts
var generateInterpretations = (chart, mode = "reflective") => {
  return [
    {
      id: "sun_leo_interpretation",
      type: "planet",
      target: "sun",
      mode,
      system: chart.system,
      content: {
        title: "Sun in Leo",
        description: "Your core essence radiates with creative fire and natural leadership.",
        keywords: ["leadership", "creativity", "confidence"],
        themes: ["growth", "creativity"]
      },
      metadata: {
        source: "taraka_engine",
        confidence: 0.85
      }
    }
  ];
};

// src/interpretations/templates.ts
var interpretationTemplates = {
  western: {
    reflective: {
      sun: {
        aries: "Your inner fire burns bright with pioneering spirit...",
        leo: "Your core essence radiates with creative confidence..."
        // Add more signs as needed
      }
    },
    traditional: {
      sun: {
        aries: "The Sun in Aries bestows natural leadership...",
        leo: "The Sun in Leo grants royal bearing and creative power..."
        // Add more signs as needed
      }
    }
  },
  vedic: {
    // Vedic interpretations would go here
  }
};

// src/chart-builder.ts
var ChartBuilder = class {
  static async buildChart(birthData, system = "western") {
    const mockChart = {
      id: `chart_${Date.now()}`,
      userId: "temp_user",
      birthData,
      system,
      planets: [
        {
          id: "sun",
          name: "Sun",
          symbol: "\u2609",
          position: {
            longitude: 120.5,
            latitude: 0,
            sign: "Leo",
            house: 5,
            degree: 0,
            minute: 30
          },
          retrograde: false
        },
        {
          id: "moon",
          name: "Moon",
          symbol: "\u263D",
          position: {
            longitude: 45.2,
            latitude: 0,
            sign: "Taurus",
            house: 2,
            degree: 15,
            minute: 12
          },
          retrograde: false
        }
      ],
      houses: Array.from({ length: 12 }, (_, i) => ({
        number: i + 1,
        sign: "Aries",
        // Placeholder
        cusp: i * 30,
        ruler: "Mars"
        // Placeholder
      })),
      aspects: [
        {
          planet1: "sun",
          planet2: "moon",
          type: "trine",
          orb: 2.5,
          exact: false
        }
      ],
      ascendant: {
        sign: "Aries",
        degree: 15
      },
      midheaven: {
        sign: "Capricorn",
        degree: 10
      },
      calculatedAt: /* @__PURE__ */ new Date()
    };
    return mockChart;
  }
};
export {
  ChartBuilder,
  MayanAstrologyEngine,
  VedicAstrologyEngine,
  WesternAstrologyEngine,
  calculateAspects,
  calculateHouses,
  calculatePlanetPositions,
  generateInterpretations,
  interpretationTemplates
};
