// src/engines/western.ts
var WesternAstrologyEngine = class {
  static calculatePlanets(birthData) {
    return [
      {
        id: "sun",
        name: "<PERSON>",
        symbol: "\u2609",
        position: {
          longitude: 120.5,
          latitude: 0,
          sign: "<PERSON>",
          house: 5,
          degree: 0,
          minute: 30
        },
        retrograde: false
      },
      {
        id: "moon",
        name: "<PERSON>",
        symbol: "\u263D",
        position: {
          longitude: 45.2,
          latitude: 0,
          sign: "Taurus",
          house: 2,
          degree: 15,
          minute: 12
        },
        retrograde: false
      }
    ];
  }
  static calculateHouses(birthData) {
    return Array.from({ length: 12 }, (_, i) => ({
      number: i + 1,
      sign: "<PERSON><PERSON>",
      // This would be calculated based on birth time/location
      cusp: i * 30,
      ruler: "Mars"
    }));
  }
};

// src/engines/vedic.ts
var VedicAstrologyEngine = class {
  static calculatePlanets(birthData) {
    return [
      {
        id: "sun",
        name: "<PERSON><PERSON>",
        symbol: "\u2609",
        position: {
          longitude: 96.5,
          // Adjusted for ayanamsa
          latitude: 0,
          sign: "Cancer",
          // Different from Western due to sidereal calculation
          house: 4,
          degree: 6,
          minute: 30
        },
        retrograde: false
      }
    ];
  }
};

// src/engines/mayan.ts
var MayanAstrologyEngine = class {
  static calculateTzolkin(birthData) {
    return {
      daySign: "Imix",
      number: 1,
      trecena: "Imix",
      description: "The Crocodile - New beginnings and primal energy"
    };
  }
};

// src/calculations/planets.ts
var calculatePlanetPositions = (birthData) => {
  return [
    {
      id: "sun",
      name: "Sun",
      symbol: "\u2609",
      position: {
        longitude: 120.5,
        latitude: 0,
        sign: "Leo",
        house: 5,
        degree: 0,
        minute: 30
      },
      retrograde: false
    }
  ];
};

// src/calculations/houses.ts
var calculateHouses = (birthData) => {
  return Array.from({ length: 12 }, (_, i) => ({
    number: i + 1,
    sign: "Aries",
    cusp: i * 30,
    ruler: "Mars"
  }));
};

// src/calculations/aspects.ts
var calculateAspects = (planets) => {
  return [
    {
      planet1: "sun",
      planet2: "moon",
      type: "trine",
      orb: 2.5,
      exact: false
    }
  ];
};

// src/interpretations/generator.ts
var generateInterpretations = (chart, mode = "reflective") => {
  return [
    {
      id: "sun_leo_interpretation",
      type: "planet",
      target: "sun",
      mode,
      system: chart.system,
      content: {
        title: "Sun in Leo",
        description: "Your core essence radiates with creative fire and natural leadership.",
        keywords: ["leadership", "creativity", "confidence"],
        themes: ["growth", "creativity"]
      },
      metadata: {
        source: "taraka_engine",
        confidence: 0.85
      }
    }
  ];
};

// src/interpretations/templates.ts
var interpretationTemplates = {
  western: {
    reflective: {
      sun: {
        aries: "Your inner fire burns bright with pioneering spirit...",
        leo: "Your core essence radiates with creative confidence..."
        // Add more signs as needed
      }
    },
    traditional: {
      sun: {
        aries: "The Sun in Aries bestows natural leadership...",
        leo: "The Sun in Leo grants royal bearing and creative power..."
        // Add more signs as needed
      }
    }
  },
  vedic: {
    // Vedic interpretations would go here
  }
};

// src/calculations/astronomical.ts
var ZODIAC_SIGNS = [
  "Aries",
  "Taurus",
  "Gemini",
  "Cancer",
  "Leo",
  "Virgo",
  "Libra",
  "Scorpio",
  "Sagittarius",
  "Capricorn",
  "Aquarius",
  "Pisces"
];
var PLANET_SYMBOLS = {
  Sun: "\u2609",
  Moon: "\u263D",
  Mercury: "\u263F",
  Venus: "\u2640",
  Mars: "\u2642",
  Jupiter: "\u2643",
  Saturn: "\u2644",
  Uranus: "\u2645",
  Neptune: "\u2646",
  Pluto: "\u2647"
};
var AstronomicalCalculator = class {
  /**
   * Calculate planetary positions using simplified astronomical calculations
   */
  static calculatePlanetaryPositions(birthData) {
    const birthDateTime = new Date(birthData.date);
    birthDateTime.setHours(birthData.time.hour, birthData.time.minute, 0, 0);
    const planets = [];
    const planetNames = ["Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter", "Saturn", "Uranus", "Neptune", "Pluto"];
    planetNames.forEach((planetName) => {
      try {
        const longitude = this.calculatePlanetLongitude(planetName, birthDateTime);
        const signIndex = Math.floor(longitude / 30);
        const sign = ZODIAC_SIGNS[signIndex];
        const degreeInSign = longitude % 30;
        const degree = Math.floor(degreeInSign);
        const minute = Math.floor((degreeInSign - degree) * 60);
        const house = this.calculateHousePosition(longitude, birthData);
        const isRetrograde = this.isRetrograde(planetName, birthDateTime);
        planets.push({
          id: planetName.toLowerCase(),
          name: planetName,
          symbol: PLANET_SYMBOLS[planetName] || "\u25CF",
          position: {
            longitude,
            latitude: 0,
            // Simplified - assume on ecliptic
            sign,
            house,
            degree,
            minute
          },
          retrograde: isRetrograde
        });
      } catch (error) {
        console.warn(`Failed to calculate position for ${planetName}:`, error);
        planets.push(this.getFallbackPlanetPosition(planetName, birthData));
      }
    });
    return planets;
  }
  /**
   * Calculate approximate planet longitude based on simplified orbital mechanics
   */
  static calculatePlanetLongitude(planetName, date) {
    const daysSinceEpoch = (date.getTime() - (/* @__PURE__ */ new Date("2000-01-01")).getTime()) / (1e3 * 60 * 60 * 24);
    const planetData = {
      Sun: { period: 365.25, startLongitude: 280, speed: 0.9856 },
      Moon: { period: 27.32, startLongitude: 0, speed: 13.176 },
      Mercury: { period: 87.97, startLongitude: 252, speed: 4.092 },
      Venus: { period: 224.7, startLongitude: 181, speed: 1.602 },
      Mars: { period: 686.98, startLongitude: 355, speed: 0.524 },
      Jupiter: { period: 4332.59, startLongitude: 34, speed: 0.083 },
      Saturn: { period: 10759.22, startLongitude: 50, speed: 0.033 },
      Uranus: { period: 30688.5, startLongitude: 314, speed: 0.012 },
      Neptune: { period: 60182, startLongitude: 304, speed: 6e-3 },
      Pluto: { period: 90560, startLongitude: 238, speed: 4e-3 }
    };
    const data = planetData[planetName];
    if (!data)
      return 0;
    let longitude = data.startLongitude + daysSinceEpoch * data.speed;
    longitude = (longitude % 360 + 360) % 360;
    return longitude;
  }
  /**
   * Calculate house positions using Placidus system
   */
  static calculateHouses(birthData) {
    const houses = [];
    try {
      const lst = this.calculateLocalSiderealTime(birthData);
      const ascendant = this.calculateAscendant(birthData, lst);
      for (let i = 1; i <= 12; i++) {
        const cuspLongitude = this.calculateHouseCusp(i, ascendant, birthData);
        const signIndex = Math.floor(cuspLongitude / 30);
        const sign = ZODIAC_SIGNS[signIndex];
        houses.push({
          number: i,
          sign,
          cusp: cuspLongitude,
          ruler: this.getSignRuler(sign)
        });
      }
    } catch (error) {
      console.warn("Failed to calculate houses, using fallback:", error);
      return this.getFallbackHouses();
    }
    return houses;
  }
  /**
   * Calculate ascendant (rising sign)
   */
  static calculateAscendant(birthData) {
    try {
      const birthDateTime = new Date(birthData.date);
      birthDateTime.setHours(birthData.time.hour, birthData.time.minute, 0, 0);
      const lst = this.calculateLocalSiderealTime(birthData);
      const lat = birthData.location.coordinates.latitude * Math.PI / 180;
      const ascendantLongitude = Math.atan2(
        Math.cos(lst),
        -(Math.sin(lst) * Math.cos(23.44 * Math.PI / 180) + Math.tan(lat) * Math.sin(23.44 * Math.PI / 180))
      ) * 180 / Math.PI;
      const normalizedLongitude = (ascendantLongitude % 360 + 360) % 360;
      const signIndex = Math.floor(normalizedLongitude / 30);
      const sign = ZODIAC_SIGNS[signIndex];
      const degree = normalizedLongitude % 30;
      return { sign, degree };
    } catch (error) {
      console.warn("Failed to calculate ascendant:", error);
      return { sign: "Aries", degree: 0 };
    }
  }
  /**
   * Calculate local sidereal time
   */
  static calculateLocalSiderealTime(birthData) {
    const birthDateTime = new Date(birthData.date);
    birthDateTime.setHours(birthData.time.hour, birthData.time.minute, 0, 0);
    const jd = this.toJulianDay(birthDateTime);
    const gst = this.calculateGreenwichSiderealTime(jd);
    const longitude = birthData.location.coordinates.longitude;
    const lst = gst + longitude / 15;
    return (lst % 24 + 24) % 24;
  }
  /**
   * Convert date to Julian day number
   */
  static toJulianDay(date) {
    const a = Math.floor((14 - (date.getMonth() + 1)) / 12);
    const y = date.getFullYear() + 4800 - a;
    const m = date.getMonth() + 1 + 12 * a - 3;
    return date.getDate() + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) - 32045 + (date.getHours() + date.getMinutes() / 60 + date.getSeconds() / 3600) / 24;
  }
  /**
   * Calculate Greenwich sidereal time
   */
  static calculateGreenwichSiderealTime(jd) {
    const t = (jd - 2451545) / 36525;
    let gst = 280.46061837 + 360.98564736629 * (jd - 2451545) + 387933e-9 * t * t - t * t * t / 3871e4;
    return (gst % 360 + 360) % 360 / 15;
  }
  /**
   * Calculate house cusp using simplified Placidus system
   */
  static calculateHouseCusp(houseNumber, ascendant, birthData) {
    const baseAngle = (houseNumber - 1) * 30;
    return (ascendant.degree + baseAngle) % 360;
  }
  /**
   * Calculate house position for a planet
   */
  static calculateHousePosition(longitude, birthData) {
    const ascendant = this.calculateAscendant(birthData);
    const relativePosition = (longitude - ascendant.degree + 360) % 360;
    return Math.floor(relativePosition / 30) + 1;
  }
  /**
   * Check if planet is retrograde (simplified)
   */
  static isRetrograde(planetName, date) {
    if (planetName === "Sun" || planetName === "Moon")
      return false;
    const retrogradeChances = {
      Mercury: 0.2,
      Venus: 0.07,
      Mars: 0.1,
      Jupiter: 0.3,
      Saturn: 0.35,
      Uranus: 0.4,
      Neptune: 0.4,
      Pluto: 0.4
    };
    const chance = retrogradeChances[planetName] || 0;
    return Math.random() < chance;
  }
  /**
   * Get sign ruler
   */
  static getSignRuler(sign) {
    const rulers = {
      "Aries": "Mars",
      "Taurus": "Venus",
      "Gemini": "Mercury",
      "Cancer": "Moon",
      "Leo": "Sun",
      "Virgo": "Mercury",
      "Libra": "Venus",
      "Scorpio": "Mars",
      "Sagittarius": "Jupiter",
      "Capricorn": "Saturn",
      "Aquarius": "Saturn",
      "Pisces": "Jupiter"
    };
    return rulers[sign] || "Unknown";
  }
  /**
   * Fallback planet position if calculation fails
   */
  static getFallbackPlanetPosition(planetName, birthData) {
    const fallbackLongitudes = {
      Sun: 120,
      Moon: 45,
      Mercury: 110,
      Venus: 130,
      Mars: 200,
      Jupiter: 300,
      Saturn: 250,
      Uranus: 15,
      Neptune: 340,
      Pluto: 280
    };
    const longitude = fallbackLongitudes[planetName] || 0;
    const signIndex = Math.floor(longitude / 30);
    const sign = ZODIAC_SIGNS[signIndex];
    return {
      id: planetName.toLowerCase(),
      name: planetName,
      symbol: PLANET_SYMBOLS[planetName] || "\u25CF",
      position: {
        longitude,
        latitude: 0,
        sign,
        house: Math.floor(Math.random() * 12) + 1,
        degree: Math.floor(longitude % 30),
        minute: 0
      },
      retrograde: false
    };
  }
  /**
   * Fallback houses if calculation fails
   */
  static getFallbackHouses() {
    return Array.from({ length: 12 }, (_, i) => ({
      number: i + 1,
      sign: ZODIAC_SIGNS[i % 12],
      cusp: i * 30,
      ruler: this.getSignRuler(ZODIAC_SIGNS[i % 12])
    }));
  }
};

// src/insights/daily-generator.ts
var DailyInsightGenerator = class {
  /**
   * Generate daily insight for a user
   */
  static async generateDailyInsight(birthData, date = /* @__PURE__ */ new Date(), mode = "reflective", system = "western") {
    try {
      const transits = this.calculateTransits(birthData, date);
      const themes = this.determineDominantThemes(transits, mode);
      const content = this.generateInsightContent(transits, themes, mode, system);
      return {
        id: `insight_${Date.now()}`,
        userId: "temp_user",
        date,
        mode,
        system,
        themes,
        content,
        transits,
        createdAt: /* @__PURE__ */ new Date()
      };
    } catch (error) {
      console.error("Error generating daily insight:", error);
      return this.generateFallbackInsight(date, mode, system);
    }
  }
  /**
   * Calculate current planetary transits
   */
  static calculateTransits(birthData, date) {
    const currentPositions = AstronomicalCalculator.calculatePlanetaryPositions({
      ...birthData,
      date,
      time: { hour: 12, minute: 0 }
      // Use noon for daily transits
    });
    const natalPositions = AstronomicalCalculator.calculatePlanetaryPositions(birthData);
    const transits = [];
    for (const currentPlanet of currentPositions) {
      for (const natalPlanet of natalPositions) {
        const orb = this.calculateOrb(currentPlanet.position.longitude, natalPlanet.position.longitude);
        const aspect = this.determineAspect(orb);
        if (aspect && orb <= this.getAspectOrb(aspect)) {
          transits.push({
            planet: currentPlanet.name,
            aspect,
            target: natalPlanet.name,
            influence: this.determineInfluence(aspect, currentPlanet.name, natalPlanet.name),
            orb,
            exact: orb <= 1
          });
        }
      }
    }
    return transits;
  }
  /**
   * Calculate orb between two planetary positions
   */
  static calculateOrb(pos1, pos2) {
    let diff = Math.abs(pos1 - pos2);
    if (diff > 180)
      diff = 360 - diff;
    return diff;
  }
  /**
   * Determine aspect type from orb
   */
  static determineAspect(orb) {
    const aspects = [
      { name: "conjunction", angle: 0, orb: 8 },
      { name: "sextile", angle: 60, orb: 6 },
      { name: "square", angle: 90, orb: 8 },
      { name: "trine", angle: 120, orb: 8 },
      { name: "opposition", angle: 180, orb: 8 }
    ];
    for (const aspect of aspects) {
      if (Math.abs(orb - aspect.angle) <= aspect.orb || Math.abs(orb - (360 - aspect.angle)) <= aspect.orb) {
        return aspect.name;
      }
    }
    return null;
  }
  /**
   * Get orb tolerance for aspect
   */
  static getAspectOrb(aspect) {
    const orbs = {
      conjunction: 8,
      sextile: 6,
      square: 8,
      trine: 8,
      opposition: 8
    };
    return orbs[aspect] || 5;
  }
  /**
   * Determine influence type
   */
  static determineInfluence(aspect, transitPlanet, natalPlanet) {
    const harmonious = ["trine", "sextile"];
    const challenging = ["square", "opposition"];
    const neutral = ["conjunction"];
    if (harmonious.includes(aspect))
      return "positive";
    if (challenging.includes(aspect))
      return "challenging";
    return "neutral";
  }
  /**
   * Determine dominant themes for the day
   */
  static determineDominantThemes(transits, mode) {
    const themeWeights = {
      love: 0,
      career: 0,
      emotions: 0,
      growth: 0,
      creativity: 0,
      challenge: 0
    };
    for (const transit of transits) {
      switch (transit.planet) {
        case "Venus":
          themeWeights.love += 2;
          themeWeights.creativity += 1;
          break;
        case "Mars":
          themeWeights.career += 2;
          themeWeights.challenge += 1;
          break;
        case "Moon":
          themeWeights.emotions += 3;
          break;
        case "Jupiter":
          themeWeights.growth += 2;
          themeWeights.career += 1;
          break;
        case "Saturn":
          themeWeights.challenge += 2;
          themeWeights.career += 1;
          break;
        case "Mercury":
          themeWeights.career += 1;
          themeWeights.creativity += 1;
          break;
      }
      if (transit.influence === "challenging") {
        themeWeights.challenge += 1;
      } else if (transit.influence === "positive") {
        themeWeights.growth += 1;
      }
    }
    const sortedThemes = Object.entries(themeWeights).sort(([, a], [, b]) => b - a).slice(0, 3).filter(([, weight]) => weight > 0).map(([theme]) => theme);
    return sortedThemes.length > 0 ? sortedThemes : ["growth"];
  }
  /**
   * Generate insight content based on mode and themes
   */
  static generateInsightContent(transits, themes, mode, system) {
    const primaryTheme = themes[0] || "growth";
    if (mode === "reflective") {
      return this.generateReflectiveContent(transits, themes, primaryTheme);
    } else {
      return this.generateTraditionalContent(transits, themes, primaryTheme);
    }
  }
  /**
   * Generate reflective mode content
   */
  static generateReflectiveContent(transits, themes, primaryTheme) {
    const reflectiveTemplates = {
      love: {
        title: "Heart Connections Today",
        summary: "Your heart space is activated today, inviting deeper connections and self-love.",
        detailed: "The cosmic energies today are illuminating your relationship patterns and capacity for love. This is a beautiful time to reflect on how you give and receive affection, and what authentic connection means to you.",
        reflection_prompt: "How can I show more compassion to myself and others today? What does love mean to me right now?"
      },
      career: {
        title: "Purpose and Path",
        summary: "Your professional energy is highlighted, encouraging alignment with your true calling.",
        detailed: "The universe is supporting your career growth and professional development today. Consider how your work aligns with your deeper purpose and values.",
        reflection_prompt: "What aspects of my work bring me the most fulfillment? How can I better align my career with my authentic self?"
      },
      emotions: {
        title: "Inner Landscape",
        summary: "Your emotional world is rich and active today, offering opportunities for healing and growth.",
        detailed: "Today's energies invite you to explore your inner emotional landscape with curiosity and compassion. Your feelings are valid messengers.",
        reflection_prompt: "What emotions am I experiencing today? What might they be trying to tell me about my needs and desires?"
      },
      growth: {
        title: "Expansion and Evolution",
        summary: "You're in a powerful growth phase, with opportunities for personal development and expansion.",
        detailed: "The cosmic currents are supporting your personal evolution today. Trust in your ability to grow and transform.",
        reflection_prompt: "In what ways am I growing and evolving? What new aspects of myself am I ready to embrace?"
      },
      creativity: {
        title: "Creative Flow",
        summary: "Your creative channels are open and flowing, inviting artistic expression and innovation.",
        detailed: "Today's energies support creative expression and innovative thinking. Allow your imagination to guide you.",
        reflection_prompt: "How can I express my creativity today? What wants to be created through me?"
      },
      challenge: {
        title: "Growth Through Challenge",
        summary: "Today's obstacles are opportunities for strength-building and wisdom-gathering.",
        detailed: "The challenges you face today are cosmic invitations to develop resilience and discover your inner strength.",
        reflection_prompt: "What is this challenge teaching me? How can I approach difficulties with grace and wisdom?"
      }
    };
    return reflectiveTemplates[primaryTheme];
  }
  /**
   * Generate traditional mode content
   */
  static generateTraditionalContent(transits, themes, primaryTheme) {
    const traditionalTemplates = {
      love: {
        title: "Venus Influences Your Relationships",
        summary: "Favorable aspects for romance and partnerships are present today.",
        detailed: "The planetary alignments today favor matters of the heart. Venus's influence brings harmony to relationships and opportunities for romantic connections."
      },
      career: {
        title: "Professional Advancement Favored",
        summary: "The stars align to support your career ambitions and professional goals.",
        detailed: "Mars and Jupiter create favorable conditions for career advancement. This is an auspicious time for important business decisions and professional initiatives."
      },
      emotions: {
        title: "Lunar Influences on Mood",
        summary: "The Moon's position affects your emotional state and intuitive abilities.",
        detailed: "Today's lunar aspects heighten emotional sensitivity and psychic receptivity. Pay attention to your dreams and intuitive insights."
      },
      growth: {
        title: "Jupiter Brings Expansion",
        summary: "Beneficial Jupiter aspects promote growth, learning, and good fortune.",
        detailed: "Jupiter's benevolent influence expands your horizons and brings opportunities for advancement in education, travel, or spiritual pursuits."
      },
      creativity: {
        title: "Mercury Enhances Creative Expression",
        summary: "Mental agility and creative communication are highlighted today.",
        detailed: "Mercury's favorable position enhances your ability to communicate ideas and express creativity through writing, speaking, or artistic endeavors."
      },
      challenge: {
        title: "Saturn Tests Your Resolve",
        summary: "Challenging aspects require patience, discipline, and perseverance.",
        detailed: "Saturn's influence brings tests and obstacles that ultimately strengthen your character. Approach challenges with patience and methodical planning."
      }
    };
    return traditionalTemplates[primaryTheme];
  }
  /**
   * Generate fallback insight if calculations fail
   */
  static generateFallbackInsight(date, mode, system) {
    return {
      id: `insight_${Date.now()}`,
      userId: "temp_user",
      date,
      mode,
      system,
      themes: ["growth"],
      content: {
        title: "Cosmic Reflection",
        summary: "Today offers opportunities for growth and self-discovery.",
        detailed: "The universe continues to support your journey of growth and self-discovery. Take time today to connect with your inner wisdom and trust in your path.",
        reflection_prompt: mode === "reflective" ? "What is my heart calling me to explore today?" : void 0
      },
      transits: [],
      createdAt: /* @__PURE__ */ new Date()
    };
  }
};

// src/chart-builder.ts
var ChartBuilder = class {
  static async buildChart(birthData, system = "western") {
    try {
      const planets = AstronomicalCalculator.calculatePlanetaryPositions(birthData);
      const houses = AstronomicalCalculator.calculateHouses(birthData);
      const ascendant = AstronomicalCalculator.calculateAscendant(birthData);
      const midheaven = this.calculateMidheaven(ascendant, birthData);
      const aspects = calculateAspects(planets);
      const adjustedData = this.applySystemAdjustments(
        { planets, houses, ascendant, midheaven },
        system,
        birthData
      );
      const chart = {
        id: `chart_${Date.now()}`,
        userId: "temp_user",
        birthData,
        system,
        planets: adjustedData.planets,
        houses: adjustedData.houses,
        aspects,
        ascendant: adjustedData.ascendant,
        midheaven: adjustedData.midheaven,
        calculatedAt: /* @__PURE__ */ new Date()
      };
      return chart;
    } catch (error) {
      console.error("Error building chart:", error);
      return this.buildFallbackChart(birthData, system);
    }
  }
  /**
   * Apply system-specific adjustments (Vedic uses sidereal, etc.)
   */
  static applySystemAdjustments(chartData, system, birthData) {
    switch (system) {
      case "vedic":
        return this.applyVedicAdjustments(chartData, birthData);
      case "mayan":
        return this.applyMayanAdjustments(chartData, birthData);
      default:
        return chartData;
    }
  }
  /**
   * Apply Vedic (sidereal) adjustments
   */
  static applyVedicAdjustments(chartData, birthData) {
    const ayanamsa = this.calculateAyanamsa(birthData.date);
    const adjustedPlanets = chartData.planets.map((planet) => ({
      ...planet,
      position: {
        ...planet.position,
        longitude: (planet.position.longitude - ayanamsa + 360) % 360,
        sign: this.getSignFromLongitude((planet.position.longitude - ayanamsa + 360) % 360)
      }
    }));
    const adjustedAscendant = {
      ...chartData.ascendant,
      degree: (chartData.ascendant.degree - ayanamsa + 360) % 360,
      sign: this.getSignFromLongitude((chartData.ascendant.degree - ayanamsa + 360) % 360)
    };
    return {
      ...chartData,
      planets: adjustedPlanets,
      ascendant: adjustedAscendant
    };
  }
  /**
   * Apply Mayan adjustments (placeholder)
   */
  static applyMayanAdjustments(chartData, birthData) {
    return {
      ...chartData,
      mayanData: {
        tzolkin: this.calculateTzolkin(birthData.date),
        haab: this.calculateHaab(birthData.date)
      }
    };
  }
  /**
   * Calculate Ayanamsa for Vedic astrology
   */
  static calculateAyanamsa(date) {
    const year = date.getFullYear();
    const baseYear = 1900;
    const baseAyanamsa = 22.46;
    const annualRate = 0.0139;
    return baseAyanamsa + (year - baseYear) * annualRate;
  }
  /**
   * Calculate Mayan Tzolk'in day
   */
  static calculateTzolkin(date) {
    const tzolkinStart = /* @__PURE__ */ new Date("1900-01-01");
    const daysDiff = Math.floor((date.getTime() - tzolkinStart.getTime()) / (1e3 * 60 * 60 * 24));
    const tzolkinDays = [
      "Imix",
      "Ik",
      "Akbal",
      "Kan",
      "Chicchan",
      "Cimi",
      "Manik",
      "Lamat",
      "Muluc",
      "Oc",
      "Chuen",
      "Eb",
      "Ben",
      "Ix",
      "Men",
      "Cib",
      "Caban",
      "Etznab",
      "Cauac",
      "Ahau"
    ];
    const number = daysDiff % 13 + 1;
    const daySign = tzolkinDays[daysDiff % 20];
    return { number, daySign };
  }
  /**
   * Calculate Mayan Haab date
   */
  static calculateHaab(date) {
    const haabMonths = [
      "Pop",
      "Wo",
      "Sip",
      "Sotz",
      "Sek",
      "Xul",
      "Yaxkin",
      "Mol",
      "Chen",
      "Yax",
      "Sak",
      "Keh",
      "Mak",
      "Kankin",
      "Muan",
      "Pax",
      "Kayab",
      "Kumku",
      "Wayeb"
    ];
    const dayOfYear = Math.floor((date.getTime() - new Date(date.getFullYear(), 0, 0).getTime()) / (1e3 * 60 * 60 * 24));
    const monthIndex = Math.floor(dayOfYear / 20) % 19;
    const dayInMonth = dayOfYear % 20;
    return {
      number: dayInMonth,
      month: haabMonths[monthIndex]
    };
  }
  /**
   * Calculate midheaven
   */
  static calculateMidheaven(ascendant, birthData) {
    const mcDegree = (ascendant.degree + 90) % 360;
    const sign = this.getSignFromLongitude(mcDegree);
    return { sign, degree: mcDegree % 30 };
  }
  /**
   * Get zodiac sign from longitude
   */
  static getSignFromLongitude(longitude) {
    const signs = [
      "Aries",
      "Taurus",
      "Gemini",
      "Cancer",
      "Leo",
      "Virgo",
      "Libra",
      "Scorpio",
      "Sagittarius",
      "Capricorn",
      "Aquarius",
      "Pisces"
    ];
    const signIndex = Math.floor(longitude / 30);
    return signs[signIndex] || "Aries";
  }
  /**
   * Fallback chart if calculations fail
   */
  static buildFallbackChart(birthData, system) {
    return {
      id: `chart_${Date.now()}`,
      userId: "temp_user",
      birthData,
      system,
      planets: [
        {
          id: "sun",
          name: "Sun",
          symbol: "\u2609",
          position: { longitude: 120, latitude: 0, sign: "Leo", house: 5, degree: 0, minute: 0 },
          retrograde: false
        }
      ],
      houses: Array.from({ length: 12 }, (_, i) => ({
        number: i + 1,
        sign: "Aries",
        cusp: i * 30,
        ruler: "Mars"
      })),
      aspects: [],
      ascendant: { sign: "Aries", degree: 0 },
      midheaven: { sign: "Capricorn", degree: 0 },
      calculatedAt: /* @__PURE__ */ new Date()
    };
  }
};
export {
  ChartBuilder,
  DailyInsightGenerator,
  MayanAstrologyEngine,
  VedicAstrologyEngine,
  WesternAstrologyEngine,
  calculateAspects,
  calculateHouses,
  calculatePlanetPositions,
  generateInterpretations,
  interpretationTemplates
};
