{"name": "@taraka/astrology", "version": "0.1.0", "description": "Astrology calculation engine for Taraka app", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsup src/index.ts --format cjs,esm", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest"}, "dependencies": {"@taraka/shared": "file:../shared", "date-fns": "^2.30.0", "astronomy-engine": "^2.1.19", "suncalc": "^1.9.0"}, "devDependencies": {"@types/jest": "^29.5.0", "jest": "^29.6.0", "ts-jest": "^29.1.0", "tsup": "^7.2.0", "typescript": "^5.1.0"}, "peerDependencies": {"typescript": "^5.0.0"}}