import { BirthData, DailyInsight, PredictionMode, AstrologySystem, InsightTheme } from '@taraka/shared';
import { AstronomicalCalculator } from '../calculations/astronomical';
import { format } from 'date-fns';

export class DailyInsightGenerator {
  /**
   * Generate daily insight for a user
   */
  static async generateDailyInsight(
    birthData: BirthData,
    date: Date = new Date(),
    mode: PredictionMode = 'reflective',
    system: AstrologySystem = 'western'
  ): Promise<DailyInsight> {
    try {
      // Calculate current planetary transits
      const transits = this.calculateTransits(birthData, date);
      
      // Determine dominant themes for the day
      const themes = this.determineDominantThemes(transits, mode);
      
      // Generate insight content based on mode
      const content = this.generateInsightContent(transits, themes, mode, system);
      
      return {
        id: `insight_${Date.now()}`,
        userId: 'temp_user',
        date,
        mode,
        system,
        themes,
        content,
        transits,
        createdAt: new Date(),
      };
    } catch (error) {
      console.error('Error generating daily insight:', error);
      return this.generateFallbackInsight(date, mode, system);
    }
  }

  /**
   * Calculate current planetary transits
   */
  private static calculateTransits(birthData: BirthData, date: Date) {
    // Get current planetary positions
    const currentPositions = AstronomicalCalculator.calculatePlanetaryPositions({
      ...birthData,
      date,
      time: { hour: 12, minute: 0 }, // Use noon for daily transits
    });

    // Get natal chart positions for comparison
    const natalPositions = AstronomicalCalculator.calculatePlanetaryPositions(birthData);

    const transits = [];

    // Check for significant transits
    for (const currentPlanet of currentPositions) {
      for (const natalPlanet of natalPositions) {
        const orb = this.calculateOrb(currentPlanet.position.longitude, natalPlanet.position.longitude);
        const aspect = this.determineAspect(orb);

        if (aspect && orb <= this.getAspectOrb(aspect)) {
          transits.push({
            planet: currentPlanet.name,
            aspect,
            target: natalPlanet.name,
            influence: this.determineInfluence(aspect, currentPlanet.name, natalPlanet.name),
            orb,
            exact: orb <= 1,
          });
        }
      }
    }

    return transits;
  }

  /**
   * Calculate orb between two planetary positions
   */
  private static calculateOrb(pos1: number, pos2: number): number {
    let diff = Math.abs(pos1 - pos2);
    if (diff > 180) diff = 360 - diff;
    return diff;
  }

  /**
   * Determine aspect type from orb
   */
  private static determineAspect(orb: number): string | null {
    const aspects = [
      { name: 'conjunction', angle: 0, orb: 8 },
      { name: 'sextile', angle: 60, orb: 6 },
      { name: 'square', angle: 90, orb: 8 },
      { name: 'trine', angle: 120, orb: 8 },
      { name: 'opposition', angle: 180, orb: 8 },
    ];

    for (const aspect of aspects) {
      if (Math.abs(orb - aspect.angle) <= aspect.orb || 
          Math.abs(orb - (360 - aspect.angle)) <= aspect.orb) {
        return aspect.name;
      }
    }

    return null;
  }

  /**
   * Get orb tolerance for aspect
   */
  private static getAspectOrb(aspect: string): number {
    const orbs: Record<string, number> = {
      conjunction: 8,
      sextile: 6,
      square: 8,
      trine: 8,
      opposition: 8,
    };
    return orbs[aspect] || 5;
  }

  /**
   * Determine influence type
   */
  private static determineInfluence(aspect: string, transitPlanet: string, natalPlanet: string): 'positive' | 'neutral' | 'challenging' {
    const harmonious = ['trine', 'sextile'];
    const challenging = ['square', 'opposition'];
    const neutral = ['conjunction'];

    if (harmonious.includes(aspect)) return 'positive';
    if (challenging.includes(aspect)) return 'challenging';
    return 'neutral';
  }

  /**
   * Determine dominant themes for the day
   */
  private static determineDominantThemes(transits: any[], mode: PredictionMode): InsightTheme[] {
    const themeWeights: Record<InsightTheme, number> = {
      love: 0,
      career: 0,
      emotions: 0,
      growth: 0,
      creativity: 0,
      challenge: 0,
    };

    // Weight themes based on transits
    for (const transit of transits) {
      switch (transit.planet) {
        case 'Venus':
          themeWeights.love += 2;
          themeWeights.creativity += 1;
          break;
        case 'Mars':
          themeWeights.career += 2;
          themeWeights.challenge += 1;
          break;
        case 'Moon':
          themeWeights.emotions += 3;
          break;
        case 'Jupiter':
          themeWeights.growth += 2;
          themeWeights.career += 1;
          break;
        case 'Saturn':
          themeWeights.challenge += 2;
          themeWeights.career += 1;
          break;
        case 'Mercury':
          themeWeights.career += 1;
          themeWeights.creativity += 1;
          break;
      }

      // Adjust for aspect influence
      if (transit.influence === 'challenging') {
        themeWeights.challenge += 1;
      } else if (transit.influence === 'positive') {
        themeWeights.growth += 1;
      }
    }

    // Return top 2-3 themes
    const sortedThemes = Object.entries(themeWeights)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .filter(([, weight]) => weight > 0)
      .map(([theme]) => theme as InsightTheme);

    return sortedThemes.length > 0 ? sortedThemes : ['growth'];
  }

  /**
   * Generate insight content based on mode and themes
   */
  private static generateInsightContent(
    transits: any[],
    themes: InsightTheme[],
    mode: PredictionMode,
    system: AstrologySystem
  ) {
    const primaryTheme = themes[0] || 'growth';
    
    if (mode === 'reflective') {
      return this.generateReflectiveContent(transits, themes, primaryTheme);
    } else {
      return this.generateTraditionalContent(transits, themes, primaryTheme);
    }
  }

  /**
   * Generate reflective mode content
   */
  private static generateReflectiveContent(transits: any[], themes: InsightTheme[], primaryTheme: InsightTheme) {
    const reflectiveTemplates = {
      love: {
        title: "Heart Connections Today",
        summary: "Your heart space is activated today, inviting deeper connections and self-love.",
        detailed: "The cosmic energies today are illuminating your relationship patterns and capacity for love. This is a beautiful time to reflect on how you give and receive affection, and what authentic connection means to you.",
        reflection_prompt: "How can I show more compassion to myself and others today? What does love mean to me right now?"
      },
      career: {
        title: "Purpose and Path",
        summary: "Your professional energy is highlighted, encouraging alignment with your true calling.",
        detailed: "The universe is supporting your career growth and professional development today. Consider how your work aligns with your deeper purpose and values.",
        reflection_prompt: "What aspects of my work bring me the most fulfillment? How can I better align my career with my authentic self?"
      },
      emotions: {
        title: "Inner Landscape",
        summary: "Your emotional world is rich and active today, offering opportunities for healing and growth.",
        detailed: "Today's energies invite you to explore your inner emotional landscape with curiosity and compassion. Your feelings are valid messengers.",
        reflection_prompt: "What emotions am I experiencing today? What might they be trying to tell me about my needs and desires?"
      },
      growth: {
        title: "Expansion and Evolution",
        summary: "You're in a powerful growth phase, with opportunities for personal development and expansion.",
        detailed: "The cosmic currents are supporting your personal evolution today. Trust in your ability to grow and transform.",
        reflection_prompt: "In what ways am I growing and evolving? What new aspects of myself am I ready to embrace?"
      },
      creativity: {
        title: "Creative Flow",
        summary: "Your creative channels are open and flowing, inviting artistic expression and innovation.",
        detailed: "Today's energies support creative expression and innovative thinking. Allow your imagination to guide you.",
        reflection_prompt: "How can I express my creativity today? What wants to be created through me?"
      },
      challenge: {
        title: "Growth Through Challenge",
        summary: "Today's obstacles are opportunities for strength-building and wisdom-gathering.",
        detailed: "The challenges you face today are cosmic invitations to develop resilience and discover your inner strength.",
        reflection_prompt: "What is this challenge teaching me? How can I approach difficulties with grace and wisdom?"
      }
    };

    return reflectiveTemplates[primaryTheme];
  }

  /**
   * Generate traditional mode content
   */
  private static generateTraditionalContent(transits: any[], themes: InsightTheme[], primaryTheme: InsightTheme) {
    const traditionalTemplates = {
      love: {
        title: "Venus Influences Your Relationships",
        summary: "Favorable aspects for romance and partnerships are present today.",
        detailed: "The planetary alignments today favor matters of the heart. Venus's influence brings harmony to relationships and opportunities for romantic connections.",
      },
      career: {
        title: "Professional Advancement Favored",
        summary: "The stars align to support your career ambitions and professional goals.",
        detailed: "Mars and Jupiter create favorable conditions for career advancement. This is an auspicious time for important business decisions and professional initiatives.",
      },
      emotions: {
        title: "Lunar Influences on Mood",
        summary: "The Moon's position affects your emotional state and intuitive abilities.",
        detailed: "Today's lunar aspects heighten emotional sensitivity and psychic receptivity. Pay attention to your dreams and intuitive insights.",
      },
      growth: {
        title: "Jupiter Brings Expansion",
        summary: "Beneficial Jupiter aspects promote growth, learning, and good fortune.",
        detailed: "Jupiter's benevolent influence expands your horizons and brings opportunities for advancement in education, travel, or spiritual pursuits.",
      },
      creativity: {
        title: "Mercury Enhances Creative Expression",
        summary: "Mental agility and creative communication are highlighted today.",
        detailed: "Mercury's favorable position enhances your ability to communicate ideas and express creativity through writing, speaking, or artistic endeavors.",
      },
      challenge: {
        title: "Saturn Tests Your Resolve",
        summary: "Challenging aspects require patience, discipline, and perseverance.",
        detailed: "Saturn's influence brings tests and obstacles that ultimately strengthen your character. Approach challenges with patience and methodical planning.",
      }
    };

    return traditionalTemplates[primaryTheme];
  }

  /**
   * Generate fallback insight if calculations fail
   */
  private static generateFallbackInsight(date: Date, mode: PredictionMode, system: AstrologySystem): DailyInsight {
    return {
      id: `insight_${Date.now()}`,
      userId: 'temp_user',
      date,
      mode,
      system,
      themes: ['growth'],
      content: {
        title: "Cosmic Reflection",
        summary: "Today offers opportunities for growth and self-discovery.",
        detailed: "The universe continues to support your journey of growth and self-discovery. Take time today to connect with your inner wisdom and trust in your path.",
        reflection_prompt: mode === 'reflective' ? "What is my heart calling me to explore today?" : undefined,
      },
      transits: [],
      createdAt: new Date(),
    };
  }
}
