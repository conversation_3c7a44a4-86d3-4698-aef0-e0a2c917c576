import { BirthData, Planet, House } from '@taraka/shared';

// Zodiac signs in order
const ZODIAC_SIGNS = [
  '<PERSON><PERSON>', 'Taurus', 'Gemini', 'Cancer',
  '<PERSON>', '<PERSON>ir<PERSON>', 'Li<PERSON>', '<PERSON><PERSON><PERSON>',
  'Sagittarius', 'Capricorn', 'Aquarius', '<PERSON>sces'
];

// Planet symbols
const PLANET_SYMBOLS = {
  Sun: '☉',
  Moon: '☽',
  Mercury: '☿',
  Venus: '♀',
  Mars: '♂',
  Jupiter: '♃',
  Saturn: '♄',
  Uranus: '♅',
  Neptune: '♆',
  Pluto: '♇'
};

export class AstronomicalCalculator {
  /**
   * Calculate planetary positions using simplified astronomical calculations
   */
  static calculatePlanetaryPositions(birthData: BirthData): Planet[] {
    const birthDateTime = new Date(birthData.date);
    birthDateTime.setHours(birthData.time.hour, birthData.time.minute, 0, 0);

    const planets: Planet[] = [];
    const planetNames = ['Sun', 'Moon', 'Mercury', 'Venus', 'Mars', 'Jupiter', 'Saturn', 'Uranus', 'Neptune', 'Pluto'];

    planetNames.forEach(planetName => {
      try {
        // Calculate approximate position based on date
        const longitude = this.calculatePlanetLongitude(planetName, birthDateTime);

        // Calculate zodiac sign
        const signIndex = Math.floor(longitude / 30);
        const sign = ZODIAC_SIGNS[signIndex];

        // Calculate degree within sign
        const degreeInSign = longitude % 30;
        const degree = Math.floor(degreeInSign);
        const minute = Math.floor((degreeInSign - degree) * 60);

        // Calculate house (simplified)
        const house = this.calculateHousePosition(longitude, birthData);

        // Check if planet is retrograde (simplified calculation)
        const isRetrograde = this.isRetrograde(planetName, birthDateTime);

        planets.push({
          id: planetName.toLowerCase(),
          name: planetName,
          symbol: PLANET_SYMBOLS[planetName as keyof typeof PLANET_SYMBOLS] || '●',
          position: {
            longitude,
            latitude: 0, // Simplified - assume on ecliptic
            sign,
            house,
            degree,
            minute,
          },
          retrograde: isRetrograde,
        });
      } catch (error) {
        console.warn(`Failed to calculate position for ${planetName}:`, error);
        // Fallback to approximate position
        planets.push(this.getFallbackPlanetPosition(planetName, birthData));
      }
    });

    return planets;
  }

  /**
   * Calculate approximate planet longitude based on simplified orbital mechanics
   */
  private static calculatePlanetLongitude(planetName: string, date: Date): number {
    // This is a simplified calculation - in production, use proper ephemeris data
    const daysSinceEpoch = (date.getTime() - new Date('2000-01-01').getTime()) / (1000 * 60 * 60 * 24);

    // Approximate orbital periods and starting positions (simplified)
    const planetData: Record<string, { period: number; startLongitude: number; speed: number }> = {
      Sun: { period: 365.25, startLongitude: 280, speed: 0.9856 },
      Moon: { period: 27.32, startLongitude: 0, speed: 13.176 },
      Mercury: { period: 87.97, startLongitude: 252, speed: 4.092 },
      Venus: { period: 224.7, startLongitude: 181, speed: 1.602 },
      Mars: { period: 686.98, startLongitude: 355, speed: 0.524 },
      Jupiter: { period: 4332.59, startLongitude: 34, speed: 0.083 },
      Saturn: { period: 10759.22, startLongitude: 50, speed: 0.033 },
      Uranus: { period: 30688.5, startLongitude: 314, speed: 0.012 },
      Neptune: { period: 60182, startLongitude: 304, speed: 0.006 },
      Pluto: { period: 90560, startLongitude: 238, speed: 0.004 }
    };

    const data = planetData[planetName];
    if (!data) return 0;

    // Calculate current longitude
    let longitude = data.startLongitude + (daysSinceEpoch * data.speed);

    // Normalize to 0-360 degrees
    longitude = ((longitude % 360) + 360) % 360;

    return longitude;
  }

  /**
   * Calculate house positions using Placidus system
   */
  static calculateHouses(birthData: BirthData): House[] {
    const houses: House[] = [];

    try {
      // Calculate local sidereal time
      const lst = this.calculateLocalSiderealTime(birthData);

      // Calculate ascendant
      const ascendant = this.calculateAscendant(birthData, lst);

      // Calculate house cusps using Placidus system
      for (let i = 1; i <= 12; i++) {
        const cuspLongitude = this.calculateHouseCusp(i, ascendant, birthData);
        const signIndex = Math.floor(cuspLongitude / 30);
        const sign = ZODIAC_SIGNS[signIndex];

        houses.push({
          number: i,
          sign,
          cusp: cuspLongitude,
          ruler: this.getSignRuler(sign),
        });
      }
    } catch (error) {
      console.warn('Failed to calculate houses, using fallback:', error);
      // Fallback to equal house system
      return this.getFallbackHouses();
    }

    return houses;
  }

  /**
   * Calculate ascendant (rising sign)
   */
  static calculateAscendant(birthData: BirthData): { sign: string; degree: number } {
    try {
      const birthDateTime = new Date(birthData.date);
      birthDateTime.setHours(birthData.time.hour, birthData.time.minute, 0, 0);

      // Calculate local sidereal time
      const lst = this.calculateLocalSiderealTime(birthData);

      // Calculate ascendant longitude
      const lat = birthData.location.coordinates.latitude * Math.PI / 180; // Convert to radians
      const ascendantLongitude = Math.atan2(
        Math.cos(lst),
        -(Math.sin(lst) * Math.cos(23.44 * Math.PI / 180) + Math.tan(lat) * Math.sin(23.44 * Math.PI / 180))
      ) * 180 / Math.PI;

      const normalizedLongitude = ((ascendantLongitude % 360) + 360) % 360;
      const signIndex = Math.floor(normalizedLongitude / 30);
      const sign = ZODIAC_SIGNS[signIndex];
      const degree = normalizedLongitude % 30;

      return { sign, degree };
    } catch (error) {
      console.warn('Failed to calculate ascendant:', error);
      return { sign: 'Aries', degree: 0 };
    }
  }

  /**
   * Calculate local sidereal time
   */
  private static calculateLocalSiderealTime(birthData: BirthData): number {
    const birthDateTime = new Date(birthData.date);
    birthDateTime.setHours(birthData.time.hour, birthData.time.minute, 0, 0);

    // Julian day number
    const jd = this.toJulianDay(birthDateTime);

    // Greenwich sidereal time
    const gst = this.calculateGreenwichSiderealTime(jd);

    // Local sidereal time
    const longitude = birthData.location.coordinates.longitude;
    const lst = gst + (longitude / 15); // Convert longitude to hours

    return ((lst % 24) + 24) % 24; // Normalize to 0-24 hours
  }

  /**
   * Convert date to Julian day number
   */
  private static toJulianDay(date: Date): number {
    const a = Math.floor((14 - (date.getMonth() + 1)) / 12);
    const y = date.getFullYear() + 4800 - a;
    const m = (date.getMonth() + 1) + 12 * a - 3;

    return date.getDate() + Math.floor((153 * m + 2) / 5) + 365 * y +
           Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) - 32045 +
           (date.getHours() + date.getMinutes() / 60 + date.getSeconds() / 3600) / 24;
  }

  /**
   * Calculate Greenwich sidereal time
   */
  private static calculateGreenwichSiderealTime(jd: number): number {
    const t = (jd - 2451545.0) / 36525;
    let gst = 280.46061837 + 360.98564736629 * (jd - 2451545) +
              0.000387933 * t * t - t * t * t / 38710000;

    return ((gst % 360) + 360) % 360 / 15; // Convert to hours
  }

  /**
   * Calculate house cusp using simplified Placidus system
   */
  private static calculateHouseCusp(houseNumber: number, ascendant: { degree: number }, birthData: BirthData): number {
    // Simplified house calculation - in production, use proper Placidus formulas
    const baseAngle = (houseNumber - 1) * 30;
    return (ascendant.degree + baseAngle) % 360;
  }

  /**
   * Calculate house position for a planet
   */
  private static calculateHousePosition(longitude: number, birthData: BirthData): number {
    // Simplified house calculation
    const ascendant = this.calculateAscendant(birthData);
    const relativePosition = ((longitude - ascendant.degree) + 360) % 360;
    return Math.floor(relativePosition / 30) + 1;
  }

  /**
   * Check if planet is retrograde (simplified)
   */
  private static isRetrograde(planetName: string, date: Date): boolean {
    // Simplified retrograde calculation
    // In reality, this requires comparing velocities over time
    if (planetName === 'Sun' || planetName === 'Moon') return false;

    // Approximate retrograde periods (this is very simplified)
    const retrogradeChances = {
      Mercury: 0.2,
      Venus: 0.07,
      Mars: 0.1,
      Jupiter: 0.3,
      Saturn: 0.35,
      Uranus: 0.4,
      Neptune: 0.4,
      Pluto: 0.4
    };

    const chance = retrogradeChances[planetName as keyof typeof retrogradeChances] || 0;
    return Math.random() < chance; // This should be replaced with actual calculation
  }

  /**
   * Get sign ruler
   */
  private static getSignRuler(sign: string): string {
    const rulers: Record<string, string> = {
      'Aries': 'Mars',
      'Taurus': 'Venus',
      'Gemini': 'Mercury',
      'Cancer': 'Moon',
      'Leo': 'Sun',
      'Virgo': 'Mercury',
      'Libra': 'Venus',
      'Scorpio': 'Mars',
      'Sagittarius': 'Jupiter',
      'Capricorn': 'Saturn',
      'Aquarius': 'Saturn',
      'Pisces': 'Jupiter'
    };
    return rulers[sign] || 'Unknown';
  }

  /**
   * Fallback planet position if calculation fails
   */
  private static getFallbackPlanetPosition(planetName: string, birthData: BirthData): Planet {
    const fallbackLongitudes: Record<string, number> = {
      Sun: 120, Moon: 45, Mercury: 110, Venus: 130, Mars: 200,
      Jupiter: 300, Saturn: 250, Uranus: 15, Neptune: 340, Pluto: 280
    };

    const longitude = fallbackLongitudes[planetName] || 0;
    const signIndex = Math.floor(longitude / 30);
    const sign = ZODIAC_SIGNS[signIndex];

    return {
      id: planetName.toLowerCase(),
      name: planetName,
      symbol: PLANET_SYMBOLS[planetName as keyof typeof PLANET_SYMBOLS] || '●',
      position: {
        longitude,
        latitude: 0,
        sign,
        house: Math.floor(Math.random() * 12) + 1,
        degree: Math.floor(longitude % 30),
        minute: 0,
      },
      retrograde: false,
    };
  }

  /**
   * Fallback houses if calculation fails
   */
  private static getFallbackHouses(): House[] {
    return Array.from({ length: 12 }, (_, i) => ({
      number: i + 1,
      sign: ZODIAC_SIGNS[i % 12],
      cusp: i * 30,
      ruler: this.getSignRuler(ZODIAC_SIGNS[i % 12]),
    }));
  }
}
