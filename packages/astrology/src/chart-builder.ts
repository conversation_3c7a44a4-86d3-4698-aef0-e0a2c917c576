import { BirthData, NatalChart, AstrologySystem } from '@taraka/shared';
import { AstronomicalCalculator } from './calculations/astronomical';
import { calculateAspects } from './calculations/aspects';
import { WesternAstrologyEngine } from './engines/western';
import { VedicAstrologyEngine } from './engines/vedic';

export class ChartBuilder {
  static async buildChart(
    birthData: BirthData,
    system: AstrologySystem = 'western'
  ): Promise<NatalChart> {
    try {
      // Calculate real planetary positions
      const planets = AstronomicalCalculator.calculatePlanetaryPositions(birthData);

      // Calculate houses
      const houses = AstronomicalCalculator.calculateHouses(birthData);

      // Calculate ascendant and midheaven
      const ascendant = AstronomicalCalculator.calculateAscendant(birthData);
      const midheaven = this.calculateMidheaven(ascendant, birthData);

      // Calculate aspects between planets
      const aspects = calculateAspects(planets);

      // Apply system-specific adjustments
      const adjustedData = this.applySystemAdjustments(
        { planets, houses, ascendant, midheaven },
        system,
        birthData
      );

      const chart: Natal<PERSON>hart = {
        id: `chart_${Date.now()}`,
        userId: 'temp_user',
        birthData,
        system,
        planets: adjustedData.planets,
        houses: adjustedData.houses,
        aspects,
        ascendant: adjustedData.ascendant,
        midheaven: adjustedData.midheaven,
        calculatedAt: new Date()
      };

      return chart;
    } catch (error) {
      console.error('Error building chart:', error);
      // Fallback to basic calculation
      return this.buildFallbackChart(birthData, system);
    }
  }

  /**
   * Apply system-specific adjustments (Vedic uses sidereal, etc.)
   */
  private static applySystemAdjustments(
    chartData: any,
    system: AstrologySystem,
    birthData: BirthData
  ) {
    switch (system) {
      case 'vedic':
        return this.applyVedicAdjustments(chartData, birthData);
      case 'mayan':
        return this.applyMayanAdjustments(chartData, birthData);
      default:
        return chartData; // Western system uses tropical zodiac as-is
    }
  }

  /**
   * Apply Vedic (sidereal) adjustments
   */
  private static applyVedicAdjustments(chartData: any, birthData: BirthData) {
    // Ayanamsa (precession correction) - approximately 24 degrees currently
    const ayanamsa = this.calculateAyanamsa(birthData.date);

    // Adjust all planetary positions
    const adjustedPlanets = chartData.planets.map((planet: any) => ({
      ...planet,
      position: {
        ...planet.position,
        longitude: ((planet.position.longitude - ayanamsa) + 360) % 360,
        sign: this.getSignFromLongitude(((planet.position.longitude - ayanamsa) + 360) % 360),
      }
    }));

    // Adjust ascendant
    const adjustedAscendant = {
      ...chartData.ascendant,
      degree: ((chartData.ascendant.degree - ayanamsa) + 360) % 360,
      sign: this.getSignFromLongitude(((chartData.ascendant.degree - ayanamsa) + 360) % 360),
    };

    return {
      ...chartData,
      planets: adjustedPlanets,
      ascendant: adjustedAscendant,
    };
  }

  /**
   * Apply Mayan adjustments (placeholder)
   */
  private static applyMayanAdjustments(chartData: any, birthData: BirthData) {
    // Mayan system would use different calculations entirely
    // For now, return the same data with Mayan-specific metadata
    return {
      ...chartData,
      mayanData: {
        tzolkin: this.calculateTzolkin(birthData.date),
        haab: this.calculateHaab(birthData.date),
      }
    };
  }

  /**
   * Calculate Ayanamsa for Vedic astrology
   */
  private static calculateAyanamsa(date: Date): number {
    // Simplified Lahiri Ayanamsa calculation
    const year = date.getFullYear();
    const baseYear = 1900;
    const baseAyanamsa = 22.46; // degrees
    const annualRate = 0.0139; // degrees per year

    return baseAyanamsa + (year - baseYear) * annualRate;
  }

  /**
   * Calculate Mayan Tzolk'in day
   */
  private static calculateTzolkin(date: Date): { number: number; daySign: string } {
    // Simplified Tzolk'in calculation
    const tzolkinStart = new Date('1900-01-01'); // Arbitrary start date
    const daysDiff = Math.floor((date.getTime() - tzolkinStart.getTime()) / (1000 * 60 * 60 * 24));

    const tzolkinDays = [
      'Imix', 'Ik', 'Akbal', 'Kan', 'Chicchan', 'Cimi', 'Manik', 'Lamat',
      'Muluc', 'Oc', 'Chuen', 'Eb', 'Ben', 'Ix', 'Men', 'Cib', 'Caban',
      'Etznab', 'Cauac', 'Ahau'
    ];

    const number = (daysDiff % 13) + 1;
    const daySign = tzolkinDays[daysDiff % 20];

    return { number, daySign };
  }

  /**
   * Calculate Mayan Haab date
   */
  private static calculateHaab(date: Date): { number: number; month: string } {
    const haabMonths = [
      'Pop', 'Wo', 'Sip', 'Sotz', 'Sek', 'Xul', 'Yaxkin', 'Mol',
      'Chen', 'Yax', 'Sak', 'Keh', 'Mak', 'Kankin', 'Muan', 'Pax',
      'Kayab', 'Kumku', 'Wayeb'
    ];

    // Simplified calculation
    const dayOfYear = Math.floor((date.getTime() - new Date(date.getFullYear(), 0, 0).getTime()) / (1000 * 60 * 60 * 24));
    const monthIndex = Math.floor(dayOfYear / 20) % 19;
    const dayInMonth = (dayOfYear % 20);

    return {
      number: dayInMonth,
      month: haabMonths[monthIndex]
    };
  }

  /**
   * Calculate midheaven
   */
  private static calculateMidheaven(ascendant: any, birthData: BirthData): { sign: string; degree: number } {
    // Simplified midheaven calculation (MC is typically 90 degrees from ascendant)
    const mcDegree = (ascendant.degree + 90) % 360;
    const sign = this.getSignFromLongitude(mcDegree);

    return { sign, degree: mcDegree % 30 };
  }

  /**
   * Get zodiac sign from longitude
   */
  private static getSignFromLongitude(longitude: number): string {
    const signs = [
      'Aries', 'Taurus', 'Gemini', 'Cancer',
      'Leo', 'Virgo', 'Libra', 'Scorpio',
      'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'
    ];

    const signIndex = Math.floor(longitude / 30);
    return signs[signIndex] || 'Aries';
  }

  /**
   * Fallback chart if calculations fail
   */
  private static buildFallbackChart(birthData: BirthData, system: AstrologySystem): NatalChart {
    return {
      id: `chart_${Date.now()}`,
      userId: 'temp_user',
      birthData,
      system,
      planets: [
        {
          id: 'sun',
          name: 'Sun',
          symbol: '☉',
          position: { longitude: 120, latitude: 0, sign: 'Leo', house: 5, degree: 0, minute: 0 },
          retrograde: false
        }
      ],
      houses: Array.from({ length: 12 }, (_, i) => ({
        number: i + 1,
        sign: 'Aries',
        cusp: i * 30,
        ruler: 'Mars'
      })),
      aspects: [],
      ascendant: { sign: 'Aries', degree: 0 },
      midheaven: { sign: 'Capricorn', degree: 0 },
      calculatedAt: new Date()
    };
  }
}
