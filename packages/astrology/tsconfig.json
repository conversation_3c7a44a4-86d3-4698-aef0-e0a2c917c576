{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "ESNext", "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "composite": true, "noEmit": false}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.*", "**/*.spec.*"]}