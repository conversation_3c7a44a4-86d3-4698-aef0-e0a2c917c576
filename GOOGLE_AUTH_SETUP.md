# 🌟 Google OAuth Setup for Taraka

This guide helps you configure Google OAuth authentication for the Taraka astrology app.

## ✅ Prerequisites

- Supabase project created and configured
- Google Cloud Console account

## 🚀 Setup Steps

### 1. Google Cloud Console Setup

1. **Go to [Google Cloud Console](https://console.cloud.google.com/)**
2. **Create a new project** (or select existing one)
3. **Enable Google+ API**:
   - Go to "APIs & Services" → "Library"
   - Search for "Google+ API" 
   - Click "Enable"

### 2. Create OAuth 2.0 Credentials

1. **Go to "APIs & Services" → "Credentials"**
2. **Click "Create Credentials" → "OAuth 2.0 Client IDs"**
3. **Configure OAuth consent screen** (if not done):
   - Choose "External" for user type
   - Fill in app information:
     - **App name**: Taraka Astrology
     - **User support email**: Your email
     - **Developer contact**: Your email
   - Add scopes: `email`, `profile`, `openid`
   - Add test users (your email for testing)

4. **Create OAuth Client ID**:
   - **Application type**: Web application
   - **Name**: Taraka Web App
   - **Authorized JavaScript origins**:
     ```
     http://localhost:3000
     https://your-domain.com (for production)
     ```
   - **Authorized redirect URIs**:
     ```
     https://your-project-ref.supabase.co/auth/v1/callback
     ```

5. **Copy your credentials**:
   - **Client ID**: `123456789-abc123.apps.googleusercontent.com`
   - **Client Secret**: `GOCSPX-your-client-secret`

### 3. Configure Supabase

1. **Go to your Supabase dashboard**
2. **Navigate to "Authentication" → "Providers"**
3. **Enable Google provider**
4. **Add your Google credentials**:
   - **Client ID**: Paste from Google Cloud Console
   - **Client Secret**: Paste from Google Cloud Console
5. **Save the configuration**

### 4. Update Redirect URLs

In your Supabase dashboard:
1. **Go to "Authentication" → "Settings"**
2. **Add redirect URLs**:
   ```
   http://localhost:3000/auth/callback
   https://your-domain.com/auth/callback (for production)
   ```

## 🧪 Testing Google OAuth

### 1. Test the Flow

1. **Visit your app**: `http://localhost:3000`
2. **Click "Continue with Google"**
3. **You should be redirected to Google**
4. **Sign in with your Google account**
5. **You should be redirected back to your app**
6. **Check that you're logged in and see your profile**

### 2. Verify in Supabase

1. **Go to "Authentication" → "Users"**
2. **You should see your Google user listed**
3. **Check the "Profiles" table for auto-created profile**

## 🔧 Troubleshooting

### Common Issues

**"redirect_uri_mismatch"**
- Check that your redirect URI in Google Cloud Console matches exactly:
  `https://your-project-ref.supabase.co/auth/v1/callback`

**"OAuth consent screen not configured"**
- Complete the OAuth consent screen setup in Google Cloud Console
- Add your email as a test user

**"Invalid client ID"**
- Verify the Client ID is correctly copied to Supabase
- Check for extra spaces or characters

**"Access blocked"**
- Your OAuth consent screen might need verification
- For development, add yourself as a test user

### Debug Steps

1. **Check browser console** for error messages
2. **Check Supabase logs** in the dashboard
3. **Verify environment variables** are loaded correctly
4. **Test with incognito mode** to avoid cached auth states

## 🌟 What's Implemented

### ✅ Features Working

- **Google OAuth Sign-In**: Full OAuth flow with Google
- **Automatic Profile Creation**: User profiles created on first sign-in
- **Secure Redirect Handling**: Proper callback route handling
- **Error Handling**: User-friendly error messages
- **Loading States**: Beautiful loading animations during auth
- **Session Management**: Automatic session persistence

### 🎨 UI Features

- **Google Brand Colors**: Official Google logo and colors
- **Mystical Design**: Cosmic theme maintained throughout auth flow
- **Responsive Design**: Works on all device sizes
- **Loading Animations**: Smooth transitions and feedback

## 🚀 Next Steps

Once Google OAuth is working:

1. **Test with multiple users**
2. **Add profile completion flow**
3. **Implement birth data collection**
4. **Add more OAuth providers** (Facebook, Apple, etc.)
5. **Set up production domain** and update redirect URLs

---

🌟 **Your cosmic authentication is ready!** Users can now sign in with Google and start their astrological journey.
