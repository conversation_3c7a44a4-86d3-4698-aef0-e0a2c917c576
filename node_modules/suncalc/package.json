{"name": "suncalc", "version": "1.9.0", "description": "A tiny JavaScript library for calculating sun/moon positions and phases.", "homepage": "https://github.com/mourner/suncalc", "keywords": ["sun", "astronomy", "math", "calculation", "sunrise", "sunset", "twilight", "moon", "illumination"], "author": "<PERSON>", "repository": {"type": "git", "url": "git://github.com/mourner/suncalc.git"}, "main": "suncalc.js", "devDependencies": {"eslint": "^8.8.0", "eslint-config-mourner": "^2.0.1", "tape": "^5.5.0"}, "eslintConfig": {"extends": "mourner", "rules": {"indent": 0, "array-bracket-spacing": 0, "strict": 0, "brace-style": 0}, "env": {"amd": true}}, "files": ["suncalc.js"], "scripts": {"pretest": "eslint suncalc.js test.js", "test": "node test.js", "prepublishOnly": "npm test"}, "jshintConfig": {"quotmark": "single", "trailing": true, "unused": true}}