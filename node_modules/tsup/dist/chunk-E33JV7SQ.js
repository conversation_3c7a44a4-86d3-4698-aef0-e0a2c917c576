"use strict";Object.defineProperty(exports, "__esModule", {value: true});

var _chunkNSPLMNA4js = require('./chunk-NSPLMNA4.js');




var _chunkB7UJSB4Njs = require('./chunk-B7UJSB4N.js');

// node_modules/.pnpm/flat@5.0.2/node_modules/flat/index.js
var require_flat = _chunkB7UJSB4Njs.__commonJS.call(void 0, {
  "node_modules/.pnpm/flat@5.0.2/node_modules/flat/index.js"(exports, module) {
    "use strict";
    module.exports = flatten;
    flatten.flatten = flatten;
    flatten.unflatten = unflatten;
    function isBuffer(obj) {
      return obj && obj.constructor && typeof obj.constructor.isBuffer === "function" && obj.constructor.isBuffer(obj);
    }
    function keyIdentity(key) {
      return key;
    }
    function flatten(target, opts) {
      opts = opts || {};
      const delimiter = opts.delimiter || ".";
      const maxDepth = opts.maxDepth;
      const transformKey = opts.transformKey || keyIdentity;
      const output = {};
      function step(object, prev, currentDepth) {
        currentDepth = currentDepth || 1;
        Object.keys(object).forEach(function(key) {
          const value = object[key];
          const isarray = opts.safe && Array.isArray(value);
          const type = Object.prototype.toString.call(value);
          const isbuffer = isBuffer(value);
          const isobject = type === "[object Object]" || type === "[object Array]";
          const newKey = prev ? prev + delimiter + transformKey(key) : transformKey(key);
          if (!isarray && !isbuffer && isobject && Object.keys(value).length && (!opts.maxDepth || currentDepth < maxDepth)) {
            return step(value, newKey, currentDepth + 1);
          }
          output[newKey] = value;
        });
      }
      step(target);
      return output;
    }
    function unflatten(target, opts) {
      opts = opts || {};
      const delimiter = opts.delimiter || ".";
      const overwrite = opts.overwrite || false;
      const transformKey = opts.transformKey || keyIdentity;
      const result = {};
      const isbuffer = isBuffer(target);
      if (isbuffer || Object.prototype.toString.call(target) !== "[object Object]") {
        return target;
      }
      function getkey(key) {
        const parsedKey = Number(key);
        return isNaN(parsedKey) || key.indexOf(".") !== -1 || opts.object ? key : parsedKey;
      }
      function addKeys(keyPrefix, recipient, target2) {
        return Object.keys(target2).reduce(function(result2, key) {
          result2[keyPrefix + delimiter + key] = target2[key];
          return result2;
        }, recipient);
      }
      function isEmpty(val) {
        const type = Object.prototype.toString.call(val);
        const isArray = type === "[object Array]";
        const isObject = type === "[object Object]";
        if (!val) {
          return true;
        } else if (isArray) {
          return !val.length;
        } else if (isObject) {
          return !Object.keys(val).length;
        }
      }
      target = Object.keys(target).reduce(function(result2, key) {
        const type = Object.prototype.toString.call(target[key]);
        const isObject = type === "[object Object]" || type === "[object Array]";
        if (!isObject || isEmpty(target[key])) {
          result2[key] = target[key];
          return result2;
        } else {
          return addKeys(
            key,
            result2,
            flatten(target[key], opts)
          );
        }
      }, {});
      Object.keys(target).forEach(function(key) {
        const split = key.split(delimiter).map(transformKey);
        let key1 = getkey(split.shift());
        let key2 = getkey(split[0]);
        let recipient = result;
        while (key2 !== void 0) {
          if (key1 === "__proto__") {
            return;
          }
          const type = Object.prototype.toString.call(recipient[key1]);
          const isobject = type === "[object Object]" || type === "[object Array]";
          if (!overwrite && !isobject && typeof recipient[key1] !== "undefined") {
            return;
          }
          if (overwrite && !isobject || !overwrite && recipient[key1] == null) {
            recipient[key1] = typeof key2 === "number" && !opts.object ? [] : {};
          }
          recipient = recipient[key1];
          if (split.length > 0) {
            key1 = getkey(split.shift());
            key2 = getkey(split[0]);
          }
        }
        recipient[key1] = unflatten(target[key], opts);
      });
      return result;
    }
  }
});

// src/cli-main.ts
var import_flat = _chunkB7UJSB4Njs.__toESM.call(void 0, require_flat());
var _cac = require('cac');
function ensureArray(input) {
  return Array.isArray(input) ? input : input.split(",");
}
async function main(options = {}) {
  const cli = _cac.cac.call(void 0, "tsup");
  cli.command("[...files]", "Bundle files", {
    ignoreOptionDefaultValue: true
  }).option("--entry.* <file>", "Use a key-value pair as entry files").option("-d, --out-dir <dir>", "Output directory", { default: "dist" }).option("--format <format>", 'Bundle format, "cjs", "iife", "esm"', {
    default: "cjs"
  }).option("--minify [terser]", "Minify bundle").option("--minify-whitespace", "Minify whitespace").option("--minify-identifiers", "Minify identifiers").option("--minify-syntax", "Minify syntax").option(
    "--keep-names",
    "Keep original function and class names in minified code"
  ).option("--target <target>", 'Bundle target, "es20XX" or "esnext"', {
    default: "es2017"
  }).option(
    "--legacy-output",
    "Output different formats to different folder instead of using different extensions"
  ).option("--dts [entry]", "Generate declaration file").option("--dts-resolve", "Resolve externals types used for d.ts files").option("--dts-only", "Emit declaration files only").option(
    "--sourcemap [inline]",
    "Generate external sourcemap, or inline source: --sourcemap inline"
  ).option(
    "--watch [path]",
    'Watch mode, if path is not specified, it watches the current folder ".". Repeat "--watch" for more than one path'
  ).option("--ignore-watch <path>", "Ignore custom paths in watch mode").option(
    "--onSuccess <command>",
    "Execute command after successful build, specially useful for watch mode"
  ).option("--env.* <value>", "Define compile-time env variables").option(
    "--inject <file>",
    "Replace a global variable with an import from another file"
  ).option("--define.* <value>", "Define compile-time constants").option(
    "--external <name>",
    "Mark specific packages / package.json (dependencies and peerDependencies) as external"
  ).option("--global-name <name>", "Global variable name for iife format").option("--jsxFactory <jsxFactory>", "Name of JSX factory function", {
    default: "React.createElement"
  }).option("--jsxFragment <jsxFragment>", "Name of JSX fragment function", {
    default: "React.Fragment"
  }).option("--replaceNodeEnv", "Replace process.env.NODE_ENV").option("--no-splitting", "Disable code splitting").option("--clean", "Clean output directory").option(
    "--silent",
    'Suppress non-error logs (excluding "onSuccess" process output)'
  ).option("--pure <express>", "Mark specific expressions as pure").option("--metafile", "Emit esbuild metafile (a JSON file)").option("--platform <platform>", "Target platform", {
    default: "node"
  }).option("--loader <ext=loader>", "Specify the loader for a file extension").option("--tsconfig <filename>", "Use a custom tsconfig").option("--config <filename>", "Use a custom config file").option("--no-config", "Disable config file").option("--shims", "Enable cjs and esm shims").option("--inject-style", "Inject style tag to document head").option(
    "--treeshake [strategy]",
    'Using Rollup for treeshaking instead, "recommended" or "smallest" or "safest"'
  ).option("--publicDir [dir]", "Copy public directory to output directory").option(
    "--killSignal <signal>",
    'Signal to kill child process, "SIGTERM" or "SIGKILL"'
  ).option("--cjsInterop", "Enable cjs interop").action(async (files, flags) => {
    const { build } = await Promise.resolve().then(() => require("./index.js"));
    Object.assign(options, {
      ...flags
    });
    if (!options.entry && files.length > 0) {
      options.entry = files.map(_chunkB7UJSB4Njs.slash);
    }
    if (flags.format) {
      const format = ensureArray(flags.format);
      options.format = format;
    }
    if (flags.external) {
      const external = ensureArray(flags.external);
      options.external = external;
    }
    if (flags.target) {
      options.target = flags.target.indexOf(",") >= 0 ? flags.target.split(",") : flags.target;
    }
    if (flags.dts || flags.dtsResolve || flags.dtsOnly) {
      options.dts = {};
      if (typeof flags.dts === "string") {
        options.dts.entry = flags.dts;
      }
      if (flags.dtsResolve) {
        options.dts.resolve = flags.dtsResolve;
      }
      if (flags.dtsOnly) {
        options.dts.only = true;
      }
    }
    if (flags.inject) {
      const inject = ensureArray(flags.inject);
      options.inject = inject;
    }
    if (flags.define) {
      const define = (0, import_flat.default)(flags.define);
      options.define = define;
    }
    if (flags.loader) {
      const loader = ensureArray(flags.loader);
      options.loader = loader.reduce((result, item) => {
        const parts = item.split("=");
        return {
          ...result,
          [parts[0]]: parts[1]
        };
      }, {});
    }
    await build(options);
  });
  cli.help();
  cli.version(_chunkNSPLMNA4js.version);
  cli.parse(process.argv, { run: false });
  await cli.runMatchedCommand();
}



exports.main = main;
