/*
 MIT


    Astronomy library for JavaScript (browser and Node.js).
    https://github.com/cosinekitty/astronomy

    MIT License

    Copyright (c) 2019-2023 Don Cross <<EMAIL>>

    Permission is hereby granted, free of charge, to any person obtaining a copy
    of this software and associated documentation files (the "Software"), to deal
    in the Software without restriction, including without limitation the rights
    to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
    copies of the Software, and to permit persons to whom the Software is
    furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice shall be included in all
    copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
    AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
    OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
    SOFTWARE.
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.createTemplateTagFirstArg=function(a){return a.raw=a};$jscomp.createTemplateTagFirstArgWithRaw=function(a,b){a.raw=b;return a};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};$jscomp.arrayIterator=function(a){return{next:$jscomp.arrayIteratorImpl(a)}};$jscomp.makeIterator=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):$jscomp.arrayIterator(a)};
$jscomp.getGlobal=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;
$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};
$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(a,b){var c=$jscomp.propertyToPolyfillSymbol[b];if(null==c)return a[b];c=a[c];return void 0!==c?c:a[b]};$jscomp.polyfill=function(a,b,c,d){b&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(a,b,c,d):$jscomp.polyfillUnisolated(a,b,c,d))};
$jscomp.polyfillUnisolated=function(a,b,c,d){c=$jscomp.global;a=a.split(".");for(d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))return;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&$jscomp.defineProperty(c,a,{configurable:!0,writable:!0,value:b})};
$jscomp.polyfillIsolated=function(a,b,c,d){var e=a.split(".");a=1===e.length;d=e[0];d=!a&&d in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var f=0;f<e.length-1;f++){var g=e[f];if(!(g in d))return;d=d[g]}e=e[e.length-1];c=$jscomp.IS_SYMBOL_NATIVE&&"es6"===c?d[e]:null;b=b(c);null!=b&&(a?$jscomp.defineProperty($jscomp.polyfills,e,{configurable:!0,writable:!0,value:b}):b!==c&&(void 0===$jscomp.propertyToPolyfillSymbol[e]&&($jscomp.propertyToPolyfillSymbol[e]=$jscomp.IS_SYMBOL_NATIVE?$jscomp.global.Symbol(e):
$jscomp.POLYFILL_PREFIX+e),$jscomp.defineProperty(d,$jscomp.propertyToPolyfillSymbol[e],{configurable:!0,writable:!0,value:b})))};$jscomp.polyfill("Math.log10",function(a){return a?a:function(b){return Math.log(b)/Math.LN10}},"es6","es3");$jscomp.polyfill("Number.isFinite",function(a){return a?a:function(b){return"number"!==typeof b?!1:!isNaN(b)&&Infinity!==b&&-Infinity!==b}},"es6","es3");
$jscomp.polyfill("Math.hypot",function(a){return a?a:function(b){if(2>arguments.length)return arguments.length?Math.abs(arguments[0]):0;var c,d,e;for(c=e=0;c<arguments.length;c++)e=Math.max(e,Math.abs(arguments[c]));if(1E100<e||1E-100>e){if(!e)return e;for(c=d=0;c<arguments.length;c++){var f=Number(arguments[c])/e;d+=f*f}return Math.sqrt(d)*e}for(c=d=0;c<arguments.length;c++)f=Number(arguments[c]),d+=f*f;return Math.sqrt(d)}},"es6","es3");
$jscomp.polyfill("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991},"es6","es3");$jscomp.polyfill("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}},"es6","es3");$jscomp.polyfill("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}},"es6","es3");
$jscomp.polyfill("Math.cbrt",function(a){return a?a:function(b){if(0===b)return b;b=Number(b);var c=Math.pow(Math.abs(b),1/3);return 0>b?-c:c}},"es6","es3");Object.defineProperty(exports,"__esModule",{value:!0});
exports.GeoEmbState=exports.GeoMoonState=exports.EclipticGeoMoon=exports.GeoMoon=exports.Ecliptic=exports.ObserverGravity=exports.VectorObserver=exports.ObserverState=exports.ObserverVector=exports.Equator=exports.SunPosition=exports.Observer=exports.Horizon=exports.EclipticCoordinates=exports.HorizontalCoordinates=exports.MakeRotation=exports.RotationMatrix=exports.EquatorialCoordinates=exports.Spherical=exports.StateVector=exports.Vector=exports.SiderealTime=exports.Libration=exports.LibrationInfo=
exports.CalcMoonCount=exports.e_tilt=exports.MakeTime=exports.AstroTime=exports.SetDeltaTFunction=exports.DeltaT_JplHorizons=exports.DeltaT_EspenakMeeus=exports.PlanetOrbitalPeriod=exports.DefineStar=exports.Body=exports.AngleBetween=exports.MassProduct=exports.CALLISTO_RADIUS_KM=exports.GANYMEDE_RADIUS_KM=exports.EUROPA_RADIUS_KM=exports.IO_RADIUS_KM=exports.JUPITER_MEAN_RADIUS_KM=exports.JUPITER_POLAR_RADIUS_KM=exports.JUPITER_EQUATORIAL_RADIUS_KM=exports.RAD2HOUR=exports.RAD2DEG=exports.HOUR2RAD=
exports.DEG2RAD=exports.AU_PER_LY=exports.KM_PER_AU=exports.C_AUDAY=void 0;
exports.VectorFromHorizon=exports.HorizonFromVector=exports.SphereFromVector=exports.EquatorFromVector=exports.VectorFromSphere=exports.Pivot=exports.IdentityMatrix=exports.CombineRotation=exports.InverseRotation=exports.NextPlanetApsis=exports.SearchPlanetApsis=exports.NextLunarApsis=exports.SearchLunarApsis=exports.Apsis=exports.ApsisKind=exports.SearchPeakMagnitude=exports.SearchMaxElongation=exports.Elongation=exports.ElongationEvent=exports.Seasons=exports.SeasonInfo=exports.HourAngle=exports.SearchHourAngle=
exports.HourAngleEvent=exports.SearchAltitude=exports.SearchRiseSet=exports.Atmosphere=exports.AtmosphereInfo=exports.NextMoonQuarter=exports.SearchMoonQuarter=exports.MoonQuarter=exports.SearchMoonPhase=exports.MoonPhase=exports.SearchRelativeLongitude=exports.Illumination=exports.IlluminationInfo=exports.EclipticLongitude=exports.AngleFromSun=exports.PairLongitude=exports.SearchSunLongitude=exports.Search=exports.HelioState=exports.BaryState=exports.GeoVector=exports.BackdatePosition=exports.CorrectLightTravel=
exports.HelioDistance=exports.HelioVector=exports.JupiterMoons=exports.JupiterMoonsInfo=void 0;
exports.GravitySimulator=exports.LagrangePointFast=exports.LagrangePoint=exports.RotationAxis=exports.AxisInfo=exports.NextMoonNode=exports.SearchMoonNode=exports.NodeEventInfo=exports.NodeEventKind=exports.NextTransit=exports.SearchTransit=exports.TransitInfo=exports.NextLocalSolarEclipse=exports.SearchLocalSolarEclipse=exports.LocalSolarEclipseInfo=exports.EclipseEvent=exports.NextGlobalSolarEclipse=exports.SearchGlobalSolarEclipse=exports.NextLunarEclipse=exports.GlobalSolarEclipseInfo=exports.SearchLunarEclipse=
exports.LunarEclipseInfo=exports.EclipseKind=exports.Constellation=exports.ConstellationInfo=exports.Rotation_EQD_ECT=exports.Rotation_ECT_EQD=exports.Rotation_GAL_EQJ=exports.Rotation_EQJ_GAL=exports.Rotation_HOR_ECL=exports.Rotation_ECL_HOR=exports.Rotation_ECL_EQD=exports.Rotation_EQD_ECL=exports.Rotation_EQJ_HOR=exports.Rotation_HOR_EQJ=exports.Rotation_HOR_EQD=exports.Rotation_EQD_HOR=exports.Rotation_EQD_EQJ=exports.Rotation_ECT_EQJ=exports.Rotation_EQJ_ECT=exports.Rotation_EQJ_EQD=exports.Rotation_ECL_EQJ=
exports.Rotation_EQJ_ECL=exports.RotateState=exports.RotateVector=exports.InverseRefraction=exports.Refraction=void 0;exports.C_AUDAY=173.1446326846693;exports.KM_PER_AU=1.4959787069098932E8;exports.AU_PER_LY=63241.07708807546;exports.DEG2RAD=.017453292519943295;exports.HOUR2RAD=.26179938779914946;exports.RAD2DEG=57.29577951308232;exports.RAD2HOUR=3.819718634205488;exports.JUPITER_EQUATORIAL_RADIUS_KM=71492;exports.JUPITER_POLAR_RADIUS_KM=66854;exports.JUPITER_MEAN_RADIUS_KM=69911;
exports.IO_RADIUS_KM=1821.6;exports.EUROPA_RADIUS_KM=1560.8;exports.GANYMEDE_RADIUS_KM=2631.2;exports.CALLISTO_RADIUS_KM=2410.3;
var DAYS_PER_TROPICAL_YEAR=365.24217,J2000=new Date("2000-01-01T12:00:00Z"),PI2=2*Math.PI,ARC=180/Math.PI*3600,ASEC2RAD=4.84813681109536E-6,ASEC180=648E3,ASEC360=2*ASEC180,ANGVEL=7.292115E-5,AU_PER_PARSEC=ASEC180/Math.PI,SUN_MAG_1AU=-.17-5*Math.log10(AU_PER_PARSEC),MEAN_SYNODIC_MONTH=29.530588,SECONDS_PER_DAY=86400,MILLIS_PER_DAY=1E3*SECONDS_PER_DAY,SOLAR_DAYS_PER_SIDEREAL_DAY=.9972695717592592,SUN_RADIUS_KM=695700,SUN_RADIUS_AU=SUN_RADIUS_KM/exports.KM_PER_AU,EARTH_FLATTENING=.996647180302104,EARTH_FLATTENING_SQUARED=
EARTH_FLATTENING*EARTH_FLATTENING,EARTH_EQUATORIAL_RADIUS_KM=6378.1366,EARTH_EQUATORIAL_RADIUS_AU=EARTH_EQUATORIAL_RADIUS_KM/exports.KM_PER_AU,EARTH_POLAR_RADIUS_KM=EARTH_EQUATORIAL_RADIUS_KM*EARTH_FLATTENING,EARTH_MEAN_RADIUS_KM=6371,EARTH_ATMOSPHERE_KM=88,EARTH_ECLIPSE_RADIUS_KM=EARTH_MEAN_RADIUS_KM+EARTH_ATMOSPHERE_KM,MOON_EQUATORIAL_RADIUS_KM=1738.1,MOON_EQUATORIAL_RADIUS_AU=MOON_EQUATORIAL_RADIUS_KM/exports.KM_PER_AU,MOON_MEAN_RADIUS_KM=1737.4,MOON_POLAR_RADIUS_KM=1736,MOON_POLAR_RADIUS_AU=MOON_POLAR_RADIUS_KM/
exports.KM_PER_AU,REFRACTION_NEAR_HORIZON=34/60,EARTH_MOON_MASS_RATIO=81.30056,SUN_GM=2.959122082855911E-4,MERCURY_GM=4.912547451450812E-11,VENUS_GM=7.243452486162703E-10,EARTH_GM=8.887692390113509E-10,MARS_GM=9.549535105779258E-11,JUPITER_GM=2.825345909524226E-7,SATURN_GM=8.459715185680659E-8,URANUS_GM=1.292024916781969E-8,NEPTUNE_GM=1.524358900784276E-8,PLUTO_GM=2.18869976542597E-12,MOON_GM=EARTH_GM/EARTH_MOON_MASS_RATIO;
function MassProduct(a){switch(a){case Body.Sun:return SUN_GM;case Body.Mercury:return MERCURY_GM;case Body.Venus:return VENUS_GM;case Body.Earth:return EARTH_GM;case Body.Moon:return MOON_GM;case Body.EMB:return EARTH_GM+MOON_GM;case Body.Mars:return MARS_GM;case Body.Jupiter:return JUPITER_GM;case Body.Saturn:return SATURN_GM;case Body.Uranus:return URANUS_GM;case Body.Neptune:return NEPTUNE_GM;case Body.Pluto:return PLUTO_GM;default:throw"Do not know mass product for body: "+a;}}
exports.MassProduct=MassProduct;function VerifyBoolean(a){if(!0!==a&&!1!==a)throw console.trace(),"Value is not boolean: "+a;return a}function VerifyNumber(a){if(!Number.isFinite(a))throw console.trace(),"Value is not a finite number: "+a;return a}function Frac(a){return a-Math.floor(a)}
function AngleBetween(a,b){var c=a.x*a.x+a.y*a.y+a.z*a.z;if(1E-8>Math.abs(c))throw"AngleBetween: first vector is too short.";var d=b.x*b.x+b.y*b.y+b.z*b.z;if(1E-8>Math.abs(d))throw"AngleBetween: second vector is too short.";a=(a.x*b.x+a.y*b.y+a.z*b.z)/Math.sqrt(c*d);return-1>=a?180:1<=a?0:exports.RAD2DEG*Math.acos(a)}exports.AngleBetween=AngleBetween;var Body;
(function(a){a.Sun="Sun";a.Moon="Moon";a.Mercury="Mercury";a.Venus="Venus";a.Earth="Earth";a.Mars="Mars";a.Jupiter="Jupiter";a.Saturn="Saturn";a.Uranus="Uranus";a.Neptune="Neptune";a.Pluto="Pluto";a.SSB="SSB";a.EMB="EMB";a.Star1="Star1";a.Star2="Star2";a.Star3="Star3";a.Star4="Star4";a.Star5="Star5";a.Star6="Star6";a.Star7="Star7";a.Star8="Star8"})(Body=exports.Body||(exports.Body={}));
var StarList=[Body.Star1,Body.Star2,Body.Star3,Body.Star4,Body.Star5,Body.Star6,Body.Star7,Body.Star8],StarTable=[{ra:0,dec:0,dist:0},{ra:0,dec:0,dist:0},{ra:0,dec:0,dist:0},{ra:0,dec:0,dist:0},{ra:0,dec:0,dist:0},{ra:0,dec:0,dist:0},{ra:0,dec:0,dist:0},{ra:0,dec:0,dist:0}];function GetStar(a){a=StarList.indexOf(a);return 0<=a?StarTable[a]:null}function UserDefinedStar(a){return(a=GetStar(a))&&0<a.dist?a:null}
function DefineStar(a,b,c,d){var e=GetStar(a);if(!e)throw"Invalid star body: "+a;VerifyNumber(b);VerifyNumber(c);VerifyNumber(d);if(0>b||24<=b)throw"Invalid right ascension for star: "+b;if(-90>c||90<c)throw"Invalid declination for star: "+c;if(1>d)throw"Invalid star distance: "+d;e.ra=b;e.dec=c;e.dist=d*exports.AU_PER_LY}exports.DefineStar=DefineStar;var PrecessDirection;(function(a){a[a.From2000=0]="From2000";a[a.Into2000=1]="Into2000"})(PrecessDirection||(PrecessDirection={}));
var Planet={Mercury:{OrbitalPeriod:87.969},Venus:{OrbitalPeriod:224.701},Earth:{OrbitalPeriod:365.256},Mars:{OrbitalPeriod:686.98},Jupiter:{OrbitalPeriod:4332.589},Saturn:{OrbitalPeriod:10759.22},Uranus:{OrbitalPeriod:30685.4},Neptune:{OrbitalPeriod:60189},Pluto:{OrbitalPeriod:90560}};function PlanetOrbitalPeriod(a){if(a in Planet)return Planet[a].OrbitalPeriod;throw"Unknown orbital period for: "+a;}exports.PlanetOrbitalPeriod=PlanetOrbitalPeriod;
var vsop={Mercury:[[[[4.40250710144,0,0],[.40989414977,1.48302034195,26087.9031415742],[.050462942,4.47785489551,52175.8062831484],[.00855346844,1.16520322459,78263.70942472259],[.00165590362,4.11969163423,104351.61256629678],[3.4561897E-4,.77930768443,130439.51570787099],[7.583476E-5,3.71348404924,156527.41884944518]],[[26087.90313685529,0,0],[.01131199811,6.21874197797,26087.9031415742],[.00292242298,3.04449355541,52175.8062831484],[7.5775081E-4,6.08568821653,78263.70942472259],[1.9676525E-4,2.80965111777,
104351.61256629678]]],[[[.11737528961,1.98357498767,26087.9031415742],[.02388076996,5.03738959686,52175.8062831484],[.01222839532,3.14159265359,0],[.0054325181,1.79644363964,78263.70942472259],[.0012977877,4.83232503958,104351.61256629678],[3.1866927E-4,1.58088495658,130439.51570787099],[7.963301E-5,4.60972126127,156527.41884944518]],[[.00274646065,3.95008450011,26087.9031415742],[9.9737713E-4,3.14159265359,0]]],[[[.39528271651,0,0],[.07834131818,6.19233722598,26087.9031415742],[.00795525558,2.95989690104,
52175.8062831484],[.00121281764,6.01064153797,78263.70942472259],[2.1921969E-4,2.77820093972,104351.61256629678],[4.354065E-5,5.82894543774,130439.51570787099]],[[.0021734774,4.65617158665,26087.9031415742],[4.4141826E-4,1.42385544001,52175.8062831484]]]],Venus:[[[[3.17614666774,0,0],[.01353968419,5.59313319619,10213.285546211],[8.9891645E-4,5.30650047764,20426.571092422],[5.477194E-5,4.41630661466,7860.4193924392],[3.455741E-5,2.6996444782,11790.6290886588],[2.372061E-5,2.99377542079,3930.2096962196],
[1.317168E-5,5.18668228402,26.2983197998],[1.664146E-5,4.25018630147,1577.3435424478],[1.438387E-5,4.15745084182,9683.5945811164],[1.200521E-5,6.15357116043,30639.856638633]],[[10213.28554621638,0,0],[9.5617813E-4,2.4640651111,10213.285546211],[7.787201E-5,.6247848222,20426.571092422]]],[[[.05923638472,.26702775812,10213.285546211],[4.0107978E-4,1.14737178112,20426.571092422],[3.2814918E-4,3.14159265359,0]],[[.00287821243,1.88964962838,10213.285546211]]],[[[.72334820891,0,0],[.00489824182,4.02151831717,
10213.285546211],[1.658058E-5,4.90206728031,20426.571092422],[1.378043E-5,1.12846591367,11790.6290886588],[1.632096E-5,2.84548795207,7860.4193924392],[4.98395E-6,2.58682193892,9683.5945811164],[2.21985E-6,2.01346696541,19367.1891622328],[2.37454E-6,2.55136053886,15720.8387848784]],[[3.4551041E-4,.89198706276,10213.285546211]]]],Earth:[[[[1.75347045673,0,0],[.03341656453,4.66925680415,6283.0758499914],[3.4894275E-4,4.62610242189,12566.1516999828],[3.417572E-5,2.82886579754,3.523118349],[3.497056E-5,
2.74411783405,5753.3848848968],[3.135899E-5,3.62767041756,77713.7714681205],[2.676218E-5,4.41808345438,7860.4193924392],[2.342691E-5,6.13516214446,3930.2096962196],[1.273165E-5,2.03709657878,529.6909650946],[1.324294E-5,.74246341673,11506.7697697936],[9.01854E-6,2.04505446477,26.2983197998],[1.199167E-5,1.10962946234,1577.3435424478],[8.57223E-6,3.50849152283,398.1490034082],[7.79786E-6,1.17882681962,5223.6939198022],[9.9025E-6,5.23268072088,5884.9268465832],[7.53141E-6,2.53339052847,5507.5532386674],
[5.05267E-6,4.58292599973,18849.2275499742],[4.92392E-6,4.20505711826,775.522611324],[3.56672E-6,2.91954114478,.0673103028],[2.84125E-6,1.89869240932,796.2980068164],[2.42879E-6,.34481445893,5486.777843175],[3.17087E-6,5.84901948512,11790.6290886588],[2.71112E-6,.31486255375,10977.078804699],[2.06217E-6,4.80646631478,2544.3144198834],[2.05478E-6,1.86953770281,5573.1428014331],[2.02318E-6,2.45767790232,6069.7767545534],[1.26225E-6,1.08295459501,20.7753954924],[1.55516E-6,.83306084617,213.299095438]],
[[6283.0758499914,0,0],[.00206058863,2.67823455808,6283.0758499914],[4.303419E-5,2.63512233481,12566.1516999828]],[[8.721859E-5,1.07253635559,6283.0758499914]]],[[],[[.00227777722,3.4137662053,6283.0758499914],[3.805678E-5,3.37063423795,12566.1516999828]]],[[[1.00013988784,0,0],[.01670699632,3.09846350258,6283.0758499914],[1.3956024E-4,3.05524609456,12566.1516999828],[3.08372E-5,5.19846674381,77713.7714681205],[1.628463E-5,1.17387558054,5753.3848848968],[1.575572E-5,2.84685214877,7860.4193924392],
[9.24799E-6,5.45292236722,11506.7697697936],[5.42439E-6,4.56409151453,3930.2096962196],[4.7211E-6,3.66100022149,5884.9268465832],[8.5831E-7,1.27079125277,161000.6857376741],[5.7056E-7,2.01374292245,83996.84731811189],[5.5736E-7,5.2415979917,71430.69561812909],[1.74844E-6,3.01193636733,18849.2275499742],[2.43181E-6,4.2734953079,11790.6290886588]],[[.00103018607,1.10748968172,6283.0758499914],[1.721238E-5,1.06442300386,12566.1516999828]],[[4.359385E-5,5.78455133808,6283.0758499914]]]],Mars:[[[[6.20347711581,
0,0],[.18656368093,5.0503710027,3340.6124266998],[.01108216816,5.40099836344,6681.2248533996],[9.1798406E-4,5.75478744667,10021.8372800994],[2.7744987E-4,5.97049513147,3.523118349],[1.0610235E-4,2.93958560338,2281.2304965106],[1.2315897E-4,.84956094002,2810.9214616052],[8.926784E-5,4.15697846427,.0172536522],[8.715691E-5,6.11005153139,13362.4497067992],[6.797556E-5,.36462229657,398.1490034082],[7.774872E-5,3.33968761376,5621.8429232104],[3.575078E-5,1.6618650571,2544.3144198834],[4.161108E-5,.22814971327,
2942.4634232916],[3.075252E-5,.85696614132,191.4482661116],[2.628117E-5,.64806124465,3337.0893083508],[2.937546E-5,6.07893711402,.0673103028],[2.389414E-5,5.03896442664,796.2980068164],[2.579844E-5,.02996736156,3344.1355450488],[1.528141E-5,1.14979301996,6151.533888305],[1.798806E-5,.65634057445,529.6909650946],[1.264357E-5,3.62275122593,5092.1519581158],[1.286228E-5,3.06796065034,2146.1654164752],[1.546404E-5,2.91579701718,1751.539531416],[1.024902E-5,3.69334099279,8962.4553499102],[8.91566E-6,.18293837498,
16703.062133499],[8.58759E-6,2.4009381194,2914.0142358238],[8.32715E-6,2.46418619474,3340.5951730476],[8.3272E-6,4.49495782139,3340.629680352],[7.12902E-6,3.66335473479,1059.3819301892],[7.48723E-6,3.82248614017,155.4203994342],[7.23861E-6,.67497311481,3738.761430108],[6.35548E-6,2.92182225127,8432.7643848156],[6.55162E-6,.48864064125,3127.3133312618],[5.50474E-6,3.81001042328,.9803210682],[5.5275E-6,4.47479317037,1748.016413067],[4.25966E-6,.55364317304,6283.0758499914],[4.15131E-6,.49662285038,
213.299095438],[4.72167E-6,3.62547124025,1194.4470102246],[3.06551E-6,.38052848348,6684.7479717486],[3.12141E-6,.99853944405,6677.7017350506],[2.93198E-6,4.22131299634,20.7753954924],[3.02375E-6,4.48618007156,3532.0606928114],[2.74027E-6,.54222167059,3340.545116397],[2.81079E-6,5.88163521788,1349.8674096588],[2.31183E-6,1.28242156993,3870.3033917944],[2.83602E-6,5.7688543494,3149.1641605882],[2.36117E-6,5.75503217933,3333.498879699],[2.74033E-6,.13372524985,3340.6797370026],[2.99395E-6,2.78323740866,
6254.6266625236]],[[3340.61242700512,0,0],[.01457554523,3.60433733236,3340.6124266998],[.00168414711,3.92318567804,6681.2248533996],[2.0622975E-4,4.26108844583,10021.8372800994],[3.452392E-5,4.7321039319,3.523118349],[2.586332E-5,4.60670058555,13362.4497067992],[8.41535E-6,4.45864030426,2281.2304965106]],[[5.8152577E-4,2.04961712429,3340.6124266998],[1.3459579E-4,2.45738706163,6681.2248533996]]],[[[.03197134986,3.76832042431,3340.6124266998],[.00298033234,4.10616996305,6681.2248533996],[.00289104742,
0,0],[3.1365539E-4,4.4465105309,10021.8372800994],[3.4841E-5,4.7881254926,13362.4497067992]],[[.00217310991,6.04472194776,3340.6124266998],[2.0976948E-4,3.14159265359,0],[1.2834709E-4,1.60810667915,6681.2248533996]]],[[[1.53033488271,0,0],[.1418495316,3.47971283528,3340.6124266998],[.00660776362,3.81783443019,6681.2248533996],[4.6179117E-4,4.15595316782,10021.8372800994],[8.109733E-5,5.55958416318,2810.9214616052],[7.485318E-5,1.77239078402,5621.8429232104],[5.523191E-5,1.3643630377,2281.2304965106],
[3.82516E-5,4.49407183687,13362.4497067992],[2.306537E-5,.09081579001,2544.3144198834],[1.999396E-5,5.36059617709,3337.0893083508],[2.484394E-5,4.9254563992,2942.4634232916],[1.960195E-5,4.74249437639,3344.1355450488],[1.167119E-5,2.11260868341,5092.1519581158],[1.102816E-5,5.00908403998,398.1490034082],[8.99066E-6,4.40791133207,529.6909650946],[9.92252E-6,5.83861961952,6151.533888305],[8.07354E-6,2.10217065501,1059.3819301892],[7.97915E-6,3.44839203899,796.2980068164],[7.40975E-6,1.49906336885,2146.1654164752]],
[[.01107433345,2.03250524857,3340.6124266998],[.00103175887,2.37071847807,6681.2248533996],[1.28772E-4,0,0],[1.081588E-4,2.70888095665,10021.8372800994]],[[4.4242249E-4,.47930604954,3340.6124266998],[8.138042E-5,.86998389204,6681.2248533996]]]],Jupiter:[[[[.59954691494,0,0],[.09695898719,5.06191793158,529.6909650946],[.00573610142,1.44406205629,7.1135470008],[.00306389205,5.41734730184,1059.3819301892],[9.7178296E-4,4.14264726552,632.7837393132],[7.2903078E-4,3.64042916389,522.5774180938],[6.4263975E-4,
3.41145165351,103.0927742186],[3.9806064E-4,2.29376740788,419.4846438752],[3.8857767E-4,1.27231755835,316.3918696566],[2.7964629E-4,1.7845459182,536.8045120954],[1.358973E-4,5.7748104079,1589.0728952838],[8.246349E-5,3.5822792584,206.1855484372],[8.768704E-5,3.63000308199,949.1756089698],[7.368042E-5,5.0810119427,735.8765135318],[6.26315E-5,.02497628807,213.299095438],[6.114062E-5,4.51319998626,1162.4747044078],[4.905396E-5,1.32084470588,110.2063212194],[5.305285E-5,1.30671216791,14.2270940016],[5.305441E-5,
4.18625634012,1052.2683831884],[4.647248E-5,4.69958103684,3.9321532631],[3.045023E-5,4.31676431084,426.598190876],[2.609999E-5,1.56667394063,846.0828347512],[2.028191E-5,1.06376530715,3.1813937377],[1.764763E-5,2.14148655117,1066.49547719],[1.722972E-5,3.88036268267,1265.5674786264],[1.920945E-5,.97168196472,639.897286314],[1.633223E-5,3.58201833555,515.463871093],[1.431999E-5,4.29685556046,625.6701923124],[9.73272E-6,4.09764549134,95.9792272178]],[[529.69096508814,0,0],[.00489503243,4.2208293947,
529.6909650946],[.00228917222,6.02646855621,7.1135470008],[3.0099479E-4,4.54540782858,1059.3819301892],[2.072092E-4,5.45943156902,522.5774180938],[1.2103653E-4,.16994816098,536.8045120954],[6.067987E-5,4.42422292017,103.0927742186],[5.433968E-5,3.98480737746,419.4846438752],[4.237744E-5,5.89008707199,14.2270940016]],[[4.7233601E-4,4.32148536482,7.1135470008],[3.0649436E-4,2.929777887,529.6909650946],[1.4837605E-4,3.14159265359,0]]],[[[.02268615702,3.55852606721,529.6909650946],[.00109971634,3.90809347197,
1059.3819301892],[.00110090358,0,0],[8.101428E-5,3.60509572885,522.5774180938],[6.043996E-5,4.25883108339,1589.0728952838],[6.437782E-5,.30627119215,536.8045120954]],[[7.8203446E-4,1.52377859742,529.6909650946]]],[[[5.20887429326,0,0],[.25209327119,3.49108639871,529.6909650946],[.00610599976,3.84115365948,1059.3819301892],[.00282029458,2.57419881293,632.7837393132],[.00187647346,2.07590383214,522.5774180938],[8.6792905E-4,.71001145545,419.4846438752],[7.2062974E-4,.21465724607,536.8045120954],[6.5517248E-4,
5.9799588479,316.3918696566],[2.9134542E-4,1.67759379655,103.0927742186],[3.0135335E-4,2.16132003734,949.1756089698],[2.3453271E-4,3.54023522184,735.8765135318],[2.2283743E-4,4.19362594399,1589.0728952838],[2.3947298E-4,.2745803748,7.1135470008],[1.3032614E-4,2.96042965363,1162.4747044078],[9.70336E-5,1.90669633585,206.1855484372],[1.2749023E-4,2.71550286592,1052.2683831884],[7.057931E-5,2.18184839926,1265.5674786264],[6.137703E-5,6.26418240033,846.0828347512],[2.616976E-5,2.00994012876,1581.959348283]],
[[.0127180152,2.64937512894,529.6909650946],[6.1661816E-4,3.00076460387,1059.3819301892],[5.3443713E-4,3.89717383175,522.5774180938],[3.1185171E-4,4.88276958012,536.8045120954],[4.1390269E-4,0,0]]]],Saturn:[[[[.87401354025,0,0],[.11107659762,3.96205090159,213.299095438],[.01414150957,4.58581516874,7.1135470008],[.00398379389,.52112032699,206.1855484372],[.00350769243,3.30329907896,426.598190876],[.00206816305,.24658372002,103.0927742186],[7.92713E-4,3.84007056878,220.4126424388],[2.3990355E-4,4.66976924553,
110.2063212194],[1.6573588E-4,.43719228296,419.4846438752],[1.4906995E-4,5.76903183869,316.3918696566],[1.582029E-4,.93809155235,632.7837393132],[1.4609559E-4,1.56518472,3.9321532631],[1.3160301E-4,4.44891291899,14.2270940016],[1.5053543E-4,2.71669915667,639.897286314],[1.3005299E-4,5.98119023644,11.0457002639],[1.0725067E-4,3.12939523827,202.2533951741],[5.863206E-5,.23656938524,529.6909650946],[5.227757E-5,4.20783365759,3.1813937377],[6.126317E-5,1.76328667907,277.0349937414],[5.019687E-5,3.17787728405,
433.7117378768],[4.59255E-5,.61977744975,199.0720014364],[4.005867E-5,2.24479718502,63.7358983034],[2.953796E-5,.98280366998,95.9792272178],[3.87367E-5,3.22283226966,138.5174968707],[2.461186E-5,2.03163875071,735.8765135318],[3.269484E-5,.77492638211,949.1756089698],[1.758145E-5,3.2658010994,522.5774180938],[1.640172E-5,5.5050445305,846.0828347512],[1.391327E-5,4.02333150505,323.5054166574],[1.580648E-5,4.37265307169,309.2783226558],[1.123498E-5,2.83726798446,415.5524906121],[1.017275E-5,3.71700135395,
227.5261894396],[8.48642E-6,3.1915017083,209.3669421749]],[[213.2990952169,0,0],[.01297370862,1.82834923978,213.299095438],[.00564345393,2.88499717272,7.1135470008],[9.3734369E-4,1.06311793502,426.598190876],[.00107674962,2.27769131009,206.1855484372],[4.0244455E-4,2.04108104671,220.4126424388],[1.9941774E-4,1.2795439047,103.0927742186],[1.0511678E-4,2.7488034213,14.2270940016],[6.416106E-5,.38238295041,639.897286314],[4.848994E-5,2.43037610229,419.4846438752],[4.056892E-5,2.92133209468,110.2063212194],
[3.768635E-5,3.6496533078,3.9321532631]],[[.0011644133,1.17988132879,7.1135470008],[9.1841837E-4,.0732519584,213.299095438],[3.6661728E-4,0,0],[1.5274496E-4,4.06493179167,206.1855484372]]],[[[.04330678039,3.60284428399,213.299095438],[.00240348302,2.85238489373,426.598190876],[8.4745939E-4,0,0],[3.0863357E-4,3.48441504555,220.4126424388],[3.4116062E-4,.57297307557,206.1855484372],[1.473407E-4,2.11846596715,639.897286314],[9.916667E-5,5.79003188904,419.4846438752],[6.993564E-5,4.7360468972,7.1135470008],
[4.807588E-5,5.43305312061,316.3918696566]],[[.00198927992,4.93901017903,213.299095438],[3.6947916E-4,3.14159265359,0],[1.7966989E-4,.5197943111,426.598190876]]],[[[9.55758135486,0,0],[.52921382865,2.39226219573,213.299095438],[.01873679867,5.2354960466,206.1855484372],[.01464663929,1.64763042902,426.598190876],[.00821891141,5.93520042303,316.3918696566],[.00547506923,5.0153261898,103.0927742186],[.0037168465,2.27114821115,220.4126424388],[.00361778765,3.13904301847,7.1135470008],[.00140617506,5.70406606781,
632.7837393132],[.00108974848,3.29313390175,110.2063212194],[6.9006962E-4,5.94099540992,419.4846438752],[6.1053367E-4,.94037691801,639.897286314],[4.8913294E-4,1.55733638681,202.2533951741],[3.4143772E-4,.19519102597,277.0349937414],[3.2401773E-4,5.47084567016,949.1756089698],[2.0936596E-4,.46349251129,735.8765135318],[9.796004E-5,5.20477537945,1265.5674786264],[1.1993338E-4,5.98050967385,846.0828347512],[2.08393E-4,1.52102476129,433.7117378768],[1.5298404E-4,3.0594381494,529.6909650946],[6.465823E-5,
.17732249942,1052.2683831884],[1.1380257E-4,1.7310542704,522.5774180938],[3.419618E-5,4.94550542171,1581.959348283]],[[.0618298134,.2584351148,213.299095438],[.00506577242,.71114625261,206.1855484372],[.00341394029,5.79635741658,426.598190876],[.00188491195,.47215589652,220.4126424388],[.00186261486,3.14159265359,0],[.00143891146,1.40744822888,7.1135470008]],[[.00436902572,4.78671677509,213.299095438]]]],Uranus:[[[[5.48129294297,0,0],[.09260408234,.89106421507,74.7815985673],[.01504247898,3.6271926092,
1.4844727083],[.00365981674,1.89962179044,73.297125859],[.00272328168,3.35823706307,149.5631971346],[7.0328461E-4,5.39254450063,63.7358983034],[6.8892678E-4,6.09292483287,76.2660712756],[6.1998615E-4,2.26952066061,2.9689454166],[6.1950719E-4,2.85098872691,11.0457002639],[2.646877E-4,3.14152083966,71.8126531507],[2.5710476E-4,6.11379840493,454.9093665273],[2.107885E-4,4.36059339067,148.0787244263],[1.7818647E-4,1.74436930289,36.6485629295],[1.4613507E-4,4.73732166022,3.9321532631],[1.1162509E-4,5.8268179635,
224.3447957019],[1.099791E-4,.48865004018,138.5174968707],[9.527478E-5,2.95516862826,35.1640902212],[7.545601E-5,5.236265824,109.9456887885],[4.220241E-5,3.23328220918,70.8494453042],[4.0519E-5,2.277550173,151.0476698429],[3.354596E-5,1.0654900738,4.4534181249],[2.926718E-5,4.62903718891,9.5612275556],[3.49034E-5,5.48306144511,146.594251718],[3.144069E-5,4.75199570434,77.7505439839],[2.922333E-5,5.35235361027,85.8272988312],[2.272788E-5,4.36600400036,70.3281804424],[2.051219E-5,1.51773566586,.1118745846],
[2.148602E-5,.60745949945,38.1330356378],[1.991643E-5,4.92437588682,277.0349937414],[1.376226E-5,2.04283539351,65.2203710117],[1.666902E-5,3.62744066769,380.12776796],[1.284107E-5,3.11347961505,202.2533951741],[1.150429E-5,.93343589092,3.1813937377],[1.533221E-5,2.58594681212,52.6901980395],[1.281604E-5,.54271272721,222.8603229936],[1.372139E-5,4.19641530878,111.4301614968],[1.221029E-5,.1990065003,108.4612160802],[9.46181E-6,1.19253165736,127.4717966068],[1.150989E-5,4.17898916639,33.6796175129]],
[[74.7815986091,0,0],[.00154332863,5.24158770553,74.7815985673],[2.4456474E-4,1.71260334156,1.4844727083],[9.258442E-5,.4282973235,11.0457002639],[8.265977E-5,1.50218091379,63.7358983034],[9.15016E-5,1.41213765216,149.5631971346]]],[[[.01346277648,2.61877810547,74.7815985673],[6.23414E-4,5.08111189648,149.5631971346],[6.1601196E-4,3.14159265359,0],[9.963722E-5,1.61603805646,76.2660712756],[9.92616E-5,.57630380333,73.297125859]],[[3.4101978E-4,.01321929936,74.7815985673]]],[[[19.21264847206,0,0],[.88784984413,
5.60377527014,74.7815985673],[.03440836062,.32836099706,73.297125859],[.0205565386,1.7829515933,149.5631971346],[.0064932241,4.52247285911,76.2660712756],[.00602247865,3.86003823674,63.7358983034],[.00496404167,1.40139935333,454.9093665273],[.00338525369,1.58002770318,138.5174968707],[.00243509114,1.57086606044,71.8126531507],[.00190522303,1.99809394714,1.4844727083],[.00161858838,2.79137786799,148.0787244263],[.00143706183,1.38368544947,11.0457002639],[9.3192405E-4,.17437220467,36.6485629295],[7.1424548E-4,
4.24509236074,224.3447957019],[8.9806014E-4,3.66105364565,109.9456887885],[3.9009723E-4,1.66971401684,70.8494453042],[4.6677296E-4,1.39976401694,35.1640902212],[3.9025624E-4,3.36234773834,277.0349937414],[3.6755274E-4,3.88649278513,146.594251718],[3.0348723E-4,.70100838798,151.0476698429],[2.9156413E-4,3.180563367,77.7505439839],[2.2637073E-4,.72518687029,529.6909650946],[1.1959076E-4,1.7504339214,984.6003316219],[2.5620756E-4,5.25656086672,380.12776796]],[[.01479896629,3.67205697578,74.7815985673]]]],
Neptune:[[[[5.31188633046,0,0],[.0179847553,2.9010127389,38.1330356378],[.01019727652,.48580922867,1.4844727083],[.00124531845,4.83008090676,36.6485629295],[4.2064466E-4,5.41054993053,2.9689454166],[3.7714584E-4,6.09221808686,35.1640902212],[3.3784738E-4,1.24488874087,76.2660712756],[1.6482741E-4,7.727998E-5,491.5579294568],[9.198584E-5,4.93747051954,39.6175083461],[8.99425E-5,.27462171806,175.1660598002]],[[38.13303563957,0,0],[1.6604172E-4,4.86323329249,1.4844727083],[1.5744045E-4,2.27887427527,
38.1330356378]]],[[[.03088622933,1.44104372644,38.1330356378],[2.7780087E-4,5.91271884599,76.2660712756],[2.7623609E-4,0,0],[1.5355489E-4,2.52123799551,36.6485629295],[1.5448133E-4,3.50877079215,39.6175083461]]],[[[30.07013205828,0,0],[.27062259632,1.32999459377,38.1330356378],[.01691764014,3.25186135653,36.6485629295],[.00807830553,5.18592878704,1.4844727083],[.0053776051,4.52113935896,35.1640902212],[.00495725141,1.5710564165,491.5579294568],[.00274571975,1.84552258866,175.1660598002],[1.201232E-4,
1.92059384991,1021.2488945514],[.00121801746,5.79754470298,76.2660712756],[.00100896068,.3770272493,73.297125859],[.00135134092,3.37220609835,39.6175083461],[7.571796E-5,1.07149207335,388.4651552382]]]]};
function DeltaT_EspenakMeeus(a){var b=2E3+(a-14)/DAYS_PER_TROPICAL_YEAR;if(-500>b)return a=(b-1820)/100,-20+32*a*a;if(500>b){a=b/100;b=a*a;var c=a*b;return 10583.6-1014.41*a+33.78311*b-5.952053*c-.1798452*b*b+.022174192*b*c+.0090316521*c*c}if(1600>b)return a=(b-1E3)/100,b=a*a,c=a*b,1574.2-556.01*a+71.23472*b+.319781*c-.8503463*b*b-.005050998*b*c+.0083572073*c*c;if(1700>b)return a=b-1600,b=a*a,120-.9808*a-.01532*b+a*b/7129;if(1800>b)return a=b-1700,b=a*a,8.83+.1603*a-.0059285*b+1.3336E-4*a*b-b*b/1174E3;
if(1860>b){a=b-1800;b=a*a;c=a*b;var d=b*b;return 13.72-.332447*a+.0068612*b+.0041116*c-3.7436E-4*d+1.21272E-5*b*c-1.699E-7*c*c+8.75E-10*c*d}if(1900>b)return a=b-1860,b=a*a,c=a*b,7.62+.5737*a-.251754*b+.01680668*c-4.473624E-4*b*b+b*c/233174;if(1920>b)return a=b-1900,b=a*a,-2.79+1.494119*a-.0598939*b+.0061966*a*b-1.97E-4*b*b;if(1941>b)return a=b-1920,b=a*a,21.2+.84493*a-.0761*b+.0020936*a*b;if(1961>b)return a=b-1950,b=a*a,29.07+.407*a-b/233+a*b/2547;if(1986>b)return a=b-1975,b=a*a,45.45+1.067*a-b/260-
a*b/718;if(2005>b)return a=b-2E3,b=a*a,c=a*b,63.86+.3345*a-.060374*b+.0017275*c+6.51814E-4*b*b+2.373599E-5*b*c;if(2050>b)return a=b-2E3,62.92+.32217*a+.005589*a*a;if(2150>b)return a=(b-1820)/100,-20+32*a*a-.5628*(2150-b);a=(b-1820)/100;return-20+32*a*a}exports.DeltaT_EspenakMeeus=DeltaT_EspenakMeeus;function DeltaT_JplHorizons(a){return DeltaT_EspenakMeeus(Math.min(a,17*DAYS_PER_TROPICAL_YEAR))}exports.DeltaT_JplHorizons=DeltaT_JplHorizons;var DeltaT=DeltaT_EspenakMeeus;
function SetDeltaTFunction(a){DeltaT=a}exports.SetDeltaTFunction=SetDeltaTFunction;function TerrestrialTime(a){return a+DeltaT(a)/86400}
var AstroTime=function(a){if(a instanceof AstroTime)this.date=a.date,this.ut=a.ut,this.tt=a.tt;else if(a instanceof Date&&Number.isFinite(a.getTime()))this.date=a,this.ut=(a.getTime()-J2000.getTime())/864E5,this.tt=TerrestrialTime(this.ut);else if(Number.isFinite(a))this.date=new Date(J2000.getTime()+864E5*a),this.ut=a,this.tt=TerrestrialTime(this.ut);else throw"Argument must be a Date object, an AstroTime object, or a numeric UTC Julian date.";};
AstroTime.FromTerrestrialTime=function(a){for(var b=new AstroTime(a);;){var c=a-b.tt;if(1E-12>Math.abs(c))return b;b=b.AddDays(c)}};AstroTime.prototype.toString=function(){return this.date.toISOString()};AstroTime.prototype.AddDays=function(a){return new AstroTime(this.ut+a)};exports.AstroTime=AstroTime;function InterpolateTime(a,b,c){return new AstroTime(a.ut+c*(b.ut-a.ut))}function MakeTime(a){return a instanceof AstroTime?a:new AstroTime(a)}exports.MakeTime=MakeTime;
function iau2000b(a){a=a.tt/36525;var b=(1287104.79305+1.295965810481E8*a)%ASEC360*ASEC2RAD,c=(335779.526232+1.7395272628478E9*a)%ASEC360*ASEC2RAD,d=(450160.398036-6962890.5431*a)%ASEC360*ASEC2RAD,e=Math.sin(d),f=Math.cos(d),g=(-172064161-174666*a)*e+33386*f,k=(92052331+9086*a)*f+15377*e;f=2*(c-(1072260.70369+1.602961601209E9*a)%ASEC360*ASEC2RAD+d);e=Math.sin(f);f=Math.cos(f);g+=(-13170906-1675*a)*e-13696*f;k+=(5730336-3015*a)*f-4587*e;f=2*(c+d);e=Math.sin(f);f=Math.cos(f);g+=(-2276413-234*a)*e+2796*
f;k+=(978459-485*a)*f+1374*e;f=2*d;e=Math.sin(f);f=Math.cos(f);g+=(2074554+207*a)*e-698*f;k+=(-897492+470*a)*f-291*e;e=Math.sin(b);f=Math.cos(b);return{dpsi:-1.35E-4+1E-7*(g+((1475877-3633*a)*e+11817*f)),deps:3.88E-4+1E-7*(k+((73871-184*a)*f-1924*e))}}function mean_obliq(a){a=a.tt/36525;return(((((-4.34E-8*a-5.76E-7)*a+.0020034)*a-1.831E-4)*a-46.836769)*a+84381.406)/3600}var cache_e_tilt;
function e_tilt(a){if(!cache_e_tilt||1E-6<Math.abs(cache_e_tilt.tt-a.tt)){var b=iau2000b(a),c=mean_obliq(a);cache_e_tilt={tt:a.tt,dpsi:b.dpsi,deps:b.deps,ee:b.dpsi*Math.cos(c*exports.DEG2RAD)/15,mobl:c,tobl:c+b.deps/3600}}return cache_e_tilt}exports.e_tilt=e_tilt;function obl_ecl2equ_vec(a,b){var c=a*exports.DEG2RAD;a=Math.cos(c);c=Math.sin(c);return[b[0],b[1]*a-b[2]*c,b[1]*c+b[2]*a]}function ecl2equ_vec(a,b){return obl_ecl2equ_vec(mean_obliq(a),b)}exports.CalcMoonCount=0;
function CalcMoon(a){function b(u,v,x,A){for(var w=[],D=0;D<=v-u;++D){var E=w,L=E.push,M,O=x,Q=A,P=[];for(M=0;M<=Q-O;++M)P.push(0);L.call(E,{min:O,array:P})}return{min:u,array:w}}function c(u,v,x){u=u.array[v-u.min];return u.array[x-u.min]}function d(u,v,x){u=r.array[u-r.min];u.array[v-u.min]=x}function e(u,v,x){u=q.array[u-q.min];u.array[v-u.min]=x}function f(u,v,x,A,w){w(u*x-v*A,v*x+u*A)}function g(u){return Math.sin(PI2*u)}function k(u,v,x,A){var w={x:1,y:0};u=[0,u,v,x,A];for(v=1;4>=v;++v)0!==
u[v]&&f(w.x,w.y,c(r,u[v],v),c(q,u[v],v),function(D,E){return w.x=D,w.y=E});return w}function h(u,v,x,A,w,D,E,L){w=k(w,D,E,L);n+=u*w.y;p+=v*w.y;H+=x*w.x;F+=A*w.x}++exports.CalcMoonCount;a=a.tt/36525;var l,m,n,p,r=b(-6,6,1,4),q=b(-6,6,1,4);var t=a*a;var H=p=n=0;var F=3422.7;var C=g(.19833+.05611*a);var B=g(.27869+.04508*a);var y=g(.16827-.36903*a);var z=g(.34734-5.37261*a);var G=g(.10498-5.37899*a);var I=g(.42681-.41855*a),R=g(.14943-5.37511*a);var J=.84*C+.31*B+14.27*y+7.26*z+.28*G+.24*I;var N=2.94*
C+.31*B+14.27*y+9.34*z+1.12*G+.83*I;var K=-6.4*C-1.89*I;B=.21*C+.31*B+14.27*y-88.7*z-15.3*G+.24*I-1.86*R;y=J-K;C=-3.332E-6*g(.59734-5.37261*a)-5.39E-7*g(.35498-5.37899*a)-6.4E-8*g(.39943-5.37511*a);J=PI2*Frac(.60643382+1336.85522467*a-3.13E-6*t)+J/ARC;N=PI2*Frac(.37489701+1325.55240982*a+2.565E-5*t)+N/ARC;K=PI2*Frac(.99312619+99.99735956*a-4.4E-7*t)+K/ARC;B=PI2*Frac(.25909118+1342.2278298*a-8.92E-6*t)+B/ARC;G=PI2*Frac(.82736186+1236.85308708*a-3.97E-6*t)+y/ARC;for(l=1;4>=l;++l){switch(l){case 1:y=
N;t=4;z=1.000002208;break;case 2:y=K;t=3;z=.997504612-.002495388*a;break;case 3:y=B;t=4;z=1.000002708+139.978*C;break;case 4:y=G;t=6;z=1;break;default:throw"Internal error: I = "+l;}d(0,l,1);d(1,l,Math.cos(y)*z);e(0,l,0);e(1,l,Math.sin(y)*z);for(m=2;m<=t;++m)f(c(r,m-1,l),c(q,m-1,l),c(r,1,l),c(q,1,l),function(u,v){return d(m,l,u),e(m,l,v)});for(m=1;m<=t;++m)d(-m,l,c(r,m,l)),e(-m,l,-c(q,m,l))}h(13.902,14.06,-.001,.2607,0,0,0,4);h(.403,-4.01,.394,.0023,0,0,0,3);h(2369.912,2373.36,.601,28.2333,0,0,0,
2);h(-125.154,-112.79,-.725,-.9781,0,0,0,1);h(1.979,6.98,-.445,.0433,1,0,0,4);h(191.953,192.72,.029,3.0861,1,0,0,2);h(-8.466,-13.51,.455,-.1093,1,0,0,1);h(22639.5,22609.07,.079,186.5398,1,0,0,0);h(18.609,3.59,-.094,.0118,1,0,0,-1);h(-4586.465,-4578.13,-.077,34.3117,1,0,0,-2);h(3.215,5.44,.192,-.0386,1,0,0,-3);h(-38.428,-38.64,.001,.6008,1,0,0,-4);h(-.393,-1.43,-.092,.0086,1,0,0,-6);h(-.289,-1.59,.123,-.0053,0,1,0,4);h(-24.42,-25.1,.04,-.3,0,1,0,2);h(18.023,17.93,.007,.1494,0,1,0,1);h(-668.146,-126.98,
-1.302,-.3997,0,1,0,0);h(.56,.32,-.001,-.0037,0,1,0,-1);h(-165.145,-165.06,.054,1.9178,0,1,0,-2);h(-1.877,-6.46,-.416,.0339,0,1,0,-4);h(.213,1.02,-.074,.0054,2,0,0,4);h(14.387,14.78,-.017,.2833,2,0,0,2);h(-.586,-1.2,.054,-.01,2,0,0,1);h(769.016,767.96,.107,10.1657,2,0,0,0);h(1.75,2.01,-.018,.0155,2,0,0,-1);h(-211.656,-152.53,5.679,-.3039,2,0,0,-2);h(1.225,.91,-.03,-.0088,2,0,0,-3);h(-30.773,-34.07,-.308,.3722,2,0,0,-4);h(-.57,-1.4,-.074,.0109,2,0,0,-6);h(-2.921,-11.75,.787,-.0484,1,1,0,2);h(1.267,
1.52,-.022,.0164,1,1,0,1);h(-109.673,-115.18,.461,-.949,1,1,0,0);h(-205.962,-182.36,2.056,1.4437,1,1,0,-2);h(.233,.36,.012,-.0025,1,1,0,-3);h(-4.391,-9.66,-.471,.0673,1,1,0,-4);h(.283,1.53,-.111,.006,1,-1,0,4);h(14.577,31.7,-1.54,.2302,1,-1,0,2);h(147.687,138.76,.679,1.1528,1,-1,0,0);h(-1.089,.55,.021,0,1,-1,0,-1);h(28.475,23.59,-.443,-.2257,1,-1,0,-2);h(-.276,-.38,-.006,-.0036,1,-1,0,-3);h(.636,2.27,.146,-.0102,1,-1,0,-4);h(-.189,-1.68,.131,-.0028,0,2,0,2);h(-7.486,-.66,-.037,-.0086,0,2,0,0);h(-8.096,
-16.35,-.74,.0918,0,2,0,-2);h(-5.741,-.04,0,-9E-4,0,0,2,2);h(.255,0,0,0,0,0,2,1);h(-411.608,-.2,0,-.0124,0,0,2,0);h(.584,.84,0,.0071,0,0,2,-1);h(-55.173,-52.14,0,-.1052,0,0,2,-2);h(.254,.25,0,-.0017,0,0,2,-3);h(.025,-1.67,0,.0031,0,0,2,-4);h(1.06,2.96,-.166,.0243,3,0,0,2);h(36.124,50.64,-1.3,.6215,3,0,0,0);h(-13.193,-16.4,.258,-.1187,3,0,0,-2);h(-1.187,-.74,.042,.0074,3,0,0,-4);h(-.293,-.31,-.002,.0046,3,0,0,-6);h(-.29,-1.45,.116,-.0051,2,1,0,2);h(-7.649,-10.56,.259,-.1038,2,1,0,0);h(-8.627,-7.59,
.078,-.0192,2,1,0,-2);h(-2.74,-2.54,.022,.0324,2,1,0,-4);h(1.181,3.32,-.212,.0213,2,-1,0,2);h(9.703,11.67,-.151,.1268,2,-1,0,0);h(-.352,-.37,.001,-.0028,2,-1,0,-1);h(-2.494,-1.17,-.003,-.0017,2,-1,0,-2);h(.36,.2,-.012,-.0043,2,-1,0,-4);h(-1.167,-1.25,.008,-.0106,1,2,0,0);h(-7.412,-6.12,.117,.0484,1,2,0,-2);h(-.311,-.65,-.032,.0044,1,2,0,-4);h(.757,1.82,-.105,.0112,1,-2,0,2);h(2.58,2.32,.027,.0196,1,-2,0,0);h(2.533,2.4,-.014,-.0212,1,-2,0,-2);h(-.344,-.57,-.025,.0036,0,3,0,-2);h(-.992,-.02,0,0,1,0,
2,2);h(-45.099,-.02,0,-.001,1,0,2,0);h(-.179,-9.52,0,-.0833,1,0,2,-2);h(-.301,-.33,0,.0014,1,0,2,-4);h(-6.382,-3.37,0,-.0481,1,0,-2,2);h(39.528,85.13,0,-.7136,1,0,-2,0);h(9.366,.71,0,-.0112,1,0,-2,-2);h(.202,.02,0,0,1,0,-2,-4);h(.415,.1,0,.0013,0,1,2,0);h(-2.152,-2.26,0,-.0066,0,1,2,-2);h(-1.44,-1.3,0,.0014,0,1,-2,2);h(.384,-.04,0,0,0,1,-2,-2);h(1.938,3.6,-.145,.0401,4,0,0,0);h(-.952,-1.58,.052,-.013,4,0,0,-2);h(-.551,-.94,.032,-.0097,3,1,0,0);h(-.482,-.57,.005,-.0045,3,1,0,-2);h(.681,.96,-.026,.0115,
3,-1,0,0);h(-.297,-.27,.002,-9E-4,2,2,0,-2);h(.254,.21,-.003,0,2,-2,0,-2);h(-.25,-.22,.004,.0014,1,3,0,-2);h(-3.996,0,0,4E-4,2,0,2,0);h(.557,-.75,0,-.009,2,0,2,-2);h(-.459,-.38,0,-.0053,2,0,-2,2);h(-1.298,.74,0,4E-4,2,0,-2,0);h(.538,1.14,0,-.0141,2,0,-2,-2);h(.263,.02,0,0,1,1,2,0);h(.426,.07,0,-6E-4,1,1,-2,-2);h(-.304,.03,0,3E-4,1,-1,2,0);h(-.372,-.19,0,-.0027,1,-1,-2,2);h(.418,0,0,0,0,0,4,0);h(-.33,-.04,0,0,3,0,2,0);t=-526.069*k(0,0,1,-2).y;t+=-3.352*k(0,0,1,-4).y;t+=44.297*k(1,0,1,-2).y;t+=-6*k(1,
0,1,-4).y;t+=20.599*k(-1,0,1,0).y;t+=-30.598*k(-1,0,1,-2).y;t+=-24.649*k(-2,0,1,0).y;t+=-2*k(-2,0,1,-2).y;t+=-22.571*k(0,1,1,-2).y;t+=10.985*k(0,-1,1,-2).y;n+=.82*g(.7736-62.5512*a)+.31*g(.0466-125.1025*a)+.35*g(.5785-25.1042*a)+.66*g(.4591+1335.8075*a)+.64*g(.313-91.568*a)+1.14*g(.148+1331.2898*a)+.21*g(.5918+1056.5859*a)+.44*g(.5784+1322.8595*a)+.24*g(.2275-5.7374*a)+.28*g(.2965+2.6929*a)+.33*g(.3132+6.3368*a);a=B+p/ARC;a=(1.000002708+139.978*C)*(18518.511+1.189+H)*Math.sin(a)-6.24*Math.sin(3*a)+
t;return{geo_eclip_lon:PI2*Frac((J+n/ARC)/PI2),geo_eclip_lat:Math.PI/648E3*a,distance_au:ARC*EARTH_EQUATORIAL_RADIUS_AU/(.999953253*F)}}var LibrationInfo=function(a,b,c,d,e,f){this.elat=a;this.elon=b;this.mlat=c;this.mlon=d;this.dist_km=e;this.diam_deg=f};exports.LibrationInfo=LibrationInfo;
function Libration(a){var b=MakeTime(a);a=b.tt/36525;var c=a*a,d=c*a,e=c*c,f=CalcMoon(b);b=f.geo_eclip_lon;var g=f.geo_eclip_lat;f=f.distance_au*exports.KM_PER_AU;var k=1.543*exports.DEG2RAD,h=exports.DEG2RAD*NormalizeLongitude(93.272095+483202.0175233*a-.0036539*c-d/3526E3+e/86331E4),l=exports.DEG2RAD*NormalizeLongitude(125.0445479-1934.1362891*a+.0020754*c+d/467441-e/60616E3),m=exports.DEG2RAD*NormalizeLongitude(357.5291092+35999.0502909*a-1.536E-4*c+d/2449E4),n=exports.DEG2RAD*NormalizeLongitude(134.9633964+
477198.8675055*a+.0087414*c+d/69699-e/14712E3);d=exports.DEG2RAD*NormalizeLongitude(297.8501921+445267.1114034*a-.0018819*c+d/545868-e/113065E3);c=1-.002516*a-7.4E-6*c;var p=b-l;e=Math.atan2(Math.sin(p)*Math.cos(g)*Math.cos(k)-Math.sin(g)*Math.sin(k),Math.cos(p)*Math.cos(g));var r=LongitudeOffset(exports.RAD2DEG*(e-h));k=Math.asin(-Math.sin(p)*Math.cos(g)*Math.sin(k)-Math.sin(g)*Math.cos(k));p=-.02752*Math.cos(n)+-.02245*Math.sin(h)+.00684*Math.cos(n-2*h)+-.00293*Math.cos(2*h)+-8.5E-4*Math.cos(2*
h-2*d)+-5.4E-4*Math.cos(n-2*d)+-2E-4*Math.sin(n+h)+-2E-4*Math.cos(n+2*h)+-2E-4*Math.cos(n-h)+1.4E-4*Math.cos(n+2*h-2*d);var q=-.02816*Math.sin(n)+.02244*Math.cos(h)+-.00682*Math.sin(n-2*h)+-.00279*Math.sin(2*h)+-8.3E-4*Math.sin(2*h-2*d)+6.9E-4*Math.sin(n-2*d)+4E-4*Math.cos(n+h)+-2.5E-4*Math.sin(2*n)+-2.3E-4*Math.sin(n+2*h)+2E-4*Math.cos(n-h)+1.9E-4*Math.sin(n-h)+1.3E-4*Math.sin(n+2*h-2*d)+-1E-4*Math.cos(n-3*h);return new LibrationInfo(exports.RAD2DEG*k+(q*Math.cos(e)-p*Math.sin(e)),r+(-(.0252*c*Math.sin(m)+
.00473*Math.sin(2*n-2*h)+-.00467*Math.sin(n)+.00396*Math.sin(exports.DEG2RAD*(119.75+131.849*a))+.00276*Math.sin(2*n-2*d)+.00196*Math.sin(l)+-.00183*Math.cos(n-h)+.00115*Math.sin(n-2*d)+-9.6E-4*Math.sin(n-d)+4.6E-4*Math.sin(2*h-2*d)+-3.9E-4*Math.sin(n-h)+-3.2E-4*Math.sin(n-m-d)+2.7E-4*Math.sin(2*n-m-2*d)+2.3E-4*Math.sin(exports.DEG2RAD*(72.56+20.186*a))+-1.4E-4*Math.sin(2*d)+1.4E-4*Math.cos(2*n-2*h)+-1.2E-4*Math.sin(n-2*h)+-1.2E-4*Math.sin(2*n)+1.1E-4*Math.sin(2*n-2*m-2*d))+(p*Math.cos(e)+q*Math.sin(e))*
Math.tan(k)),exports.RAD2DEG*g,exports.RAD2DEG*b,f,2*exports.RAD2DEG*Math.atan(MOON_MEAN_RADIUS_KM/Math.sqrt(f*f-MOON_MEAN_RADIUS_KM*MOON_MEAN_RADIUS_KM)))}exports.Libration=Libration;function rotate(a,b){return[a.rot[0][0]*b[0]+a.rot[1][0]*b[1]+a.rot[2][0]*b[2],a.rot[0][1]*b[0]+a.rot[1][1]*b[1]+a.rot[2][1]*b[2],a.rot[0][2]*b[0]+a.rot[1][2]*b[1]+a.rot[2][2]*b[2]]}function precession(a,b,c){b=precession_rot(b,c);return rotate(b,a)}
function precession_posvel(a,b,c){b=precession_rot(b,c);return RotateState(b,a)}
function precession_rot(a,b){a=a.tt/36525;var c=84381.406,d=((((3.337E-7*a-4.67E-7)*a-.00772503)*a+.0512623)*a-.025754)*a+c;c*=ASEC2RAD;var e=((((-9.51E-8*a+1.32851E-4)*a-.00114045)*a-1.0790069)*a+5038.481507)*a*ASEC2RAD;d*=ASEC2RAD;var f=((((-5.6E-8*a+1.70663E-4)*a-.00121197)*a-2.3814292)*a+10.556403)*a*ASEC2RAD;a=Math.sin(c);c=Math.cos(c);var g=Math.sin(-e);e=Math.cos(-e);var k=Math.sin(-d);d=Math.cos(-d);var h=Math.sin(f),l=Math.cos(f);f=l*e-g*h*d;var m=l*g*c+h*d*e*c-a*h*k,n=l*g*a+h*d*e*a+c*h*
k,p=-h*e-g*l*d,r=-h*g*c+l*d*e*c-a*l*k;h=-h*g*a+l*d*e*a+c*l*k;g*=k;l=-k*e*c-a*d;a=-k*e*a+d*c;if(b===PrecessDirection.Into2000)return new RotationMatrix([[f,m,n],[p,r,h],[g,l,a]]);if(b===PrecessDirection.From2000)return new RotationMatrix([[f,p,g],[m,r,l],[n,h,a]]);throw"Invalid precess direction";}function era(a){a=(.779057273264+.00273781191135448*a.ut+a.ut%1)%1*360;0>a&&(a+=360);return a}var sidereal_time_cache;
function sidereal_time(a){if(!sidereal_time_cache||sidereal_time_cache.tt!==a.tt){var b=a.tt/36525,c=15*e_tilt(a).ee,d=era(a);b=((c+.014506+((((-3.68E-8*b-2.9956E-5)*b-4.4E-7)*b+1.3915817)*b+4612.156534)*b)/3600+d)%360/15;0>b&&(b+=24);sidereal_time_cache={tt:a.tt,st:b}}return sidereal_time_cache.st}function SiderealTime(a){a=MakeTime(a);return sidereal_time(a)}exports.SiderealTime=SiderealTime;
function inverse_terra(a,b){var c=a[0]*exports.KM_PER_AU,d=a[1]*exports.KM_PER_AU;a=a[2]*exports.KM_PER_AU;var e=Math.hypot(c,d);if(1E-6>e){b=0;var f=0<a?90:-90;a=Math.abs(a)-EARTH_POLAR_RADIUS_KM}else{for(b=exports.RAD2DEG*Math.atan2(d,c)-15*b;-180>=b;)b+=360;for(;180<b;)b-=360;f=Math.atan2(a,e);for(var g,k=0;;){if(10<++k)throw"inverse_terra failed to converge.";c=Math.cos(f);d=Math.sin(f);var h=(EARTH_FLATTENING_SQUARED-1)*EARTH_EQUATORIAL_RADIUS_KM,l=c*c,m=d*d,n=l+EARTH_FLATTENING_SQUARED*m;g=
Math.sqrt(n);var p=h*d*c/g-a*c+e*d;if(1E-8>Math.abs(p))break;f-=p/(h*((l-m)/g-m*l*(EARTH_FLATTENING_SQUARED-1)/(h*n))+a*d+e*c)}f*=exports.RAD2DEG;g=EARTH_EQUATORIAL_RADIUS_KM/g;a=Math.abs(d)>Math.abs(c)?a/d-EARTH_FLATTENING_SQUARED*g:e/c-g}return new Observer(f,b,1E3*a)}
function terra(a,b){var c=a.latitude*exports.DEG2RAD,d=Math.sin(c);c=Math.cos(c);var e=1/Math.hypot(c,EARTH_FLATTENING*d),f=a.height/1E3,g=EARTH_EQUATORIAL_RADIUS_KM*e+f;b=(15*b+a.longitude)*exports.DEG2RAD;a=Math.sin(b);b=Math.cos(b);return{pos:[g*c*b/exports.KM_PER_AU,g*c*a/exports.KM_PER_AU,(EARTH_EQUATORIAL_RADIUS_KM*EARTH_FLATTENING_SQUARED*e+f)*d/exports.KM_PER_AU],vel:[-ANGVEL*g*c*a*86400/exports.KM_PER_AU,ANGVEL*g*c*b*86400/exports.KM_PER_AU,0]}}
function nutation(a,b,c){b=nutation_rot(b,c);return rotate(b,a)}function nutation_posvel(a,b,c){b=nutation_rot(b,c);return RotateState(b,a)}
function nutation_rot(a,b){a=e_tilt(a);var c=a.mobl*exports.DEG2RAD,d=a.tobl*exports.DEG2RAD,e=a.dpsi*ASEC2RAD;a=Math.cos(c);c=Math.sin(c);var f=Math.cos(d),g=Math.sin(d);d=Math.cos(e);var k=Math.sin(e);e=-k*a;var h=-k*c,l=k*f,m=d*a*f+c*g,n=d*c*f-a*g;k*=g;var p=d*a*g-c*f;a=d*c*g+a*f;if(b===PrecessDirection.From2000)return new RotationMatrix([[d,l,k],[e,m,p],[h,n,a]]);if(b===PrecessDirection.Into2000)return new RotationMatrix([[d,e,h],[l,m,n],[k,p,a]]);throw"Invalid precess direction";}
function gyration(a,b,c){return c===PrecessDirection.Into2000?precession(nutation(a,b,c),b,c):nutation(precession(a,b,c),b,c)}function gyration_posvel(a,b,c){return c===PrecessDirection.Into2000?precession_posvel(nutation_posvel(a,b,c),b,c):nutation_posvel(precession_posvel(a,b,c),b,c)}function geo_pos(a,b){var c=sidereal_time(a);b=terra(b,c).pos;return gyration(b,a,PrecessDirection.Into2000)}var Vector=function(a,b,c,d){this.x=a;this.y=b;this.z=c;this.t=d};
Vector.prototype.Length=function(){return Math.hypot(this.x,this.y,this.z)};exports.Vector=Vector;var StateVector=function(a,b,c,d,e,f,g){this.x=a;this.y=b;this.z=c;this.vx=d;this.vy=e;this.vz=f;this.t=g};exports.StateVector=StateVector;var Spherical=function(a,b,c){this.lat=VerifyNumber(a);this.lon=VerifyNumber(b);this.dist=VerifyNumber(c)};exports.Spherical=Spherical;var EquatorialCoordinates=function(a,b,c,d){this.ra=VerifyNumber(a);this.dec=VerifyNumber(b);this.dist=VerifyNumber(c);this.vec=d};
exports.EquatorialCoordinates=EquatorialCoordinates;function IsValidRotationArray(a){if(!(a instanceof Array)||3!==a.length)return!1;for(var b=0;3>b;++b){if(!(a[b]instanceof Array)||3!==a[b].length)return!1;for(var c=0;3>c;++c)if(!Number.isFinite(a[b][c]))return!1}return!0}var RotationMatrix=function(a){this.rot=a};exports.RotationMatrix=RotationMatrix;function MakeRotation(a){if(!IsValidRotationArray(a))throw"Argument must be a [3][3] array of numbers";return new RotationMatrix(a)}
exports.MakeRotation=MakeRotation;var HorizontalCoordinates=function(a,b,c,d){this.azimuth=VerifyNumber(a);this.altitude=VerifyNumber(b);this.ra=VerifyNumber(c);this.dec=VerifyNumber(d)};exports.HorizontalCoordinates=HorizontalCoordinates;var EclipticCoordinates=function(a,b,c){this.vec=a;this.elat=VerifyNumber(b);this.elon=VerifyNumber(c)};exports.EclipticCoordinates=EclipticCoordinates;function VectorFromArray(a,b){return new Vector(a[0],a[1],a[2],b)}
function vector2radec(a,b){b=VectorFromArray(a,b);var c=b.x*b.x+b.y*b.y,d=Math.sqrt(c+b.z*b.z);if(0===c){if(0===b.z)throw"Indeterminate sky coordinates";return new EquatorialCoordinates(0,0>b.z?-90:90,d,b)}var e=exports.RAD2HOUR*Math.atan2(b.y,b.x);0>e&&(e+=24);return new EquatorialCoordinates(e,exports.RAD2DEG*Math.atan2(a[2],Math.sqrt(c)),d,b)}function spin(a,b){var c=a*exports.DEG2RAD;a=Math.cos(c);c=Math.sin(c);return[a*b[0]+c*b[1],a*b[1]-c*b[0],b[2]]}
function Horizon(a,b,c,d,e){a=MakeTime(a);VerifyObserver(b);VerifyNumber(c);VerifyNumber(d);var f=Math.sin(b.latitude*exports.DEG2RAD),g=Math.cos(b.latitude*exports.DEG2RAD),k=Math.sin(b.longitude*exports.DEG2RAD),h=Math.cos(b.longitude*exports.DEG2RAD);b=Math.sin(d*exports.DEG2RAD);var l=Math.cos(d*exports.DEG2RAD),m=Math.sin(c*exports.HOUR2RAD),n=Math.cos(c*exports.HOUR2RAD),p=[g*h,g*k,f];f=[-f*h,-f*k,g];k=[k,-h,0];g=-15*sidereal_time(a);a=spin(g,p);p=spin(g,f);k=spin(g,k);b=[l*n,l*m,b];m=b[0]*
a[0]+b[1]*a[1]+b[2]*a[2];l=b[0]*p[0]+b[1]*p[1]+b[2]*p[2];p=b[0]*k[0]+b[1]*k[1]+b[2]*k[2];n=Math.hypot(l,p);0<n?(l=-exports.RAD2DEG*Math.atan2(p,l),0>l&&(l+=360)):l=0;m=exports.RAD2DEG*Math.atan2(n,m);n=d;if(e&&(d=m,e=Refraction(e,90-m),m-=e,0<e&&3E-4<m)){c=Math.sin(m*exports.DEG2RAD);n=Math.cos(m*exports.DEG2RAD);p=Math.sin(d*exports.DEG2RAD);d=Math.cos(d*exports.DEG2RAD);e=[];for(k=0;3>k;++k)e.push((b[k]-d*a[k])/p*c+a[k]*n);n=Math.hypot(e[0],e[1]);0<n?(c=exports.RAD2HOUR*Math.atan2(e[1],e[0]),0>
c&&(c+=24)):c=0;n=exports.RAD2DEG*Math.atan2(e[2],n)}return new HorizontalCoordinates(l,90-m,c,n)}exports.Horizon=Horizon;function VerifyObserver(a){if(!(a instanceof Observer))throw"Not an instance of the Observer class: "+a;VerifyNumber(a.latitude);VerifyNumber(a.longitude);VerifyNumber(a.height);if(-90>a.latitude||90<a.latitude)throw"Latitude "+a.latitude+" is out of range. Must be -90..+90.";return a}var Observer=function(a,b,c){this.latitude=a;this.longitude=b;this.height=c;VerifyObserver(this)};
exports.Observer=Observer;function SunPosition(a){a=MakeTime(a).AddDays(-1/exports.C_AUDAY);var b=CalcVsop(vsop.Earth,a),c=$jscomp.makeIterator(gyration([-b.x,-b.y,-b.z],a,PrecessDirection.From2000));b=c.next().value;var d=c.next().value,e=c.next().value,f=exports.DEG2RAD*e_tilt(a).tobl;c=Math.cos(f);f=Math.sin(f);a=new Vector(b,d,e,a);return RotateEquatorialToEcliptic(a,c,f)}exports.SunPosition=SunPosition;
function Equator(a,b,c,d,e){VerifyObserver(c);VerifyBoolean(d);VerifyBoolean(e);b=MakeTime(b);c=geo_pos(b,c);a=GeoVector(a,b,e);a=[a.x-c[0],a.y-c[1],a.z-c[2]];if(!d)return vector2radec(a,b);d=gyration(a,b,PrecessDirection.From2000);return vector2radec(d,b)}exports.Equator=Equator;function ObserverVector(a,b,c){a=MakeTime(a);var d=sidereal_time(a);b=terra(b,d).pos;c||(b=gyration(b,a,PrecessDirection.Into2000));return VectorFromArray(b,a)}exports.ObserverVector=ObserverVector;
function ObserverState(a,b,c){a=MakeTime(a);var d=sidereal_time(a);b=terra(b,d);b=new StateVector(b.pos[0],b.pos[1],b.pos[2],b.vel[0],b.vel[1],b.vel[2],a);return c?b:gyration_posvel(b,a,PrecessDirection.Into2000)}exports.ObserverState=ObserverState;function VectorObserver(a,b){var c=sidereal_time(a.t),d=[a.x,a.y,a.z];b||(d=precession(d,a.t,PrecessDirection.From2000),d=nutation(d,a.t,PrecessDirection.From2000));return inverse_terra(d,c)}exports.VectorObserver=VectorObserver;
function ObserverGravity(a,b){a=Math.sin(a*exports.DEG2RAD);a*=a;return 9.7803253359*(1+.00193185265241*a)/Math.sqrt(1-.00669437999013*a)*(1-(3.15704E-7-2.10269E-9*a)*b+7.37452E-14*b*b)}exports.ObserverGravity=ObserverGravity;function RotateEquatorialToEcliptic(a,b,c){var d=a.x,e=a.y*b+a.z*c;c=-a.y*c+a.z*b;var f=Math.hypot(d,e);b=0;0<f&&(b=exports.RAD2DEG*Math.atan2(e,d),0>b&&(b+=360));f=exports.RAD2DEG*Math.atan2(c,f);a=new Vector(d,e,c,a.t);return new EclipticCoordinates(a,f,b)}
function Ecliptic(a){var b=e_tilt(a.t),c=precession([a.x,a.y,a.z],a.t,PrecessDirection.From2000),d=$jscomp.makeIterator(nutation(c,a.t,PrecessDirection.From2000));c=d.next().value;var e=d.next().value;d=d.next().value;a=new Vector(c,e,d,a.t);b=b.tobl*exports.DEG2RAD;return RotateEquatorialToEcliptic(a,Math.cos(b),Math.sin(b))}exports.Ecliptic=Ecliptic;
function GeoMoon(a){a=MakeTime(a);var b=CalcMoon(a),c=b.distance_au*Math.cos(b.geo_eclip_lat);b=ecl2equ_vec(a,[c*Math.cos(b.geo_eclip_lon),c*Math.sin(b.geo_eclip_lon),b.distance_au*Math.sin(b.geo_eclip_lat)]);b=precession(b,a,PrecessDirection.Into2000);return new Vector(b[0],b[1],b[2],a)}exports.GeoMoon=GeoMoon;
function EclipticGeoMoon(a){var b=MakeTime(a);a=CalcMoon(b);var c=a.distance_au*Math.cos(a.geo_eclip_lat),d=[c*Math.cos(a.geo_eclip_lon),c*Math.sin(a.geo_eclip_lon),a.distance_au*Math.sin(a.geo_eclip_lat)];c=e_tilt(b);d=obl_ecl2equ_vec(c.mobl,d);d=nutation(d,b,PrecessDirection.From2000);b=VectorFromArray(d,b);c=c.tobl*exports.DEG2RAD;b=RotateEquatorialToEcliptic(b,Math.cos(c),Math.sin(c));return new Spherical(b.elat,b.elon,a.distance_au)}exports.EclipticGeoMoon=EclipticGeoMoon;
function GeoMoonState(a){a=MakeTime(a);var b=a.AddDays(-1E-5),c=a.AddDays(1E-5);b=GeoMoon(b);c=GeoMoon(c);return new StateVector((b.x+c.x)/2,(b.y+c.y)/2,(b.z+c.z)/2,(c.x-b.x)/2E-5,(c.y-b.y)/2E-5,(c.z-b.z)/2E-5,a)}exports.GeoMoonState=GeoMoonState;function GeoEmbState(a){a=MakeTime(a);var b=GeoMoonState(a),c=1+EARTH_MOON_MASS_RATIO;return new StateVector(b.x/c,b.y/c,b.z/c,b.vx/c,b.vy/c,b.vz/c,a)}exports.GeoEmbState=GeoEmbState;
function VsopFormula(a,b,c){var d=1,e=0;a=$jscomp.makeIterator(a);for(var f=a.next();!f.done;f=a.next()){var g=0;f=$jscomp.makeIterator(f.value);for(var k=f.next();!k.done;k=f.next()){var h=$jscomp.makeIterator(k.value);k=h.next().value;var l=h.next().value;h=h.next().value;g+=k*Math.cos(l+b*h)}g*=d;c&&(g%=PI2);e+=g;d*=b}return e}
function VsopDeriv(a,b){var c=1,d=0,e=0,f=0;a=$jscomp.makeIterator(a);for(var g=a.next();!g.done;g=a.next()){var k=0,h=0;g=$jscomp.makeIterator(g.value);for(var l=g.next();!l.done;l=g.next()){var m=$jscomp.makeIterator(l.value);l=m.next().value;var n=m.next().value;m=m.next().value;n+=b*m;k+=l*m*Math.sin(n);0<f&&(h+=l*Math.cos(n))}e+=f*d*h-c*k;d=c;c*=b;++f}return e}var DAYS_PER_MILLENNIUM=365250,LON_INDEX=0,LAT_INDEX=1,RAD_INDEX=2;
function VsopRotate(a){return new TerseVector(a[0]+4.4036E-7*a[1]-1.90919E-7*a[2],-4.79966E-7*a[0]+.917482137087*a[1]-.397776982902*a[2],.397776982902*a[1]+.917482137087*a[2])}function VsopSphereToRect(a,b,c){var d=c*Math.cos(b);return[d*Math.cos(a),d*Math.sin(a),c*Math.sin(b)]}function CalcVsop(a,b){var c=b.tt/DAYS_PER_MILLENNIUM,d=VsopFormula(a[LON_INDEX],c,!0),e=VsopFormula(a[LAT_INDEX],c,!1);a=VsopFormula(a[RAD_INDEX],c,!1);d=VsopSphereToRect(d,e,a);return VsopRotate(d).ToAstroVector(b)}
function CalcVsopPosVel(a,b){var c=b/DAYS_PER_MILLENNIUM,d=VsopFormula(a[LON_INDEX],c,!0),e=VsopFormula(a[LAT_INDEX],c,!1),f=VsopFormula(a[RAD_INDEX],c,!1),g=VsopDeriv(a[LON_INDEX],c),k=VsopDeriv(a[LAT_INDEX],c);c=VsopDeriv(a[RAD_INDEX],c);var h=Math.cos(d),l=Math.sin(d),m=Math.cos(e),n=Math.sin(e);a=+(c*m*h)-f*n*h*k-f*m*l*g;g=+(c*m*l)-f*n*l*k+f*m*h*g;k=+(c*n)+f*m*k;d=VsopSphereToRect(d,e,f);e=[a/DAYS_PER_MILLENNIUM,g/DAYS_PER_MILLENNIUM,k/DAYS_PER_MILLENNIUM];d=VsopRotate(d);e=VsopRotate(e);return new body_state_t(b,
d,e)}function AdjustBarycenter(a,b,c,d){d/=d+SUN_GM;b=CalcVsop(vsop[c],b);a.x+=d*b.x;a.y+=d*b.y;a.z+=d*b.z}function CalcSolarSystemBarycenter(a){var b=new Vector(0,0,0,a);AdjustBarycenter(b,a,Body.Jupiter,JUPITER_GM);AdjustBarycenter(b,a,Body.Saturn,SATURN_GM);AdjustBarycenter(b,a,Body.Uranus,URANUS_GM);AdjustBarycenter(b,a,Body.Neptune,NEPTUNE_GM);return b}
var PLUTO_NUM_STATES=51,PLUTO_TIME_STEP=29200,PLUTO_DT=146,PLUTO_NSTEPS=201,PlutoStateTable=[[-73E4,[-26.118207232108,-14.376168177825,3.384402515299],[.0016339372163656,-.0027861699588508,-.0013585880229445]],[-700800,[41.974905202127,-.448502952929,-12.770351505989],[7.3458569351457E-4,.0022785014891658,4.8619778602049E-4]],[-671600,[14.706930780744,44.269110540027,9.353698474772],[-.00210001479998,2.2295915939915E-4,7.0143443551414E-4]],[-642400,[-29.441003929957,-6.43016153057,6.858481011305],
[8.4495803960544E-4,-.0030783914758711,-.0012106305981192]],[-613200,[39.444396946234,-6.557989760571,-13.913760296463],[.0011480029005873,.0022400006880665,3.5168075922288E-4]],[-584E3,[20.2303809507,43.266966657189,7.382966091923],[-.0019754081700585,5.3457141292226E-4,7.5929169129793E-4]],[-554800,[-30.65832536462,2.093818874552,9.880531138071],[6.1010603013347E-5,-.0031326500935382,-9.9346125151067E-4]],[-525600,[35.737703251673,-12.587706024764,-14.677847247563],[.0015802939375649,.0021347678412429,
1.9074436384343E-4]],[-496400,[25.466295188546,41.367478338417,5.216476873382],[-.0018054401046468,8.328308359951E-4,8.0260156912107E-4]],[-467200,[-29.847174904071,10.636426313081,12.297904180106],[-6.3257063052907E-4,-.0029969577578221,-7.4476074151596E-4]],[-438E3,[30.774692107687,-18.236637015304,-14.945535879896],[.0020113162005465,.0019353827024189,-2.0937793168297E-6]],[-408800,[30.243153324028,38.656267888503,2.938501750218],[-.0016052508674468,.0011183495337525,8.3333973416824E-4]],[-379600,
[-27.288984772533,18.643162147874,14.023633623329],[-.0011856388898191,-.0027170609282181,-4.9015526126399E-4]],[-350400,[24.519605196774,-23.245756064727,-14.626862367368],[.0024322321483154,.0016062008146048,-2.3369181613312E-4]],[-321200,[34.505274805875,35.125338586954,.557361475637],[-.0013824391637782,.0013833397561817,8.4823598806262E-4]],[-292E3,[-23.275363915119,25.818514298769,15.055381588598],[-.0016062295460975,-.0023395961498533,-2.4377362639479E-4]],[-262800,[17.050384798092,-27.180376290126,
-13.608963321694],[.0028175521080578,.0011358749093955,-4.9548725258825E-4]],[-233600,[38.093671910285,30.880588383337,-1.843688067413],[-.0011317697153459,.0016128814698472,8.4177586176055E-4]],[-204400,[-18.197852930878,31.932869934309,15.438294826279],[-.0019117272501813,-.0019146495909842,-1.9657304369835E-5]],[-175200,[8.528924039997,-29.618422200048,-11.805400994258],[.0031034370787005,5.139363329243E-4,-7.7293066202546E-4]],[-146E3,[40.94685725864,25.904973592021,-4.256336240499],[-8.3652705194051E-4,
.0018129497136404,8.156422827306E-4]],[-116800,[-12.326958895325,36.881883446292,15.217158258711],[-.0021166103705038,-.001481442003599,1.7401209844705E-4]],[-87600,[-.633258375909,-30.018759794709,-9.17193287495],[.0032016994581737,-2.5279858672148E-4,-.0010411088271861]],[-58400,[42.936048423883,20.344685584452,-6.588027007912],[-5.0525450073192E-4,.0019910074335507,7.7440196540269E-4]],[-29200,[-5.975910552974,40.61180995846,14.470131723673],[-.0022184202156107,-.0010562361130164,3.3652250216211E-4]],
[0,[-9.875369580774,-27.978926224737,-5.753711824704],[.0030287533248818,-.0011276087003636,-.0012651326732361]],[29200,[43.958831986165,14.214147973292,-8.808306227163],[-1.4717608981871E-4,.0021404187242141,7.1486567806614E-4]],[58400,[.67813676352,43.094461639362,13.243238780721],[-.0022358226110718,-6.3233636090933E-4,4.7664798895648E-4]],[87600,[-18.282602096834,-23.30503958666,-1.766620508028],[.0025567245263557,-.0019902940754171,-.0013943491701082]],[116800,[43.873338744526,7.700705617215,
-10.814273666425],[2.3174803055677E-4,.0022402163127924,6.2988756452032E-4]],[146E3,[7.392949027906,44.382678951534,11.629500214854],[-.002193281545383,-2.1751799585364E-4,5.9556516201114E-4]],[175200,[-24.981690229261,-16.204012851426,2.466457544298],[.001819398914958,-.0026765419531201,-.0013848283502247]],[204400,[42.530187039511,.845935508021,-12.554907527683],[6.5059779150669E-4,.0022725657282262,5.1133743202822E-4]],[233600,[13.999526486822,44.462363044894,9.669418486465],[-.0021079296569252,
1.7533423831993E-4,6.9128485798076E-4]],[262800,[-29.184024803031,-7.371243995762,6.493275957928],[9.3581363109681E-4,-.0030610357109184,-.0012364201089345]],[292E3,[39.831980671753,-6.078405766765,-13.909815358656],[.0011117769689167,.0022362097830152,3.6230548231153E-4]],[321200,[20.294955108476,43.417190420251,7.450091985932],[-.0019742157451535,5.3102050468554E-4,7.5938408813008E-4]],[350400,[-30.66999230216,2.318743558955,9.973480913858],[4.5605107450676E-5,-.0031308219926928,-9.9066533301924E-4]],
[379600,[35.626122155983,-12.897647509224,-14.777586508444],[.0016015684949743,.0021171931182284,1.8002516202204E-4]],[408800,[26.133186148561,41.232139187599,5.00640132622],[-.0017857704419579,8.6046232702817E-4,8.0614690298954E-4]],[438E3,[-29.57674022923,11.863535943587,12.631323039872],[-7.2292830060955E-4,-.0029587820140709,-7.08242964503E-4]],[467200,[29.910805787391,-19.159019294,-15.013363865194],[.0020871080437997,.0018848372554514,-3.8528655083926E-5]],[496400,[31.375957451819,38.050372720763,
2.433138343754],[-.0015546055556611,.0011699815465629,8.3565439266001E-4]],[525600,[-26.360071336928,20.662505904952,14.414696258958],[-.0013142373118349,-.0026236647854842,-4.2542017598193E-4]],[554800,[22.599441488648,-24.508879898306,-14.484045731468],[.0025454108304806,.0014917058755191,-3.0243665086079E-4]],[584E3,[35.877864013014,33.894226366071,-.224524636277],[-.0012941245730845,.0014560427668319,8.4762160640137E-4]],[613200,[-21.538149762417,28.204068269761,15.321973799534],[-.001731211740901,
-.0021939631314577,-1.631691327518E-4]],[642400,[13.971521374415,-28.339941764789,-13.083792871886],[.0029334630526035,9.1860931752944E-4,-5.9939422488627E-4]],[671600,[39.526942044143,28.93989736011,-2.872799527539],[-.0010068481658095,.001702113288809,8.3578230511981E-4]],[700800,[-15.576200701394,34.399412961275,15.466033737854],[-.0020098814612884,-.0017191109825989,7.0414782780416E-5]],[73E4,[4.24325283709,-30.118201690825,-10.707441231349],[.0031725847067411,1.609846120227E-4,-9.0672150593868E-4]]],
TerseVector=function(a,b,c){this.x=a;this.y=b;this.z=c};TerseVector.prototype.clone=function(){return new TerseVector(this.x,this.y,this.z)};TerseVector.prototype.ToAstroVector=function(a){return new Vector(this.x,this.y,this.z,a)};TerseVector.zero=function(){return new TerseVector(0,0,0)};TerseVector.prototype.quadrature=function(){return this.x*this.x+this.y*this.y+this.z*this.z};TerseVector.prototype.add=function(a){return new TerseVector(this.x+a.x,this.y+a.y,this.z+a.z)};
TerseVector.prototype.sub=function(a){return new TerseVector(this.x-a.x,this.y-a.y,this.z-a.z)};TerseVector.prototype.incr=function(a){this.x+=a.x;this.y+=a.y;this.z+=a.z};TerseVector.prototype.decr=function(a){this.x-=a.x;this.y-=a.y;this.z-=a.z};TerseVector.prototype.mul=function(a){return new TerseVector(a*this.x,a*this.y,a*this.z)};TerseVector.prototype.div=function(a){return new TerseVector(this.x/a,this.y/a,this.z/a)};
TerseVector.prototype.mean=function(a){return new TerseVector((this.x+a.x)/2,(this.y+a.y)/2,(this.z+a.z)/2)};TerseVector.prototype.neg=function(){return new TerseVector(-this.x,-this.y,-this.z)};var body_state_t=function(a,b,c){this.tt=a;this.r=b;this.v=c};body_state_t.prototype.clone=function(){return new body_state_t(this.tt,this.r,this.v)};body_state_t.prototype.sub=function(a){return new body_state_t(this.tt,this.r.sub(a.r),this.v.sub(a.v))};
function BodyStateFromTable(a){var b=$jscomp.makeIterator(a);a=b.next().value;var c=$jscomp.makeIterator(b.next().value),d=c.next().value,e=c.next().value;c=c.next().value;var f=$jscomp.makeIterator(b.next().value);b=f.next().value;var g=f.next().value;f=f.next().value;return new body_state_t(a,new TerseVector(d,e,c),new TerseVector(b,g,f))}function AdjustBarycenterPosVel(a,b,c,d){d/=d+SUN_GM;b=CalcVsopPosVel(vsop[c],b);a.r.incr(b.r.mul(d));a.v.incr(b.v.mul(d));return b}
function AccelerationIncrement(a,b,c){a=c.sub(a);c=a.quadrature();return a.mul(b/(c*Math.sqrt(c)))}
var major_bodies_t=function(a){var b=new body_state_t(a,new TerseVector(0,0,0),new TerseVector(0,0,0));this.Jupiter=AdjustBarycenterPosVel(b,a,Body.Jupiter,JUPITER_GM);this.Saturn=AdjustBarycenterPosVel(b,a,Body.Saturn,SATURN_GM);this.Uranus=AdjustBarycenterPosVel(b,a,Body.Uranus,URANUS_GM);this.Neptune=AdjustBarycenterPosVel(b,a,Body.Neptune,NEPTUNE_GM);this.Jupiter.r.decr(b.r);this.Jupiter.v.decr(b.v);this.Saturn.r.decr(b.r);this.Saturn.v.decr(b.v);this.Uranus.r.decr(b.r);this.Uranus.v.decr(b.v);
this.Neptune.r.decr(b.r);this.Neptune.v.decr(b.v);this.Sun=new body_state_t(a,b.r.mul(-1),b.v.mul(-1))};major_bodies_t.prototype.Acceleration=function(a){var b=AccelerationIncrement(a,SUN_GM,this.Sun.r);b.incr(AccelerationIncrement(a,JUPITER_GM,this.Jupiter.r));b.incr(AccelerationIncrement(a,SATURN_GM,this.Saturn.r));b.incr(AccelerationIncrement(a,URANUS_GM,this.Uranus.r));b.incr(AccelerationIncrement(a,NEPTUNE_GM,this.Neptune.r));return b};
var body_grav_calc_t=function(a,b,c,d){this.tt=a;this.r=b;this.v=c;this.a=d};body_grav_calc_t.prototype.clone=function(){return new body_grav_calc_t(this.tt,this.r.clone(),this.v.clone(),this.a.clone())};var grav_sim_t=function(a,b){this.bary=a;this.grav=b};function UpdatePosition(a,b,c,d){return new TerseVector(b.x+a*(c.x+a*d.x/2),b.y+a*(c.y+a*d.y/2),b.z+a*(c.z+a*d.z/2))}function UpdateVelocity(a,b,c){return new TerseVector(b.x+a*c.x,b.y+a*c.y,b.z+a*c.z)}
function GravSim(a,b){var c=a-b.tt,d=new major_bodies_t(a),e=UpdatePosition(c,b.r,b.v,b.a),f=d.Acceleration(e).mean(b.a);e=UpdatePosition(c,b.r,b.v,f);b=b.v.add(f.mul(c));c=d.Acceleration(e);a=new body_grav_calc_t(a,e,b,c);return new grav_sim_t(d,a)}var pluto_cache=[];function ClampIndex(a,b){a=Math.floor(a);return 0>a?0:a>=b?b-1:a}
function GravFromState(a){var b=BodyStateFromTable(a);a=new major_bodies_t(b.tt);var c=b.r.add(a.Sun.r),d=b.v.add(a.Sun.v),e=a.Acceleration(c);b=new body_grav_calc_t(b.tt,c,d,e);return new grav_sim_t(a,b)}
function GetSegment(a,b){var c=PlutoStateTable[0][0];if(b<c||b>PlutoStateTable[PLUTO_NUM_STATES-1][0])return null;b=ClampIndex((b-c)/PLUTO_TIME_STEP,PLUTO_NUM_STATES-1);if(!a[b]){c=a[b]=[];c[0]=GravFromState(PlutoStateTable[b]).grav;c[PLUTO_NSTEPS-1]=GravFromState(PlutoStateTable[b+1]).grav;var d,e=c[0].tt;for(d=1;d<PLUTO_NSTEPS-1;++d)c[d]=GravSim(e+=PLUTO_DT,c[d-1]).grav;e=c[PLUTO_NSTEPS-1].tt;var f=[];f[PLUTO_NSTEPS-1]=c[PLUTO_NSTEPS-1];for(d=PLUTO_NSTEPS-2;0<d;--d)f[d]=GravSim(e-=PLUTO_DT,f[d+
1]).grav;for(d=PLUTO_NSTEPS-2;0<d;--d)e=d/(PLUTO_NSTEPS-1),c[d].r=c[d].r.mul(1-e).add(f[d].r.mul(e)),c[d].v=c[d].v.mul(1-e).add(f[d].v.mul(e)),c[d].a=c[d].a.mul(1-e).add(f[d].a.mul(e))}return a[b]}function CalcPlutoOneWay(a,b,c){a=GravFromState(a);for(var d=Math.ceil((b-a.grav.tt)/c),e=0;e<d;++e)a=GravSim(e+1===d?b:a.grav.tt+c,a.grav);return a}
function CalcPluto(a,b){var c;if(c=GetSegment(pluto_cache,a.tt)){var d=ClampIndex((a.tt-c[0].tt)/PLUTO_DT,PLUTO_NSTEPS-1);var e=c[d];var f=c[d+1],g=e.a.mean(f.a);d=UpdatePosition(a.tt-e.tt,e.r,e.v,g);c=UpdateVelocity(a.tt-e.tt,e.v,g);var k=UpdatePosition(a.tt-f.tt,f.r,f.v,g);f=UpdateVelocity(a.tt-f.tt,f.v,g);g=(a.tt-e.tt)/PLUTO_DT;e=d.mul(1-g).add(k.mul(g));c=c.mul(1-g).add(f.mul(g))}else{var h=a.tt<PlutoStateTable[0][0]?CalcPlutoOneWay(PlutoStateTable[0],a.tt,-PLUTO_DT):CalcPlutoOneWay(PlutoStateTable[PLUTO_NUM_STATES-
1],a.tt,+PLUTO_DT);e=h.grav.r;c=h.grav.v;h=h.bary}b&&(h||(h=new major_bodies_t(a.tt)),e=e.sub(h.Sun.r),c=c.sub(h.Sun.v));return new StateVector(e.x,e.y,e.z,c.x,c.y,c.z,a)}
var Rotation_JUP_EQJ=new RotationMatrix([[.999432765338654,-.0336771074697641,0],[.0303959428906285,.902057912352809,.430543388542295],[-.0144994559663353,-.430299169409101,.902569881273754]]),JupiterMoonModel=[{mu:2.82489428433814E-7,al:[1.446213296021224,3.5515522861824],a:[[.0028210960212903,0,0]],l:[[-1.925258348666E-4,4.9369589722645,.01358483658305],[-9.70803596076E-5,4.3188796477322,.01303413843243],[-8.988174165E-5,1.9080016428617,.00305064867158],[-5.53101050262E-5,1.4936156681569,.01293892891155]],
z:[[.0041510849668155,4.089939635545,-.01290686414666],[6.260521444113E-4,1.446188898627,3.5515522949802],[3.52747346169E-5,2.1256287034578,1.2727416567E-4]],zeta:[[3.142172466014E-4,2.7964219722923,-.002315096098],[9.04169207946E-5,1.0477061879627,-5.6920638196E-4]]},{mu:2.82483274392893E-7,al:[-.3735263437471362,1.76932271112347],a:[[.0044871037804314,0,0],[4.324367498E-7,1.819645606291,1.7822295777568]],l:[[8.576433172936E-4,4.3188693178264,.01303413830805],[4.549582875086E-4,1.4936531751079,.01293892881962],
[3.248939825174E-4,1.8196494533458,1.7822295777568],[-3.074250079334E-4,4.9377037005911,.01358483286724],[1.982386144784E-4,1.907986905476,.00305101212869],[1.834063551804E-4,2.1402853388529,.00145009789338],[-1.434383188452E-4,5.622214036663,.89111478887838],[-7.71939140944E-5,4.300272437235,2.6733443704266]],z:[[-.0093589104136341,4.0899396509039,-.01290686414666],[2.988994545555E-4,5.9097265185595,1.7693227079462],[2.13903639035E-4,2.1256289300016,1.2727418407E-4],[1.980963564781E-4,2.743516829265,
6.7797343009E-4],[1.210388158965E-4,5.5839943711203,3.20566149E-5],[8.37042048393E-5,1.6094538368039,-.90402165808846],[8.23525166369E-5,1.4461887708689,3.5515522949802]],zeta:[[.0040404917832303,1.0477063169425,-5.692064054E-4],[2.200421034564E-4,3.3368857864364,-1.2491307307E-4],[1.662544744719E-4,2.4134862374711,0],[5.90282470983E-5,5.9719930968366,-3.056160225E-5]]},{mu:2.82498184184723E-7,al:[.2874089391143348,.878207923589328],a:[[.0071566594572575,0,0],[1.393029911E-6,1.1586745884981,2.6733443704266]],
l:[[2.310797886226E-4,2.1402987195942,.00145009784384],[-1.828635964118E-4,4.3188672736968,.01303413828263],[1.512378778204E-4,4.9373102372298,.01358483481252],[-1.163720969778E-4,4.300265986149,2.6733443704266],[-9.55478069846E-5,1.4936612842567,.01293892879857],[8.15246854464E-5,5.6222137132535,.89111478887838],[-8.01219679602E-5,1.2995922951532,1.0034433456729],[-6.07017260182E-5,.64978769669238,.50172167043264]],z:[[.0014289811307319,2.1256295942739,1.2727413029E-4],[7.71093122676E-4,5.5836330003496,
3.20643411E-5],[5.925911780766E-4,4.0899396636448,-.01290686414666],[2.045597496146E-4,5.2713683670372,-.12523544076106],[1.785118648258E-4,.28743156721063,.8782079244252],[1.131999784893E-4,1.4462127277818,3.5515522949802],[-6.5877816921E-5,2.2702423990985,-1.7951364394537],[4.97058888328E-5,5.9096792204858,1.7693227129285]],zeta:[[.0015932721570848,3.3368862796665,-1.2491307058E-4],[8.533093128905E-4,2.4133881688166,0],[3.513347911037E-4,5.9720789850127,-3.056101771E-5],[-1.441929255483E-4,1.0477061764435,
-5.6920632124E-4]]},{mu:2.82492144889909E-7,al:[-.3620341291375704,.376486233433828],a:[[.0125879701715314,0,0],[3.595204947E-6,.64965776007116,.50172168165034],[2.7580210652E-6,1.808423578151,3.1750660413359]],l:[[5.586040123824E-4,2.1404207189815,.00145009793231],[-3.805813868176E-4,2.7358844897853,2.972965062E-5],[2.205152863262E-4,.649796525964,.5017216724358],[1.877895151158E-4,1.8084787604005,3.1750660413359],[7.66916975242E-5,6.2720114319755,1.3928364636651],[7.47056855106E-5,1.2995916202344,
1.0034433456729]],z:[[.0073755808467977,5.5836071576084,3.206509914E-5],[2.065924169942E-4,5.9209831565786,.37648624194703],[1.589869764021E-4,.28744006242623,.8782079244252],[-1.561131605348E-4,2.1257397865089,1.2727441285E-4],[1.486043380971E-4,1.4462134301023,3.5515522949802],[6.35073108731E-5,5.9096803285954,1.7693227129285],[5.99351698525E-5,4.1125517584798,-2.7985797954589],[5.40660842731E-5,5.5390350845569,.00286834082283],[-4.89596900866E-5,4.6218149483338,-.62695712529519]],zeta:[[.0038422977898495,
2.4133922085557,0],[.0022453891791894,5.9721736773277,-3.056125525E-5],[-2.604479450559E-4,3.3368746306409,-1.2491309972E-4],[3.3211214323E-5,5.5604137742337,.00290037688507]]}],JupiterMoonsInfo=function(a,b,c,d){this.io=a;this.europa=b;this.ganymede=c;this.callisto=d};exports.JupiterMoonsInfo=JupiterMoonsInfo;
function JupiterMoon_elem2pv(a,b,c){var d=c[0],e=c[1],f=c[2],g=c[3],k=c[4];c=c[5];var h=Math.sqrt(b/(d*d*d));b=e+f*Math.sin(e)-g*Math.cos(e);do{var l=Math.cos(b);var m=Math.sin(b);l=(e-b+f*m-g*l)/(1-f*l-g*m);b+=l}while(1E-12<=Math.abs(l));l=Math.cos(b);m=Math.sin(b);b=g*l-f*m;var n=-f*l-g*m,p=1/(1+n),r=1/(1+Math.sqrt(1-f*f-g*g));e=d*(l-f-r*g*b);b=d*(m-g+r*f*b);g=h*p*d*(-m-r*g*n);d=h*p*d*(+l+r*f*n);f=2*Math.sqrt(1-k*k-c*c);h=1-2*c*c;l=1-2*k*k;m=2*c*k;return new StateVector(e*h+b*m,e*m+b*l,(k*b-e*c)*
f,g*h+d*m,g*m+d*l,(k*d-g*c)*f,a)}
function CalcJupiterMoon(a,b){for(var c=a.tt+18262.5,d=[0,b.al[0]+c*b.al[1],0,0,0,0],e=$jscomp.makeIterator(b.a),f=e.next();!f.done;f=e.next()){var g=$jscomp.makeIterator(f.value);f=g.next().value;var k=g.next().value;g=g.next().value;d[0]+=f*Math.cos(k+c*g)}e=$jscomp.makeIterator(b.l);for(f=e.next();!f.done;f=e.next())g=$jscomp.makeIterator(f.value),f=g.next().value,k=g.next().value,g=g.next().value,d[1]+=f*Math.sin(k+c*g);d[1]%=PI2;0>d[1]&&(d[1]+=PI2);e=$jscomp.makeIterator(b.z);for(f=e.next();!f.done;f=
e.next())g=$jscomp.makeIterator(f.value),f=g.next().value,k=g.next().value,g=g.next().value,k+=c*g,d[2]+=f*Math.cos(k),d[3]+=f*Math.sin(k);e=$jscomp.makeIterator(b.zeta);for(f=e.next();!f.done;f=e.next())g=$jscomp.makeIterator(f.value),f=g.next().value,k=g.next().value,g=g.next().value,k+=c*g,d[4]+=f*Math.cos(k),d[5]+=f*Math.sin(k);a=JupiterMoon_elem2pv(a,b.mu,d);return RotateState(Rotation_JUP_EQJ,a)}
function JupiterMoons(a){a=new AstroTime(a);return new JupiterMoonsInfo(CalcJupiterMoon(a,JupiterMoonModel[0]),CalcJupiterMoon(a,JupiterMoonModel[1]),CalcJupiterMoon(a,JupiterMoonModel[2]),CalcJupiterMoon(a,JupiterMoonModel[3]))}exports.JupiterMoons=JupiterMoons;
function HelioVector(a,b){b=MakeTime(b);if(a in vsop)return CalcVsop(vsop[a],b);if(a===Body.Pluto)return a=CalcPluto(b,!0),new Vector(a.x,a.y,a.z,b);if(a===Body.Sun)return new Vector(0,0,0,b);if(a===Body.Moon){a=CalcVsop(vsop.Earth,b);var c=GeoMoon(b);return new Vector(a.x+c.x,a.y+c.y,a.z+c.z,b)}if(a===Body.EMB){a=CalcVsop(vsop.Earth,b);c=GeoMoon(b);var d=1+EARTH_MOON_MASS_RATIO;return new Vector(a.x+c.x/d,a.y+c.y/d,a.z+c.z/d,b)}if(a===Body.SSB)return CalcSolarSystemBarycenter(b);if(c=UserDefinedStar(a))return a=
new Spherical(c.dec,15*c.ra,c.dist),VectorFromSphere(a,b);throw'HelioVector: Unknown body "'+a+'"';}exports.HelioVector=HelioVector;function HelioDistance(a,b){var c=UserDefinedStar(a);if(c)return c.dist;b=MakeTime(b);return a in vsop?VsopFormula(vsop[a][RAD_INDEX],b.tt/DAYS_PER_MILLENNIUM,!1):HelioVector(a,b).Length()}exports.HelioDistance=HelioDistance;
function CorrectLightTravel(a,b){for(var c=b,d=0,e=0;10>e;++e){var f=a(c);d=f.Length()/exports.C_AUDAY;if(1<d)throw"Object is too distant for light-travel solver.";var g=b.AddDays(-d);d=Math.abs(g.tt-c.tt);if(1E-9>d)return f;c=g}throw"Light-travel time solver did not converge: dt = "+d;}exports.CorrectLightTravel=CorrectLightTravel;var BodyPosition=function(a,b,c,d){this.observerBody=a;this.targetBody=b;this.aberration=c;this.observerPos=d};
BodyPosition.prototype.Position=function(a){this.aberration&&(this.observerPos=HelioVector(this.observerBody,a));var b=HelioVector(this.targetBody,a);return new Vector(b.x-this.observerPos.x,b.y-this.observerPos.y,b.z-this.observerPos.z,a)};
function BackdatePosition(a,b,c,d){VerifyBoolean(d);a=MakeTime(a);if(UserDefinedStar(c)){c=HelioVector(c,a);if(d)return b=HelioState(b,a),d=new Vector(c.x-b.x,c.y-b.y,c.z-b.z,a),c=exports.C_AUDAY/d.Length(),new Vector(d.x+b.vx/c,d.y+b.vy/c,d.z+b.vz/c,a);b=HelioVector(b,a);return new Vector(c.x-b.x,c.y-b.y,c.z-b.z,a)}var e=d?new Vector(0,0,0,a):HelioVector(b,a);var f=new BodyPosition(b,c,d,e);return CorrectLightTravel(function(g){return f.Position(g)},a)}exports.BackdatePosition=BackdatePosition;
function GeoVector(a,b,c){VerifyBoolean(c);b=MakeTime(b);switch(a){case Body.Earth:return new Vector(0,0,0,b);case Body.Moon:return GeoMoon(b);default:return a=BackdatePosition(b,Body.Earth,a,c),a.t=b,a}}exports.GeoVector=GeoVector;function ExportState(a,b){return new StateVector(a.r.x,a.r.y,a.r.z,a.v.x,a.v.y,a.v.z,b)}
function BaryState(a,b){b=MakeTime(b);if(a===Body.SSB)return new StateVector(0,0,0,0,0,0,b);if(a===Body.Pluto)return CalcPluto(b,!1);var c=new major_bodies_t(b.tt);switch(a){case Body.Sun:return ExportState(c.Sun,b);case Body.Jupiter:return ExportState(c.Jupiter,b);case Body.Saturn:return ExportState(c.Saturn,b);case Body.Uranus:return ExportState(c.Uranus,b);case Body.Neptune:return ExportState(c.Neptune,b);case Body.Moon:case Body.EMB:var d=CalcVsopPosVel(vsop[Body.Earth],b.tt);a=a===Body.Moon?
GeoMoonState(b):GeoEmbState(b);return new StateVector(a.x+c.Sun.r.x+d.r.x,a.y+c.Sun.r.y+d.r.y,a.z+c.Sun.r.z+d.r.z,a.vx+c.Sun.v.x+d.v.x,a.vy+c.Sun.v.y+d.v.y,a.vz+c.Sun.v.z+d.v.z,b)}if(a in vsop)return a=CalcVsopPosVel(vsop[a],b.tt),new StateVector(c.Sun.r.x+a.r.x,c.Sun.r.y+a.r.y,c.Sun.r.z+a.r.z,c.Sun.v.x+a.v.x,c.Sun.v.y+a.v.y,c.Sun.v.z+a.v.z,b);throw'BaryState: Unsupported body "'+a+'"';}exports.BaryState=BaryState;
function HelioState(a,b){b=MakeTime(b);switch(a){case Body.Sun:return new StateVector(0,0,0,0,0,0,b);case Body.SSB:return a=new major_bodies_t(b.tt),new StateVector(-a.Sun.r.x,-a.Sun.r.y,-a.Sun.r.z,-a.Sun.v.x,-a.Sun.v.y,-a.Sun.v.z,b);case Body.Mercury:case Body.Venus:case Body.Earth:case Body.Mars:case Body.Jupiter:case Body.Saturn:case Body.Uranus:case Body.Neptune:return a=CalcVsopPosVel(vsop[a],b.tt),ExportState(a,b);case Body.Pluto:return CalcPluto(b,!0);case Body.Moon:case Body.EMB:var c=CalcVsopPosVel(vsop.Earth,
b.tt);a=a==Body.Moon?GeoMoonState(b):GeoEmbState(b);return new StateVector(a.x+c.r.x,a.y+c.r.y,a.z+c.r.z,a.vx+c.v.x,a.vy+c.v.y,a.vz+c.v.z,b);default:if(UserDefinedStar(a))return a=HelioVector(a,b),new StateVector(a.x,a.y,a.z,0,0,0,b);throw'HelioState: Unsupported body "'+a+'"';}}exports.HelioState=HelioState;
function QuadInterp(a,b,c,d,e){var f=(e+c)/2-d;c=(e-c)/2;if(0==f){if(0==c)return null;d=-d/c;if(-1>d||1<d)return null}else{d=c*c-4*f*d;if(0>=d)return null;e=Math.sqrt(d);d=(-c+e)/(2*f);e=(-c-e)/(2*f);if(-1<=d&&1>=d){if(-1<=e&&1>=e)return null}else if(-1<=e&&1>=e)d=e;else return null}return{t:a+d*b,df_dt:(2*f*d+c)/b}}
function Search(a,b,c,d){var e=VerifyNumber(d&&d.dt_tolerance_seconds||1);e=Math.abs(e/SECONDS_PER_DAY);var f=d&&d.init_f1||a(b),g=d&&d.init_f2||a(c),k=NaN,h=0;d=d&&d.iter_limit||20;for(var l=!0;;){if(++h>d)throw"Excessive iteration in Search()";var m=InterpolateTime(b,c,.5),n=m.ut-b.ut;if(Math.abs(n)<e)return m;l?k=a(m):l=!0;var p=QuadInterp(m.ut,c.ut-m.ut,f,k,g);if(p){var r=MakeTime(p.t),q=a(r);if(0!==p.df_dt){if(Math.abs(q/p.df_dt)<e)return r;p=1.2*Math.abs(q/p.df_dt);if(p<n/10&&(n=r.AddDays(-p),
r=r.AddDays(+p),0>(n.ut-b.ut)*(n.ut-c.ut)&&0>(r.ut-b.ut)*(r.ut-c.ut))){p=a(n);var t=a(r);if(0>p&&0<=t){f=p;g=t;b=n;c=r;k=q;l=!1;continue}}}}if(0>f&&0<=k)c=m,g=k;else if(0>k&&0<=g)b=m,f=k;else return null}}exports.Search=Search;function LongitudeOffset(a){for(;-180>=a;)a+=360;for(;180<a;)a-=360;return a}function NormalizeLongitude(a){for(;0>a;)a+=360;for(;360<=a;)a-=360;return a}
function SearchSunLongitude(a,b,c){VerifyNumber(a);VerifyNumber(c);b=MakeTime(b);c=b.AddDays(c);return Search(function(d){d=SunPosition(d);return LongitudeOffset(d.elon-a)},b,c,{dt_tolerance_seconds:.01})}exports.SearchSunLongitude=SearchSunLongitude;
function PairLongitude(a,b,c){if(a===Body.Earth||b===Body.Earth)throw"The Earth does not have a longitude as seen from itself.";c=MakeTime(c);a=GeoVector(a,c,!1);a=Ecliptic(a);b=GeoVector(b,c,!1);b=Ecliptic(b);return NormalizeLongitude(a.elon-b.elon)}exports.PairLongitude=PairLongitude;function AngleFromSun(a,b){if(a==Body.Earth)throw"The Earth does not have an angle as seen from itself.";var c=MakeTime(b);b=GeoVector(Body.Sun,c,!0);a=GeoVector(a,c,!0);return AngleBetween(b,a)}
exports.AngleFromSun=AngleFromSun;function EclipticLongitude(a,b){if(a===Body.Sun)throw"Cannot calculate heliocentric longitude of the Sun.";a=HelioVector(a,b);return Ecliptic(a).elon}exports.EclipticLongitude=EclipticLongitude;
function VisualMagnitude(a,b,c,d){var e=0,f=0,g=0;switch(a){case Body.Mercury:a=-.6;e=4.98;f=-4.88;g=3.02;break;case Body.Venus:163.6>b?(a=-4.47,e=1.03,f=.57,g=.13):(a=.98,e=-1.02);break;case Body.Mars:a=-1.52;e=1.6;break;case Body.Jupiter:a=-9.4;e=.5;break;case Body.Uranus:a=-7.19;e=.25;break;case Body.Neptune:a=-6.87;break;case Body.Pluto:a=-1;e=4;break;default:throw"VisualMagnitude: unsupported body "+a;}b/=100;return a+b*(e+b*(f+b*g))+5*Math.log10(c*d)}
function SaturnMagnitude(a,b,c,d,e){d=Ecliptic(d);var f=28.06*exports.DEG2RAD,g=exports.DEG2RAD*d.elat;e=Math.asin(Math.sin(g)*Math.cos(f)-Math.cos(g)*Math.sin(f)*Math.sin(exports.DEG2RAD*d.elon-exports.DEG2RAD*(169.51+3.82E-5*e.tt)));d=Math.sin(Math.abs(e));a=-9+.044*a+d*(-2.6+1.2*d)+5*Math.log10(b*c);return{mag:a,ring_tilt:exports.RAD2DEG*e}}function MoonMagnitude(a,b,c){a*=exports.DEG2RAD;var d=a*a;a=-12.717+1.49*Math.abs(a)+.0431*d*d;return a+=5*Math.log10(c/(385000.6/exports.KM_PER_AU)*b)}
var IlluminationInfo=function(a,b,c,d,e,f,g,k){this.time=a;this.mag=b;this.phase_angle=c;this.helio_dist=d;this.geo_dist=e;this.gc=f;this.hc=g;this.ring_tilt=k;this.phase_fraction=(1+Math.cos(exports.DEG2RAD*c))/2};exports.IlluminationInfo=IlluminationInfo;
function Illumination(a,b){if(a===Body.Earth)throw"The illumination of the Earth is not defined.";var c=MakeTime(b),d=CalcVsop(vsop.Earth,c);if(a===Body.Sun){var e=new Vector(-d.x,-d.y,-d.z,c);b=new Vector(0,0,0,c);d=0}else a===Body.Moon?(e=GeoMoon(c),b=new Vector(d.x+e.x,d.y+e.y,d.z+e.z,c)):(b=HelioVector(a,b),e=new Vector(b.x-d.x,b.y-d.y,b.z-d.z,c)),d=AngleBetween(e,b);var f=e.Length(),g=b.Length();if(a===Body.Sun)a=SUN_MAG_1AU+5*Math.log10(f);else if(a===Body.Moon)a=MoonMagnitude(d,g,f);else if(a===
Body.Saturn){var k=SaturnMagnitude(d,g,f,e,c);a=k.mag;k=k.ring_tilt}else a=VisualMagnitude(a,d,g,f);return new IlluminationInfo(c,a,d,g,f,e,b,k)}exports.Illumination=Illumination;function SynodicPeriod(a){if(a===Body.Earth)throw"The Earth does not have a synodic period as seen from itself.";if(a===Body.Moon)return MEAN_SYNODIC_MONTH;var b=Planet[a];if(!b)throw"Not a valid planet name: "+a;a=Planet.Earth.OrbitalPeriod;return Math.abs(a/(a/b.OrbitalPeriod-1))}
function SearchRelativeLongitude(a,b,c){function d(l){var m=EclipticLongitude(a,l);l=EclipticLongitude(Body.Earth,l);return LongitudeOffset(f*(l-m)-b)}VerifyNumber(b);var e=Planet[a];if(!e)throw"Cannot search relative longitude because body is not a planet: "+a;if(a===Body.Earth)throw"Cannot search relative longitude for the Earth (it is always 0)";var f=e.OrbitalPeriod>Planet.Earth.OrbitalPeriod?1:-1;e=SynodicPeriod(a);c=MakeTime(c);var g=d(c);0<g&&(g-=360);for(var k=0;100>k;++k){var h=-g/360*e;
c=c.AddDays(h);if(1>Math.abs(h)*SECONDS_PER_DAY)return c;h=g;g=d(c);30>Math.abs(h)&&h!==g&&(h/=h-g,.5<h&&2>h&&(e*=h))}throw"Relative longitude search failed to converge for "+a+" near "+c.toString()+" (error_angle = "+g+").";}exports.SearchRelativeLongitude=SearchRelativeLongitude;function MoonPhase(a){return PairLongitude(Body.Moon,Body.Sun,a)}exports.MoonPhase=MoonPhase;
function SearchMoonPhase(a,b,c){function d(k){k=MoonPhase(k);return LongitudeOffset(k-a)}VerifyNumber(a);VerifyNumber(c);b=MakeTime(b);var e=d(b);if(0>c){0>e&&(e+=360);var f=-(MEAN_SYNODIC_MONTH*e)/360;e=f+1.5;if(e<c)return null;var g=Math.max(c,f-1.5)}else{0<e&&(e-=360);f=-(MEAN_SYNODIC_MONTH*e)/360;g=f-1.5;if(g>c)return null;e=Math.min(c,f+1.5)}c=b.AddDays(g);b=b.AddDays(e);return Search(d,c,b,{dt_tolerance_seconds:.1})}exports.SearchMoonPhase=SearchMoonPhase;
var MoonQuarter=function(a,b){this.quarter=a;this.time=b};exports.MoonQuarter=MoonQuarter;function SearchMoonQuarter(a){var b=MoonPhase(a);b=(Math.floor(b/90)+1)%4;a=SearchMoonPhase(90*b,a,10);if(!a)throw"Cannot find moon quarter";return new MoonQuarter(b,a)}exports.SearchMoonQuarter=SearchMoonQuarter;function NextMoonQuarter(a){a=new Date(a.time.date.getTime()+6*MILLIS_PER_DAY);return SearchMoonQuarter(a)}exports.NextMoonQuarter=NextMoonQuarter;
var AtmosphereInfo=function(a,b,c){this.pressure=a;this.temperature=b;this.density=c};exports.AtmosphereInfo=AtmosphereInfo;function Atmosphere(a){if(!Number.isFinite(a)||-500>a||1E5<a)throw"Invalid elevation: "+a;if(11E3>=a){var b=288.15-.0065*a;a=101325*Math.pow(288.15/b,-5.25577)}else 2E4>=a?(b=216.65,a=22632*Math.exp(-1.5768832E-4*(a-11E3))):(b=216.65+.001*(a-2E4),a=5474.87*Math.pow(216.65/b,34.16319));return new AtmosphereInfo(a,b,a/b/(101325/288.15))}exports.Atmosphere=Atmosphere;
function HorizonDipAngle(a,b){var c=a.latitude*exports.DEG2RAD,d=Math.sin(c);c=Math.cos(c);var e=1/Math.hypot(c,d*EARTH_FLATTENING),f=(a.height-b)/1E3;a=.175*Math.pow(1-.0065/283.15*(a.height-2/3*b),3.256);return exports.RAD2DEG*-(Math.sqrt(2*(1-a)*b/(1E3*Math.hypot((EARTH_EQUATORIAL_RADIUS_KM*e+f)*c,(EARTH_EQUATORIAL_RADIUS_KM*e*EARTH_FLATTENING*EARTH_FLATTENING+f)*d)))/(1-a))}
function BodyRadiusAu(a){switch(a){case Body.Sun:return SUN_RADIUS_AU;case Body.Moon:return MOON_EQUATORIAL_RADIUS_AU;default:return 0}}function SearchRiseSet(a,b,c,d,e,f){f=void 0===f?0:f;if(!Number.isFinite(f)||0>f)throw"Invalid value for metersAboveGround: "+f;var g=BodyRadiusAu(a),k=Atmosphere(b.height-f);f=HorizonDipAngle(b,f)-REFRACTION_NEAR_HORIZON*k.density;return InternalSearchAltitude(a,b,c,d,e,g,f)}exports.SearchRiseSet=SearchRiseSet;
function SearchAltitude(a,b,c,d,e,f){if(!Number.isFinite(f)||-90>f||90<f)throw"Invalid altitude angle: "+f;return InternalSearchAltitude(a,b,c,d,e,0,f)}exports.SearchAltitude=SearchAltitude;var AscentInfo=function(a,b,c,d){this.tx=a;this.ty=b;this.ax=c;this.ay=d};
function FindAscent(a,b,c,d,e,f,g){if(0>f&&0<=g)return new AscentInfo(d,e,f,g);if(0<=f&&0>g)return null;if(17<a)throw"Excessive recursion in rise/set ascent search.";var k=e.ut-d.ut;if(1>k*SECONDS_PER_DAY||Math.min(Math.abs(f),Math.abs(g))>k/2*c)return null;k=new AstroTime((d.ut+e.ut)/2);var h=b(k);return FindAscent(1+a,b,c,d,k,f,h)||FindAscent(1+a,b,c,k,e,h,g)}
function MaxAltitudeSlope(a,b){if(-90>b||90<b)throw"Invalid geographic latitude: "+b;switch(a){case Body.Moon:a=4.5;var c=8.2;break;case Body.Sun:a=.8;c=.5;break;case Body.Mercury:a=-1.6;c=1;break;case Body.Venus:a=-.8;c=.6;break;case Body.Mars:a=-.5;c=.4;break;case Body.Jupiter:case Body.Saturn:case Body.Uranus:case Body.Neptune:case Body.Pluto:a=-.2;c=.2;break;case Body.Star1:case Body.Star2:case Body.Star3:case Body.Star4:case Body.Star5:case Body.Star6:case Body.Star7:case Body.Star8:a=-.008;
c=.008;break;default:throw"Body not allowed for altitude search: "+a;}b*=exports.DEG2RAD;return Math.abs((360/SOLAR_DAYS_PER_SIDEREAL_DAY-a)*Math.cos(b))+Math.abs(c*Math.sin(b))}
function InternalSearchAltitude(a,b,c,d,e,f,g){function k(q){var t=Equator(a,q,b,!0,!0);q=Horizon(q,b,t.ra,t.dec).altitude+exports.RAD2DEG*Math.asin(f/t.dist);return c*(q-g)}VerifyObserver(b);VerifyNumber(e);VerifyNumber(f);VerifyNumber(g);if(-90>g||90<g)throw"Invalid target altitude angle: "+g;for(var h=MaxAltitudeSlope(a,b.latitude),l=d=MakeTime(d),m=d,n=k(l),p=n;;){0>e?(l=m.AddDays(-.42),n=k(l)):(m=l.AddDays(.42),p=k(m));var r=FindAscent(0,k,h,l,m,n,p);if(r){if(h=Search(k,r.tx,r.ty,{dt_tolerance_seconds:.1,
init_f1:r.ax,init_f2:r.ay})){if(0>e){if(h.ut<d.ut+e)return null}else if(h.ut>d.ut+e)return null;return h}throw"Rise/set search failed after finding ascent: t1="+l+", t2="+m+", a1="+n+", a2="+p;}if(0>e){if(l.ut<d.ut+e)return null;m=l;p=n}else{if(m.ut>d.ut+e)return null;l=m;n=p}}}var HourAngleEvent=function(a,b){this.time=a;this.hor=b};exports.HourAngleEvent=HourAngleEvent;
function SearchHourAngle(a,b,c,d,e){e=void 0===e?1:e;VerifyObserver(b);d=MakeTime(d);var f=0;if(a===Body.Earth)throw"Cannot search for hour angle of the Earth.";VerifyNumber(c);if(0>c||24<=c)throw"Invalid hour angle "+c;VerifyNumber(e);if(0===e)throw"Direction must be positive or negative.";for(;;){++f;var g=sidereal_time(d),k=Equator(a,d,b,!0,!0);g=(c+k.ra-b.longitude/15-g)%24;1===f?0<e?0>g&&(g+=24):0<g&&(g-=24):-12>g?g+=24:12<g&&(g-=24);if(.1>3600*Math.abs(g))return a=Horizon(d,b,k.ra,k.dec,"normal"),
new HourAngleEvent(d,a);d=d.AddDays(g/24*SOLAR_DAYS_PER_SIDEREAL_DAY)}}exports.SearchHourAngle=SearchHourAngle;function HourAngle(a,b,c){var d=MakeTime(b);b=SiderealTime(d);a=Equator(a,d,c,!0,!0);c=(c.longitude/15+b-a.ra)%24;0>c&&(c+=24);return c}exports.HourAngle=HourAngle;var SeasonInfo=function(a,b,c,d){this.mar_equinox=a;this.jun_solstice=b;this.sep_equinox=c;this.dec_solstice=d};exports.SeasonInfo=SeasonInfo;
function Seasons(a){function b(g,k,h){k=new Date(Date.UTC(a,k-1,h));g=SearchSunLongitude(g,k,20);if(!g)throw"Cannot find season change near "+k.toISOString();return g}a instanceof Date&&Number.isFinite(a.getTime())&&(a=a.getUTCFullYear());if(!Number.isSafeInteger(a))throw"Cannot calculate seasons because year argument "+a+" is neither a Date nor a safe integer.";var c=b(0,3,10),d=b(90,6,10),e=b(180,9,10),f=b(270,12,10);return new SeasonInfo(c,d,e,f)}exports.Seasons=Seasons;
var ElongationEvent=function(a,b,c,d){this.time=a;this.visibility=b;this.elongation=c;this.ecliptic_separation=d};exports.ElongationEvent=ElongationEvent;function Elongation(a,b){b=MakeTime(b);var c=PairLongitude(a,Body.Sun,b);if(180<c){var d="morning";c=360-c}else d="evening";a=AngleFromSun(a,b);return new ElongationEvent(b,d,a,c)}exports.Elongation=Elongation;
function SearchMaxElongation(a,b){function c(l){var m=l.AddDays(-.005);l=l.AddDays(.005);m=AngleFromSun(a,m);l=AngleFromSun(a,l);return(m-l)/.01}b=MakeTime(b);var d={Mercury:{s1:50,s2:85},Venus:{s1:40,s2:50}}[a];if(!d)throw"SearchMaxElongation works for Mercury and Venus only.";for(var e=0;2>=++e;){var f=EclipticLongitude(a,b),g=EclipticLongitude(Body.Earth,b),k=LongitudeOffset(f-g),h=f=g=void 0;k>=-d.s1&&k<+d.s1?(h=0,g=+d.s1,f=+d.s2):k>=+d.s2||k<-d.s2?(h=0,g=-d.s2,f=-d.s1):0<=k?(h=-SynodicPeriod(a)/
4,g=+d.s1,f=+d.s2):(h=-SynodicPeriod(a)/4,g=-d.s2,f=-d.s1);k=b.AddDays(h);g=SearchRelativeLongitude(a,g,k);f=SearchRelativeLongitude(a,f,g);k=c(g);if(0<=k)throw"SearchMaxElongation: internal error: m1 = "+k;h=c(f);if(0>=h)throw"SearchMaxElongation: internal error: m2 = "+h;k=Search(c,g,f,{init_f1:k,init_f2:h,dt_tolerance_seconds:10});if(!k)throw"SearchMaxElongation: failed search iter "+e+" (t1="+g.toString()+", t2="+f.toString()+")";if(k.tt>=b.tt)return Elongation(a,k);b=f.AddDays(1)}throw"SearchMaxElongation: failed to find event after 2 tries.";
}exports.SearchMaxElongation=SearchMaxElongation;
function SearchPeakMagnitude(a,b){function c(h){var l=h.AddDays(-.005);h=h.AddDays(.005);l=Illumination(a,l).mag;return(Illumination(a,h).mag-l)/.01}if(a!==Body.Venus)throw"SearchPeakMagnitude currently works for Venus only.";b=MakeTime(b);for(var d=0;2>=++d;){var e=EclipticLongitude(a,b),f=EclipticLongitude(Body.Earth,b),g=LongitudeOffset(e-f),k=e=f=void 0;-10<=g&&10>g?(k=0,f=10,e=30):30<=g||-30>g?(k=0,f=-30,e=-10):0<=g?(k=-SynodicPeriod(a)/4,f=10,e=30):(k=-SynodicPeriod(a)/4,f=-30,e=-10);g=b.AddDays(k);
f=SearchRelativeLongitude(a,f,g);e=SearchRelativeLongitude(a,e,f);g=c(f);if(0<=g)throw"SearchPeakMagnitude: internal error: m1 = "+g;k=c(e);if(0>=k)throw"SearchPeakMagnitude: internal error: m2 = "+k;g=Search(c,f,e,{init_f1:g,init_f2:k,dt_tolerance_seconds:10});if(!g)throw"SearchPeakMagnitude: failed search iter "+d+" (t1="+f.toString()+", t2="+e.toString()+")";if(g.tt>=b.tt)return Illumination(a,g);b=e.AddDays(1)}throw"SearchPeakMagnitude: failed to find event after 2 tries.";}
exports.SearchPeakMagnitude=SearchPeakMagnitude;var ApsisKind;(function(a){a[a.Pericenter=0]="Pericenter";a[a.Apocenter=1]="Apocenter"})(ApsisKind=exports.ApsisKind||(exports.ApsisKind={}));var Apsis=function(a,b,c){this.time=a;this.kind=b;this.dist_au=c;this.dist_km=c*exports.KM_PER_AU};exports.Apsis=Apsis;
function SearchLunarApsis(a){function b(k){var h=k.AddDays(-5E-4);k=k.AddDays(5E-4);h=CalcMoon(h).distance_au;return(CalcMoon(k).distance_au-h)/.001}function c(k){return-b(k)}a=MakeTime(a);for(var d=b(a),e=0;5*e<2*MEAN_SYNODIC_MONTH;++e){var f=a.AddDays(5),g=b(f);if(0>=d*g){if(0>d||0<g){a=Search(b,a,f,{init_f1:d,init_f2:g});if(!a)throw"SearchLunarApsis INTERNAL ERROR: perigee search failed!";d=CalcMoon(a).distance_au;return new Apsis(a,0,d)}if(0<d||0>g){a=Search(c,a,f,{init_f1:-d,init_f2:-g});if(!a)throw"SearchLunarApsis INTERNAL ERROR: apogee search failed!";
d=CalcMoon(a).distance_au;return new Apsis(a,1,d)}throw"SearchLunarApsis INTERNAL ERROR: cannot classify apsis event!";}a=f;d=g}throw"SearchLunarApsis INTERNAL ERROR: could not find apsis within 2 synodic months of start date.";}exports.SearchLunarApsis=SearchLunarApsis;
function NextLunarApsis(a){var b=SearchLunarApsis(a.time.AddDays(11));if(1!==b.kind+a.kind)throw"NextLunarApsis INTERNAL ERROR: did not find alternating apogee/perigee: prev="+a.kind+" @ "+a.time.toString()+", next="+b.kind+" @ "+b.time.toString();return b}exports.NextLunarApsis=NextLunarApsis;
function PlanetExtreme(a,b,c,d){for(var e=b===ApsisKind.Apocenter?1:-1;;){d/=9;if(d<1/1440)return c=c.AddDays(d/2),a=HelioDistance(a,c),new Apsis(c,b,a);for(var f=-1,g=0,k=0;10>k;++k){var h=c.AddDays(k*d);h=e*HelioDistance(a,h);if(0==k||h>g)f=k,g=h}c=c.AddDays((f-1)*d);d*=2}}
function BruteSearchPlanetApsis(a,b){var c=b.AddDays(-30/360*Planet[a].OrbitalPeriod),d=b.AddDays(.75*Planet[a].OrbitalPeriod),e=c,f=c,g=-1,k=-1;d=(d.ut-c.ut)/99;for(var h=0;100>h;++h){var l=c.AddDays(h*d),m=HelioDistance(a,l);0===h?k=g=m:(m>k&&(k=m,f=l),m<g&&(g=m,e=l))}c=PlanetExtreme(a,0,e.AddDays(-2*d),4*d);a=PlanetExtreme(a,1,f.AddDays(-2*d),4*d);if(c.time.tt>=b.tt)return a.time.tt>=b.tt&&a.time.tt<c.time.tt?a:c;if(a.time.tt>=b.tt)return a;throw"Internal error: failed to find Neptune apsis.";
}
function SearchPlanetApsis(a,b){function c(m){var n=m.AddDays(-5E-4);m=m.AddDays(5E-4);n=HelioDistance(a,n);return(HelioDistance(a,m)-n)/.001}function d(m){return-c(m)}b=MakeTime(b);if(a===Body.Neptune||a===Body.Pluto)return BruteSearchPlanetApsis(a,b);for(var e=Planet[a].OrbitalPeriod,f=e/6,g=c(b),k=0;k*f<2*e;++k){var h=b.AddDays(f),l=c(h);if(0>=g*l){e=f=void 0;if(0>g||0<l)f=c,e=ApsisKind.Pericenter;else if(0<g||0>l)f=d,e=ApsisKind.Apocenter;else throw"Internal error with slopes in SearchPlanetApsis";b=
Search(f,b,h);if(!b)throw"Failed to find slope transition in planetary apsis search.";g=HelioDistance(a,b);return new Apsis(b,e,g)}b=h;g=l}throw"Internal error: should have found planetary apsis within 2 orbital periods.";}exports.SearchPlanetApsis=SearchPlanetApsis;
function NextPlanetApsis(a,b){if(b.kind!==ApsisKind.Pericenter&&b.kind!==ApsisKind.Apocenter)throw"Invalid apsis kind: "+b.kind;var c=b.time.AddDays(.25*Planet[a].OrbitalPeriod);a=SearchPlanetApsis(a,c);if(1!==a.kind+b.kind)throw"Internal error: previous apsis was "+b.kind+", but found "+a.kind+" for next apsis.";return a}exports.NextPlanetApsis=NextPlanetApsis;
function InverseRotation(a){return new RotationMatrix([[a.rot[0][0],a.rot[1][0],a.rot[2][0]],[a.rot[0][1],a.rot[1][1],a.rot[2][1]],[a.rot[0][2],a.rot[1][2],a.rot[2][2]]])}exports.InverseRotation=InverseRotation;
function CombineRotation(a,b){return new RotationMatrix([[b.rot[0][0]*a.rot[0][0]+b.rot[1][0]*a.rot[0][1]+b.rot[2][0]*a.rot[0][2],b.rot[0][1]*a.rot[0][0]+b.rot[1][1]*a.rot[0][1]+b.rot[2][1]*a.rot[0][2],b.rot[0][2]*a.rot[0][0]+b.rot[1][2]*a.rot[0][1]+b.rot[2][2]*a.rot[0][2]],[b.rot[0][0]*a.rot[1][0]+b.rot[1][0]*a.rot[1][1]+b.rot[2][0]*a.rot[1][2],b.rot[0][1]*a.rot[1][0]+b.rot[1][1]*a.rot[1][1]+b.rot[2][1]*a.rot[1][2],b.rot[0][2]*a.rot[1][0]+b.rot[1][2]*a.rot[1][1]+b.rot[2][2]*a.rot[1][2]],[b.rot[0][0]*
a.rot[2][0]+b.rot[1][0]*a.rot[2][1]+b.rot[2][0]*a.rot[2][2],b.rot[0][1]*a.rot[2][0]+b.rot[1][1]*a.rot[2][1]+b.rot[2][1]*a.rot[2][2],b.rot[0][2]*a.rot[2][0]+b.rot[1][2]*a.rot[2][1]+b.rot[2][2]*a.rot[2][2]]])}exports.CombineRotation=CombineRotation;function IdentityMatrix(){return new RotationMatrix([[1,0,0],[0,1,0],[0,0,1]])}exports.IdentityMatrix=IdentityMatrix;
function Pivot(a,b,c){if(0!==b&&1!==b&&2!==b)throw"Invalid axis "+b+". Must be [0, 1, 2].";var d=VerifyNumber(c)*exports.DEG2RAD;c=Math.cos(d);d=Math.sin(d);var e=(b+1)%3,f=(b+2)%3,g=[[0,0,0],[0,0,0],[0,0,0]];g[e][e]=c*a.rot[e][e]-d*a.rot[e][f];g[e][f]=d*a.rot[e][e]+c*a.rot[e][f];g[e][b]=a.rot[e][b];g[f][e]=c*a.rot[f][e]-d*a.rot[f][f];g[f][f]=d*a.rot[f][e]+c*a.rot[f][f];g[f][b]=a.rot[f][b];g[b][e]=c*a.rot[b][e]-d*a.rot[b][f];g[b][f]=d*a.rot[b][e]+c*a.rot[b][f];g[b][b]=a.rot[b][b];return new RotationMatrix(g)}
exports.Pivot=Pivot;function VectorFromSphere(a,b){b=MakeTime(b);var c=a.lat*exports.DEG2RAD,d=a.lon*exports.DEG2RAD,e=a.dist*Math.cos(c);return new Vector(e*Math.cos(d),e*Math.sin(d),a.dist*Math.sin(c),b)}exports.VectorFromSphere=VectorFromSphere;function EquatorFromVector(a){var b=SphereFromVector(a);return new EquatorialCoordinates(b.lon/15,b.lat,b.dist,a)}exports.EquatorFromVector=EquatorFromVector;
function SphereFromVector(a){var b=a.x*a.x+a.y*a.y,c=Math.sqrt(b+a.z*a.z);if(0===b){if(0===a.z)throw"Zero-length vector not allowed.";var d=0;a=0>a.z?-90:90}else d=exports.RAD2DEG*Math.atan2(a.y,a.x),0>d&&(d+=360),a=exports.RAD2DEG*Math.atan2(a.z,Math.sqrt(b));return new Spherical(a,d,c)}exports.SphereFromVector=SphereFromVector;function ToggleAzimuthDirection(a){a=360-a;360<=a?a-=360:0>a&&(a+=360);return a}
function HorizonFromVector(a,b){a=SphereFromVector(a);a.lon=ToggleAzimuthDirection(a.lon);a.lat+=Refraction(b,a.lat);return a}exports.HorizonFromVector=HorizonFromVector;function VectorFromHorizon(a,b,c){b=MakeTime(b);var d=ToggleAzimuthDirection(a.lon);c=a.lat+InverseRefraction(c,a.lat);a=new Spherical(c,d,a.dist);return VectorFromSphere(a,b)}exports.VectorFromHorizon=VectorFromHorizon;
function Refraction(a,b){VerifyNumber(b);if(-90>b||90<b)return 0;if("normal"===a||"jplhor"===a){var c=b;-1>c&&(c=-1);c=1.02/Math.tan((c+10.3/(c+5.11))*exports.DEG2RAD)/60;"normal"===a&&-1>b&&(c*=(b+90)/89)}else{if(a)throw"Invalid refraction option: "+a;c=0}return c}exports.Refraction=Refraction;function InverseRefraction(a,b){if(-90>b||90<b)return 0;for(var c=b-Refraction(a,b);;){var d=c+Refraction(a,c)-b;if(1E-14>Math.abs(d))return c-b;c-=d}}exports.InverseRefraction=InverseRefraction;
function RotateVector(a,b){return new Vector(a.rot[0][0]*b.x+a.rot[1][0]*b.y+a.rot[2][0]*b.z,a.rot[0][1]*b.x+a.rot[1][1]*b.y+a.rot[2][1]*b.z,a.rot[0][2]*b.x+a.rot[1][2]*b.y+a.rot[2][2]*b.z,b.t)}exports.RotateVector=RotateVector;
function RotateState(a,b){return new StateVector(a.rot[0][0]*b.x+a.rot[1][0]*b.y+a.rot[2][0]*b.z,a.rot[0][1]*b.x+a.rot[1][1]*b.y+a.rot[2][1]*b.z,a.rot[0][2]*b.x+a.rot[1][2]*b.y+a.rot[2][2]*b.z,a.rot[0][0]*b.vx+a.rot[1][0]*b.vy+a.rot[2][0]*b.vz,a.rot[0][1]*b.vx+a.rot[1][1]*b.vy+a.rot[2][1]*b.vz,a.rot[0][2]*b.vx+a.rot[1][2]*b.vy+a.rot[2][2]*b.vz,b.t)}exports.RotateState=RotateState;
function Rotation_EQJ_ECL(){return new RotationMatrix([[1,0,0],[0,.9174821430670688,-.3977769691083922],[0,.3977769691083922,.9174821430670688]])}exports.Rotation_EQJ_ECL=Rotation_EQJ_ECL;function Rotation_ECL_EQJ(){return new RotationMatrix([[1,0,0],[0,.9174821430670688,.3977769691083922],[0,-.3977769691083922,.9174821430670688]])}exports.Rotation_ECL_EQJ=Rotation_ECL_EQJ;
function Rotation_EQJ_EQD(a){a=MakeTime(a);var b=precession_rot(a,PrecessDirection.From2000);a=nutation_rot(a,PrecessDirection.From2000);return CombineRotation(b,a)}exports.Rotation_EQJ_EQD=Rotation_EQJ_EQD;function Rotation_EQJ_ECT(a){var b=MakeTime(a);a=Rotation_EQJ_EQD(b);b=Rotation_EQD_ECT(b);return CombineRotation(a,b)}exports.Rotation_EQJ_ECT=Rotation_EQJ_ECT;function Rotation_ECT_EQJ(a){var b=MakeTime(a);a=Rotation_ECT_EQD(b);b=Rotation_EQD_EQJ(b);return CombineRotation(a,b)}
exports.Rotation_ECT_EQJ=Rotation_ECT_EQJ;function Rotation_EQD_EQJ(a){a=MakeTime(a);var b=nutation_rot(a,PrecessDirection.Into2000);a=precession_rot(a,PrecessDirection.Into2000);return CombineRotation(b,a)}exports.Rotation_EQD_EQJ=Rotation_EQD_EQJ;
function Rotation_EQD_HOR(a,b){a=MakeTime(a);var c=Math.sin(b.latitude*exports.DEG2RAD),d=Math.cos(b.latitude*exports.DEG2RAD),e=Math.sin(b.longitude*exports.DEG2RAD),f=Math.cos(b.longitude*exports.DEG2RAD);b=[d*f,d*e,c];c=[-c*f,-c*e,d];e=[e,-f,0];a=-15*sidereal_time(a);b=spin(a,b);c=spin(a,c);a=spin(a,e);return new RotationMatrix([[c[0],a[0],b[0]],[c[1],a[1],b[1]],[c[2],a[2],b[2]]])}exports.Rotation_EQD_HOR=Rotation_EQD_HOR;
function Rotation_HOR_EQD(a,b){a=Rotation_EQD_HOR(a,b);return InverseRotation(a)}exports.Rotation_HOR_EQD=Rotation_HOR_EQD;function Rotation_HOR_EQJ(a,b){a=MakeTime(a);b=Rotation_HOR_EQD(a,b);a=Rotation_EQD_EQJ(a);return CombineRotation(b,a)}exports.Rotation_HOR_EQJ=Rotation_HOR_EQJ;function Rotation_EQJ_HOR(a,b){a=Rotation_HOR_EQJ(a,b);return InverseRotation(a)}exports.Rotation_EQJ_HOR=Rotation_EQJ_HOR;
function Rotation_EQD_ECL(a){a=Rotation_EQD_EQJ(a);var b=Rotation_EQJ_ECL();return CombineRotation(a,b)}exports.Rotation_EQD_ECL=Rotation_EQD_ECL;function Rotation_ECL_EQD(a){a=Rotation_EQD_ECL(a);return InverseRotation(a)}exports.Rotation_ECL_EQD=Rotation_ECL_EQD;function Rotation_ECL_HOR(a,b){a=MakeTime(a);var c=Rotation_ECL_EQD(a);a=Rotation_EQD_HOR(a,b);return CombineRotation(c,a)}exports.Rotation_ECL_HOR=Rotation_ECL_HOR;
function Rotation_HOR_ECL(a,b){a=Rotation_ECL_HOR(a,b);return InverseRotation(a)}exports.Rotation_HOR_ECL=Rotation_HOR_ECL;function Rotation_EQJ_GAL(){return new RotationMatrix([[-.0548624779711344,.4941095946388765,-.8676668813529025],[-.8734572784246782,-.4447938112296831,-.1980677870294097],[-.483800052994852,.7470034631630423,.4559861124470794]])}exports.Rotation_EQJ_GAL=Rotation_EQJ_GAL;
function Rotation_GAL_EQJ(){return new RotationMatrix([[-.0548624779711344,-.8734572784246782,-.483800052994852],[.4941095946388765,-.4447938112296831,.7470034631630423],[-.8676668813529025,-.1980677870294097,.4559861124470794]])}exports.Rotation_GAL_EQJ=Rotation_GAL_EQJ;function Rotation_ECT_EQD(a){var b=e_tilt(MakeTime(a)).tobl*exports.DEG2RAD;a=Math.cos(b);b=Math.sin(b);return new RotationMatrix([[1,0,0],[0,+a,+b],[0,-b,+a]])}exports.Rotation_ECT_EQD=Rotation_ECT_EQD;
function Rotation_EQD_ECT(a){var b=e_tilt(MakeTime(a)).tobl*exports.DEG2RAD;a=Math.cos(b);b=Math.sin(b);return new RotationMatrix([[1,0,0],[0,+a,-b],[0,+b,+a]])}exports.Rotation_EQD_ECT=Rotation_EQD_ECT;
var ConstelNames=[["And","Andromeda"],["Ant","Antila"],["Aps","Apus"],["Aql","Aquila"],["Aqr","Aquarius"],["Ara","Ara"],["Ari","Aries"],["Aur","Auriga"],["Boo","Bootes"],["Cae","Caelum"],["Cam","Camelopardis"],["Cap","Capricornus"],["Car","Carina"],["Cas","Cassiopeia"],["Cen","Centaurus"],["Cep","Cepheus"],["Cet","Cetus"],["Cha","Chamaeleon"],["Cir","Circinus"],["CMa","Canis Major"],["CMi","Canis Minor"],["Cnc","Cancer"],["Col","Columba"],["Com","Coma Berenices"],["CrA","Corona Australis"],["CrB",
"Corona Borealis"],["Crt","Crater"],["Cru","Crux"],["Crv","Corvus"],["CVn","Canes Venatici"],["Cyg","Cygnus"],["Del","Delphinus"],["Dor","Dorado"],["Dra","Draco"],["Equ","Equuleus"],["Eri","Eridanus"],["For","Fornax"],["Gem","Gemini"],["Gru","Grus"],["Her","Hercules"],["Hor","Horologium"],["Hya","Hydra"],["Hyi","Hydrus"],["Ind","Indus"],["Lac","Lacerta"],["Leo","Leo"],["Lep","Lepus"],["Lib","Libra"],["LMi","Leo Minor"],["Lup","Lupus"],["Lyn","Lynx"],["Lyr","Lyra"],["Men","Mensa"],["Mic","Microscopium"],
["Mon","Monoceros"],["Mus","Musca"],["Nor","Norma"],["Oct","Octans"],["Oph","Ophiuchus"],["Ori","Orion"],["Pav","Pavo"],["Peg","Pegasus"],["Per","Perseus"],["Phe","Phoenix"],["Pic","Pictor"],["PsA","Pisces Austrinus"],["Psc","Pisces"],["Pup","Puppis"],["Pyx","Pyxis"],["Ret","Reticulum"],["Scl","Sculptor"],["Sco","Scorpius"],["Sct","Scutum"],["Ser","Serpens"],["Sex","Sextans"],["Sge","Sagitta"],["Sgr","Sagittarius"],["Tau","Taurus"],["Tel","Telescopium"],["TrA","Triangulum Australe"],["Tri","Triangulum"],
["Tuc","Tucana"],["UMa","Ursa Major"],["UMi","Ursa Minor"],["Vel","Vela"],["Vir","Virgo"],["Vol","Volans"],["Vul","Vulpecula"]],ConstelBounds=[[83,0,8640,2112],[83,2880,5220,2076],[83,7560,8280,2068],[83,6480,7560,2064],[15,0,2880,2040],[10,3300,3840,1968],[15,0,1800,1920],[10,3840,5220,1920],[83,6300,6480,1920],[33,7260,7560,1920],[15,0,1263,1848],[10,4140,4890,1848],[83,5952,6300,1800],[15,7260,7440,1800],[10,2868,3300,1764],[33,3300,4080,1764],[83,4680,5952,1680],[13,1116,1230,1632],[33,7350,7440,
1608],[33,4080,4320,1596],[15,0,120,1584],[83,5040,5640,1584],[15,8490,8640,1584],[33,4320,4860,1536],[33,4860,5190,1512],[15,8340,8490,1512],[10,2196,2520,1488],[33,7200,7350,1476],[15,7393.2,7416,1462],[10,2520,2868,1440],[82,2868,3030,1440],[33,7116,7200,1428],[15,7200,7393.2,1428],[15,8232,8340,1418],[13,0,876,1404],[33,6990,7116,1392],[13,612,687,1380],[13,876,1116,1368],[10,1116,1140,1368],[15,8034,8232,1350],[10,1800,2196,1344],[82,5052,5190,1332],[33,5190,6990,1332],[10,1140,1200,1320],[15,
7968,8034,1320],[15,7416,7908,1316],[13,0,612,1296],[50,2196,2340,1296],[82,4350,4860,1272],[33,5490,5670,1272],[15,7908,7968,1266],[10,1200,1800,1260],[13,8232,8400,1260],[33,5670,6120,1236],[62,735,906,1212],[33,6120,6564,1212],[13,0,492,1200],[62,492,600,1200],[50,2340,2448,1200],[13,8400,8640,1200],[82,4860,5052,1164],[13,0,402,1152],[13,8490,8640,1152],[39,6543,6564,1140],[33,6564,6870,1140],[30,6870,6900,1140],[62,600,735,1128],[82,3030,3300,1128],[13,60,312,1104],[82,4320,4350,1080],[50,2448,
2652,1068],[30,7887,7908,1056],[30,7875,7887,1050],[30,6900,6984,1044],[82,3300,3660,1008],[82,3660,3882,960],[8,5556,5670,960],[39,5670,5880,960],[50,3330,3450,954],[0,0,906,882],[62,906,924,882],[51,6969,6984,876],[62,1620,1689,864],[30,7824,7875,864],[44,7875,7920,864],[7,2352,2652,852],[50,2652,2790,852],[0,0,720,840],[44,7920,8214,840],[44,8214,8232,828],[0,8232,8460,828],[62,924,978,816],[82,3882,3960,816],[29,4320,4440,816],[50,2790,3330,804],[48,3330,3558,804],[0,258,507,792],[8,5466,5556,
792],[0,8460,8550,770],[29,4440,4770,768],[0,8550,8640,752],[29,5025,5052,738],[80,870,978,736],[62,978,1620,736],[7,1620,1710,720],[51,6543,6969,720],[82,3960,4320,696],[30,7080,7530,696],[7,1710,2118,684],[48,3558,3780,684],[29,4770,5025,684],[0,0,24,672],[80,507,600,672],[7,2118,2352,672],[37,2838,2880,672],[30,7530,7824,672],[30,6933,7080,660],[80,690,870,654],[25,5820,5880,648],[8,5430,5466,624],[25,5466,5820,624],[51,6612,6792,624],[48,3870,3960,612],[51,6792,6933,612],[80,600,690,600],[66,
258,306,570],[48,3780,3870,564],[87,7650,7710,564],[77,2052,2118,548],[0,24,51,528],[73,5730,5772,528],[37,2118,2238,516],[87,7140,7290,510],[87,6792,6930,506],[0,51,306,504],[87,7290,7404,492],[37,2811,2838,480],[87,7404,7650,468],[87,6930,7140,460],[6,1182,1212,456],[75,6792,6840,444],[59,2052,2076,432],[37,2238,2271,420],[75,6840,7140,388],[77,1788,1920,384],[39,5730,5790,384],[75,7140,7290,378],[77,1662,1788,372],[77,1920,2016,372],[23,4620,4860,360],[39,6210,6570,344],[23,4272,4620,336],[37,
2700,2811,324],[39,6030,6210,308],[61,0,51,300],[77,2016,2076,300],[37,2520,2700,300],[61,7602,7680,300],[37,2271,2496,288],[39,6570,6792,288],[31,7515,7578,284],[61,7578,7602,284],[45,4146,4272,264],[59,2247,2271,240],[37,2496,2520,240],[21,2811,2853,240],[61,8580,8640,240],[6,600,1182,238],[31,7251,7308,204],[8,4860,5430,192],[61,8190,8580,180],[21,2853,3330,168],[45,3330,3870,168],[58,6570,6718.4,150],[3,6718.4,6792,150],[31,7500,7515,144],[20,2520,2526,132],[73,6570,6633,108],[39,5790,6030,96],
[58,6570,6633,72],[61,7728,7800,66],[66,0,720,48],[73,6690,6792,48],[31,7308,7500,48],[34,7500,7680,48],[61,7680,7728,48],[61,7920,8190,48],[61,7800,7920,42],[20,2526,2592,36],[77,1290,1662,0],[59,1662,1680,0],[20,2592,2910,0],[85,5280,5430,0],[58,6420,6570,0],[16,954,1182,-42],[77,1182,1290,-42],[73,5430,5856,-78],[59,1680,1830,-96],[59,2100,2247,-96],[73,6420,6468,-96],[73,6570,6690,-96],[3,6690,6792,-96],[66,8190,8580,-96],[45,3870,4146,-144],[85,4146,4260,-144],[66,0,120,-168],[66,8580,8640,-168],
[85,5130,5280,-192],[58,5730,5856,-192],[3,7200,7392,-216],[4,7680,7872,-216],[58,6180,6468,-240],[54,2100,2910,-264],[35,1770,1830,-264],[59,1830,2100,-264],[41,2910,3012,-264],[74,3450,3870,-264],[85,4260,4620,-264],[58,6330,6360,-280],[3,6792,7200,-288.8],[35,1740,1770,-348],[4,7392,7680,-360],[73,6180,6570,-384],[72,6570,6792,-384],[41,3012,3090,-408],[58,5856,5895,-438],[41,3090,3270,-456],[26,3870,3900,-456],[71,5856,5895,-462],[47,5640,5730,-480],[28,4530,4620,-528],[85,4620,5130,-528],[41,
3270,3510,-576],[16,600,954,-585.2],[35,954,1350,-585.2],[26,3900,4260,-588],[28,4260,4530,-588],[47,5130,5370,-588],[58,5856,6030,-590],[16,0,600,-612],[11,7680,7872,-612],[4,7872,8580,-612],[16,8580,8640,-612],[41,3510,3690,-636],[35,1692,1740,-654],[46,1740,2202,-654],[11,7200,7680,-672],[41,3690,3810,-700],[41,4530,5370,-708],[47,5370,5640,-708],[71,5640,5760,-708],[35,1650,1692,-720],[58,6030,6336,-720],[76,6336,6420,-720],[41,3810,3900,-748],[19,2202,2652,-792],[41,4410,4530,-792],[41,3900,
4410,-840],[36,1260,1350,-864],[68,3012,3372,-882],[35,1536,1650,-888],[76,6420,6900,-888],[65,7680,8280,-888],[70,8280,8400,-888],[36,1080,1260,-950],[1,3372,3960,-954],[70,0,600,-960],[36,600,1080,-960],[35,1392,1536,-960],[70,8400,8640,-960],[14,5100,5370,-1008],[49,5640,5760,-1008],[71,5760,5911.5,-1008],[9,1740,1800,-1032],[22,1800,2370,-1032],[67,2880,3012,-1032],[35,1230,1392,-1056],[71,5911.5,6420,-1092],[24,6420,6900,-1092],[76,6900,7320,-1092],[53,7320,7680,-1092],[35,1080,1230,-1104],[9,
1620,1740,-1116],[49,5520,5640,-1152],[63,0,840,-1156],[35,960,1080,-1176],[40,1470,1536,-1176],[9,1536,1620,-1176],[38,7680,7920,-1200],[67,2160,2880,-1218],[84,2880,2940,-1218],[35,870,960,-1224],[40,1380,1470,-1224],[63,0,660,-1236],[12,2160,2220,-1260],[84,2940,3042,-1272],[40,1260,1380,-1276],[32,1380,1440,-1276],[63,0,570,-1284],[35,780,870,-1296],[64,1620,1800,-1296],[49,5418,5520,-1296],[84,3042,3180,-1308],[12,2220,2340,-1320],[14,4260,4620,-1320],[49,5100,5418,-1320],[56,5418,5520,-1320],
[32,1440,1560,-1356],[84,3180,3960,-1356],[14,3960,4050,-1356],[5,6300,6480,-1368],[78,6480,7320,-1368],[38,7920,8400,-1368],[40,1152,1260,-1380],[64,1800,1980,-1380],[12,2340,2460,-1392],[63,0,480,-1404],[35,480,780,-1404],[63,8400,8640,-1404],[32,1560,1650,-1416],[56,5520,5911.5,-1440],[43,7320,7680,-1440],[64,1980,2160,-1464],[18,5460,5520,-1464],[5,5911.5,5970,-1464],[18,5370,5460,-1526],[5,5970,6030,-1526],[64,2160,2460,-1536],[12,2460,3252,-1536],[14,4050,4260,-1536],[27,4260,4620,-1536],[14,
4620,5232,-1536],[18,4860,4920,-1560],[5,6030,6060,-1560],[40,780,1152,-1620],[69,1152,1650,-1620],[18,5310,5370,-1620],[5,6060,6300,-1620],[60,6300,6480,-1620],[81,7920,8400,-1620],[32,1650,2370,-1680],[18,4920,5310,-1680],[79,5310,6120,-1680],[81,0,480,-1800],[42,1260,1650,-1800],[86,2370,3252,-1800],[12,3252,4050,-1800],[55,4050,4920,-1800],[60,6480,7680,-1800],[43,7680,8400,-1800],[81,8400,8640,-1800],[81,270,480,-1824],[42,0,1260,-1980],[17,2760,4920,-1980],[2,4920,6480,-1980],[52,1260,2760,
-2040],[57,0,8640,-2160]],ConstelRot,Epoch2000,ConstellationInfo=function(a,b,c,d){this.symbol=a;this.name=b;this.ra1875=c;this.dec1875=d};exports.ConstellationInfo=ConstellationInfo;
function Constellation(a,b){VerifyNumber(a);VerifyNumber(b);if(-90>b||90<b)throw"Invalid declination angle. Must be -90..+90.";a%=24;0>a&&(a+=24);ConstelRot||(ConstelRot=Rotation_EQJ_EQD(new AstroTime(-45655.74141261017)),Epoch2000=new AstroTime(0));a=new Spherical(b,15*a,1);a=VectorFromSphere(a,Epoch2000);a=RotateVector(ConstelRot,a);a=EquatorFromVector(a);b=10/240;for(var c=b/15,d=$jscomp.makeIterator(ConstelBounds),e=d.next();!e.done;e=d.next()){e=e.value;var f=e[1]*c,g=e[2]*c;if(e[3]*b<=a.dec&&
f<=a.ra&&a.ra<g)return b=ConstelNames[e[0]],new ConstellationInfo(b[0],b[1],a.ra,a.dec)}throw"Unable to find constellation for given coordinates.";}exports.Constellation=Constellation;var EclipseKind;(function(a){a.Penumbral="penumbral";a.Partial="partial";a.Annular="annular";a.Total="total"})(EclipseKind=exports.EclipseKind||(exports.EclipseKind={}));var LunarEclipseInfo=function(a,b,c,d,e,f){this.kind=a;this.obscuration=b;this.peak=c;this.sd_penum=d;this.sd_partial=e;this.sd_total=f};
exports.LunarEclipseInfo=LunarEclipseInfo;var ShadowInfo=function(a,b,c,d,e,f,g){this.time=a;this.u=b;this.r=c;this.k=d;this.p=e;this.target=f;this.dir=g};function CalcShadow(a,b,c,d){var e=(d.x*c.x+d.y*c.y+d.z*c.z)/(d.x*d.x+d.y*d.y+d.z*d.z);return new ShadowInfo(b,e,exports.KM_PER_AU*Math.hypot(e*d.x-c.x,e*d.y-c.y,e*d.z-c.z),+SUN_RADIUS_KM-(1+e)*(SUN_RADIUS_KM-a),-SUN_RADIUS_KM+(1+e)*(SUN_RADIUS_KM+a),c,d)}
function EarthShadow(a){var b=GeoVector(Body.Sun,a,!0);b=new Vector(-b.x,-b.y,-b.z,b.t);var c=GeoMoon(a);return CalcShadow(EARTH_ECLIPSE_RADIUS_KM,a,c,b)}function MoonShadow(a){var b=GeoVector(Body.Sun,a,!0),c=GeoMoon(a),d=new Vector(-c.x,-c.y,-c.z,c.t);c.x-=b.x;c.y-=b.y;c.z-=b.z;return CalcShadow(MOON_MEAN_RADIUS_KM,a,d,c)}
function LocalMoonShadow(a,b){var c=geo_pos(a,b);b=GeoVector(Body.Sun,a,!0);var d=GeoMoon(a);c=new Vector(c[0]-d.x,c[1]-d.y,c[2]-d.z,a);d.x-=b.x;d.y-=b.y;d.z-=b.z;return CalcShadow(MOON_MEAN_RADIUS_KM,a,c,d)}function PlanetShadow(a,b,c){a=GeoVector(a,c,!0);var d=GeoVector(Body.Sun,c,!0),e=new Vector(a.x-d.x,a.y-d.y,a.z-d.z,c);d.x=-a.x;d.y=-a.y;d.z=-a.z;return CalcShadow(b,c,d,e)}function ShadowDistanceSlope(a,b){var c=1/86400,d=b.AddDays(-c);b=b.AddDays(+c);d=a(d);return(a(b).r-d.r)/c}
function PlanetShadowSlope(a,b,c){var d=1/86400,e=PlanetShadow(a,b,c.AddDays(-d));return(PlanetShadow(a,b,c.AddDays(+d)).r-e.r)/d}function PeakEarthShadow(a){var b=a.AddDays(-.03);a=a.AddDays(.03);b=Search(function(c){return ShadowDistanceSlope(EarthShadow,c)},b,a);if(!b)throw"Failed to find peak Earth shadow time.";return EarthShadow(b)}
function PeakMoonShadow(a){var b=a.AddDays(-.03);a=a.AddDays(.03);b=Search(function(c){return ShadowDistanceSlope(MoonShadow,c)},b,a);if(!b)throw"Failed to find peak Moon shadow time.";return MoonShadow(b)}function PeakPlanetShadow(a,b,c){var d=c.AddDays(-1);c=c.AddDays(1);d=Search(function(e){return PlanetShadowSlope(a,b,e)},d,c);if(!d)throw"Failed to find peak planet shadow time.";return PlanetShadow(a,b,d)}
function PeakLocalMoonShadow(a,b){function c(f){return LocalMoonShadow(f,b)}var d=a.AddDays(-.2),e=a.AddDays(.2);d=Search(function(f){return ShadowDistanceSlope(c,f)},d,e);if(!d)throw"PeakLocalMoonShadow: search failure for search_center_time = "+a;return LocalMoonShadow(d,b)}
function ShadowSemiDurationMinutes(a,b,c){var d=c/1440;c=a.AddDays(-d);d=a.AddDays(+d);c=Search(function(e){return-(EarthShadow(e).r-b)},c,a);a=Search(function(e){return+(EarthShadow(e).r-b)},a,d);if(!c||!a)throw"Failed to find shadow semiduration";return 720*(a.ut-c.ut)}function MoonEclipticLatitudeDegrees(a){a=CalcMoon(a);return exports.RAD2DEG*a.geo_eclip_lat}
function Obscuration(a,b,c){if(0>=a)throw"Radius of first disc must be positive.";if(0>=b)throw"Radius of second disc must be positive.";if(0>c)throw"Distance between discs is not allowed to be negative.";if(c>=a+b)return 0;if(0==c)return a<=b?1:b*b/(a*a);var d=(a*a-b*b+c*c)/(2*c),e=a*a-d*d;if(0>=e)return a<=b?1:b*b/(a*a);e=Math.sqrt(e);return(a*a*Math.acos(d/a)-d*e+(b*b*Math.acos((c-d)/b)-(c-d)*e))/(Math.PI*a*a)}
function SolarEclipseObscuration(a,b){var c=new Vector(a.x+b.x,a.y+b.y,a.z+b.z,a.t);a=Math.asin(SUN_RADIUS_AU/c.Length());var d=Math.asin(MOON_POLAR_RADIUS_AU/b.Length());b=AngleBetween(b,c);b=Obscuration(a,d,b*exports.DEG2RAD);return Math.min(.9999,b)}
function SearchLunarEclipse(a){a=MakeTime(a);for(var b=0;12>b;++b){var c=SearchMoonPhase(180,a,40);if(!c)throw"Cannot find full moon.";a=MoonEclipticLatitudeDegrees(c);if(1.8>Math.abs(a)&&(a=PeakEarthShadow(c),a.r<a.p+MOON_MEAN_RADIUS_KM)){b=EclipseKind.Penumbral;var d=c=0,e=0,f=ShadowSemiDurationMinutes(a.time,a.p+MOON_MEAN_RADIUS_KM,200);a.r<a.k+MOON_MEAN_RADIUS_KM&&(b=EclipseKind.Partial,e=ShadowSemiDurationMinutes(a.time,a.k+MOON_MEAN_RADIUS_KM,f),a.r+MOON_MEAN_RADIUS_KM<a.k?(b=EclipseKind.Total,
c=1,d=ShadowSemiDurationMinutes(a.time,a.k-MOON_MEAN_RADIUS_KM,e)):c=Obscuration(MOON_MEAN_RADIUS_KM,a.k,a.r));return new LunarEclipseInfo(b,c,a.time,f,e,d)}a=c.AddDays(10)}throw"Failed to find lunar eclipse within 12 full moons.";}exports.SearchLunarEclipse=SearchLunarEclipse;var GlobalSolarEclipseInfo=function(a,b,c,d,e,f){this.kind=a;this.obscuration=b;this.peak=c;this.distance=d;this.latitude=e;this.longitude=f};exports.GlobalSolarEclipseInfo=GlobalSolarEclipseInfo;
function EclipseKindFromUmbra(a){return.014<a?EclipseKind.Total:EclipseKind.Annular}
function GeoidIntersect(a){var b=EclipseKind.Partial,c=a.time,d=a.r,e=Rotation_EQJ_EQD(a.time),f=RotateVector(e,a.dir),g=RotateVector(e,a.target);f.x*=exports.KM_PER_AU;f.y*=exports.KM_PER_AU;f.z*=exports.KM_PER_AU/EARTH_FLATTENING;g.x*=exports.KM_PER_AU;g.y*=exports.KM_PER_AU;g.z*=exports.KM_PER_AU/EARTH_FLATTENING;var k=EARTH_EQUATORIAL_RADIUS_KM,h=f.x*f.x+f.y*f.y+f.z*f.z,l=-2*(f.x*g.x+f.y*g.y+f.z*g.z);k=l*l-4*h*(g.x*g.x+g.y*g.y+g.z*g.z-k*k);if(0<k){var m=(-l-Math.sqrt(k))/(2*h);b=m*f.x-g.x;h=m*
f.y-g.y;f=(m*f.z-g.z)*EARTH_FLATTENING;m=Math.hypot(b,h)*EARTH_FLATTENING_SQUARED;m=0==m?0<f?90:-90:exports.RAD2DEG*Math.atan(f/m);var n=sidereal_time(c);n=(exports.RAD2DEG*Math.atan2(h,b)-15*n)%360;-180>=n?n+=360:180<n&&(n-=360);e=InverseRotation(e);f=new Vector(b/exports.KM_PER_AU,h/exports.KM_PER_AU,f/exports.KM_PER_AU,a.time);f=RotateVector(e,f);f.x+=a.target.x;f.y+=a.target.y;f.z+=a.target.z;e=CalcShadow(MOON_POLAR_RADIUS_KM,a.time,f,a.dir);if(1E-9<e.r||0>e.r)throw"Unexpected shadow distance from geoid intersection = "+
e.r;b=EclipseKindFromUmbra(e.k);a=b===EclipseKind.Total?1:SolarEclipseObscuration(a.dir,f)}else a=void 0;return new GlobalSolarEclipseInfo(b,a,c,d,m,n)}function NextLunarEclipse(a){a=MakeTime(a);a=a.AddDays(10);return SearchLunarEclipse(a)}exports.NextLunarEclipse=NextLunarEclipse;
function SearchGlobalSolarEclipse(a){var b=a=MakeTime(a);for(a=0;12>a;++a){b=SearchMoonPhase(0,b,40);if(!b)throw"Cannot find new moon";var c=MoonEclipticLatitudeDegrees(b);if(1.8>Math.abs(c)&&(c=PeakMoonShadow(b),c.r<c.p+EARTH_MEAN_RADIUS_KM))return GeoidIntersect(c);b=b.AddDays(10)}throw"Failed to find solar eclipse within 12 full moons.";}exports.SearchGlobalSolarEclipse=SearchGlobalSolarEclipse;
function NextGlobalSolarEclipse(a){a=MakeTime(a);a=a.AddDays(10);return SearchGlobalSolarEclipse(a)}exports.NextGlobalSolarEclipse=NextGlobalSolarEclipse;var EclipseEvent=function(a,b){this.time=a;this.altitude=b};exports.EclipseEvent=EclipseEvent;var LocalSolarEclipseInfo=function(a,b,c,d,e,f,g){this.kind=a;this.obscuration=b;this.partial_begin=c;this.total_begin=d;this.peak=e;this.total_end=f;this.partial_end=g};exports.LocalSolarEclipseInfo=LocalSolarEclipseInfo;
function local_partial_distance(a){return a.p-a.r}function local_total_distance(a){return Math.abs(a.k)-a.r}
function LocalEclipse(a,b){var c=CalcEvent(b,a.time),d=a.time.AddDays(-.2),e=a.time.AddDays(.2),f=LocalEclipseTransition(b,1,local_partial_distance,d,a.time),g=LocalEclipseTransition(b,-1,local_partial_distance,a.time,e);if(a.r<Math.abs(a.k)){d=a.time.AddDays(-.01);e=a.time.AddDays(.01);var k=LocalEclipseTransition(b,1,local_total_distance,d,a.time);var h=LocalEclipseTransition(b,-1,local_total_distance,a.time,e);b=EclipseKindFromUmbra(a.k)}else b=EclipseKind.Partial;a=b===EclipseKind.Total?1:SolarEclipseObscuration(a.dir,
a.target);return new LocalSolarEclipseInfo(b,a,f,k,c,h,g)}function LocalEclipseTransition(a,b,c,d,e){d=Search(function(f){f=LocalMoonShadow(f,a);return b*c(f)},d,e);if(!d)throw"Local eclipse transition search failed.";return CalcEvent(a,d)}function CalcEvent(a,b){a=SunAltitude(b,a);return new EclipseEvent(b,a)}function SunAltitude(a,b){var c=Equator(Body.Sun,a,b,!0,!0);return Horizon(a,b,c.ra,c.dec,"normal").altitude}
function SearchLocalSolarEclipse(a,b){a=MakeTime(a);for(VerifyObserver(b);;){a=SearchMoonPhase(0,a,40);if(!a)throw"Cannot find next new moon";var c=MoonEclipticLatitudeDegrees(a);if(1.8>Math.abs(c)&&(c=PeakLocalMoonShadow(a,b),c.r<c.p&&(c=LocalEclipse(c,b),0<c.partial_begin.altitude||0<c.partial_end.altitude)))return c;a=a.AddDays(10)}}exports.SearchLocalSolarEclipse=SearchLocalSolarEclipse;function NextLocalSolarEclipse(a,b){a=MakeTime(a);a=a.AddDays(10);return SearchLocalSolarEclipse(a,b)}
exports.NextLocalSolarEclipse=NextLocalSolarEclipse;var TransitInfo=function(a,b,c,d){this.start=a;this.peak=b;this.finish=c;this.separation=d};exports.TransitInfo=TransitInfo;function PlanetShadowBoundary(a,b,c,d){a=PlanetShadow(b,c,a);return d*(a.r-a.p)}function PlanetTransitBoundary(a,b,c,d,e){c=Search(function(f){return PlanetShadowBoundary(f,a,b,e)},c,d);if(!c)throw"Planet transit boundary search failed";return c}
function SearchTransit(a,b){b=MakeTime(b);switch(a){case Body.Mercury:var c=2439.7;break;case Body.Venus:c=6051.8;break;default:throw"Invalid body: "+a;}for(;;){var d=SearchRelativeLongitude(a,0,b);if(.4>AngleFromSun(a,d)&&(b=PeakPlanetShadow(a,c,d),b.r<b.p)){d=b.time.AddDays(-1);d=PlanetTransitBoundary(a,c,d,b.time,-1);var e=b.time.AddDays(1);c=PlanetTransitBoundary(a,c,b.time,e,1);a=60*AngleFromSun(a,b.time);return new TransitInfo(d,b.time,c,a)}b=d.AddDays(10)}}exports.SearchTransit=SearchTransit;
function NextTransit(a,b){b=MakeTime(b);b=b.AddDays(100);return SearchTransit(a,b)}exports.NextTransit=NextTransit;var NodeEventKind;(function(a){a[a.Invalid=0]="Invalid";a[a.Ascending=1]="Ascending";a[a.Descending=-1]="Descending"})(NodeEventKind=exports.NodeEventKind||(exports.NodeEventKind={}));var NodeEventInfo=function(a,b){this.kind=a;this.time=b};exports.NodeEventInfo=NodeEventInfo;var MoonNodeStepDays=10;
function SearchMoonNode(a){var b=MakeTime(a),c=EclipticGeoMoon(b);for(a={};;a={$jscomp$loop$prop$kind$33:a.$jscomp$loop$prop$kind$33}){var d=b.AddDays(MoonNodeStepDays),e=EclipticGeoMoon(d);if(0>=c.lat*e.lat){a.$jscomp$loop$prop$kind$33=e.lat>c.lat?NodeEventKind.Ascending:NodeEventKind.Descending;b=Search(function(f){return function(g){return f.$jscomp$loop$prop$kind$33*EclipticGeoMoon(g).lat}}(a),b,d);if(!b)throw"Could not find moon node.";return new NodeEventInfo(a.$jscomp$loop$prop$kind$33,b)}b=
d;c=e}}exports.SearchMoonNode=SearchMoonNode;
function NextMoonNode(a){var b=a.time.AddDays(MoonNodeStepDays);b=SearchMoonNode(b);switch(a.kind){case NodeEventKind.Ascending:if(b.kind!==NodeEventKind.Descending)throw"Internal error: previous node was ascending, but this node was: "+b.kind;break;case NodeEventKind.Descending:if(b.kind!==NodeEventKind.Ascending)throw"Internal error: previous node was descending, but this node was: "+b.kind;break;default:throw"Previous node has an invalid node kind: "+a.kind;}return b}exports.NextMoonNode=NextMoonNode;
var AxisInfo=function(a,b,c,d){this.ra=a;this.dec=b;this.spin=c;this.north=d};exports.AxisInfo=AxisInfo;function EarthRotationAxis(a){var b=nutation([0,0,1],a,PrecessDirection.Into2000);b=precession(b,a,PrecessDirection.Into2000);b=new Vector(b[0],b[1],b[2],a);var c=EquatorFromVector(b);return new AxisInfo(c.ra,c.dec,190.41375788700253+360.9856122880876*a.ut,b)}
function RotationAxis(a,b){b=MakeTime(b);var c=b.tt,d=c/36525;switch(a){case Body.Sun:a=286.13;var e=63.87;c=84.176+14.1844*c;break;case Body.Mercury:a=281.0103-.0328*d;e=61.4155-.0049*d;c=329.5988+6.1385108*c+.01067257*Math.sin(exports.DEG2RAD*(174.7910857+4.092335*c))-.00112309*Math.sin(exports.DEG2RAD*(349.5821714+8.18467*c))-1.104E-4*Math.sin(exports.DEG2RAD*(164.3732571+12.277005*c))-2.539E-5*Math.sin(exports.DEG2RAD*(339.1643429+16.36934*c))-5.71E-6*Math.sin(exports.DEG2RAD*(153.9554286+20.461675*
c));break;case Body.Venus:a=272.76;e=67.16;c=160.2-1.4813688*c;break;case Body.Earth:return EarthRotationAxis(b);case Body.Moon:var f=exports.DEG2RAD*(125.045-.0529921*c),g=exports.DEG2RAD*(250.089-.1059842*c),k=exports.DEG2RAD*(260.008+13.0120009*c),h=exports.DEG2RAD*(176.625+13.3407154*c),l=exports.DEG2RAD*(357.529+.9856003*c),m=exports.DEG2RAD*(311.589+26.4057084*c),n=exports.DEG2RAD*(134.963+13.064993*c),p=exports.DEG2RAD*(276.617+.3287146*c),r=exports.DEG2RAD*(34.226+1.7484877*c),q=exports.DEG2RAD*
(15.134-.1589763*c),t=exports.DEG2RAD*(119.743+.0036096*c),H=exports.DEG2RAD*(239.961+.1643573*c),F=exports.DEG2RAD*(25.053+12.9590088*c);a=269.9949+.0031*d-3.8787*Math.sin(f)-.1204*Math.sin(g)+.07*Math.sin(k)-.0172*Math.sin(h)+.0072*Math.sin(m)-.0052*Math.sin(q)+.0043*Math.sin(F);e=66.5392+.013*d+1.5419*Math.cos(f)+.0239*Math.cos(g)-.0278*Math.cos(k)+.0068*Math.cos(h)-.0029*Math.cos(m)+9E-4*Math.cos(n)+8E-4*Math.cos(q)-9E-4*Math.cos(F);c=38.3213+(13.17635815-1.4E-12*c)*c+3.561*Math.sin(f)+.1208*
Math.sin(g)-.0642*Math.sin(k)+.0158*Math.sin(h)+.0252*Math.sin(l)-.0066*Math.sin(m)-.0047*Math.sin(n)-.0046*Math.sin(p)+.0028*Math.sin(r)+.0052*Math.sin(q)+.004*Math.sin(t)+.0019*Math.sin(H)-.0044*Math.sin(F);break;case Body.Mars:a=317.269202-.10927547*d+6.8E-5*Math.sin(exports.DEG2RAD*(198.991226+19139.4819985*d))+2.38E-4*Math.sin(exports.DEG2RAD*(226.292679+38280.8511281*d))+5.2E-5*Math.sin(exports.DEG2RAD*(249.663391+57420.7251593*d))+9E-6*Math.sin(exports.DEG2RAD*(266.18351+76560.636795*d))+.419057*
Math.sin(exports.DEG2RAD*(79.398797+.5042615*d));e=54.432516-.05827105*d+5.1E-5*Math.cos(exports.DEG2RAD*(122.433576+19139.9407476*d))+1.41E-4*Math.cos(exports.DEG2RAD*(43.058401+38280.8753272*d))+3.1E-5*Math.cos(exports.DEG2RAD*(57.663379+57420.7517205*d))+5E-6*Math.cos(exports.DEG2RAD*(79.476401+76560.6495004*d))+1.591274*Math.cos(exports.DEG2RAD*(166.325722+.5042615*d));c=176.049863+350.891982443297*c+1.45E-4*Math.sin(exports.DEG2RAD*(129.071773+19140.0328244*d))+1.57E-4*Math.sin(exports.DEG2RAD*
(36.352167+38281.0473591*d))+4E-5*Math.sin(exports.DEG2RAD*(56.668646+57420.929536*d))+1E-6*Math.sin(exports.DEG2RAD*(67.364003+76560.2552215*d))+1E-6*Math.sin(exports.DEG2RAD*(104.79268+95700.4387578*d))+.584542*Math.sin(exports.DEG2RAD*(95.391654+.5042615*d));break;case Body.Jupiter:e=exports.DEG2RAD*(99.360714+4850.4046*d);f=exports.DEG2RAD*(175.895369+1191.9605*d);g=exports.DEG2RAD*(300.323162+262.5475*d);k=exports.DEG2RAD*(114.012305+6070.2476*d);h=exports.DEG2RAD*(49.511251+64.3*d);a=268.056595-
.006499*d+1.17E-4*Math.sin(e)+9.38E-4*Math.sin(f)+.001432*Math.sin(g)+3E-5*Math.sin(k)+.00215*Math.sin(h);e=64.495303+.002413*d+5E-5*Math.cos(e)+4.04E-4*Math.cos(f)+6.17E-4*Math.cos(g)-1.3E-5*Math.cos(k)+9.26E-4*Math.cos(h);c=284.95+870.536*c;break;case Body.Saturn:a=40.589-.036*d;e=83.537-.004*d;c=38.9+810.7939024*c;break;case Body.Uranus:a=257.311;e=-15.175;c=203.81-501.1600928*c;break;case Body.Neptune:d=exports.DEG2RAD*(357.85+52.316*d);a=299.36+.7*Math.sin(d);e=43.46-.51*Math.cos(d);c=249.978+
541.1397757*c-.48*Math.sin(d);break;case Body.Pluto:a=132.993;e=-6.163;c=302.695+56.3625225*c;break;default:throw"Invalid body: "+a;}d=e*exports.DEG2RAD;f=a*exports.DEG2RAD;g=Math.cos(d);b=new Vector(g*Math.cos(f),g*Math.sin(f),Math.sin(d),b);return new AxisInfo(a/15,e,c,b)}exports.RotationAxis=RotationAxis;
function LagrangePoint(a,b,c,d){var e=MakeTime(b);b=MassProduct(c);var f=MassProduct(d);c===Body.Earth&&d===Body.Moon?(c=new StateVector(0,0,0,0,0,0,e),d=GeoMoonState(e)):(c=HelioState(c,e),d=HelioState(d,e));return LagrangePointFast(a,c,b,d,f)}exports.LagrangePoint=LagrangePoint;
function LagrangePointFast(a,b,c,d,e){if(1>a||5<a)throw"Invalid lagrange point "+a;if(!Number.isFinite(c)||0>=c)throw"Major mass must be a positive number.";if(!Number.isFinite(e)||0>=e)throw"Minor mass must be a negative number.";var f=d.x-b.x,g=d.y-b.y,k=d.z-b.z,h=f*f+g*g+k*k,l=Math.sqrt(h),m=d.vx-b.vx,n=d.vy-b.vy;d=d.vz-b.vz;if(4===a||5===a){h=g*d-k*n;c=k*m-f*d;var p=f*n-g*m,r=c*k-p*g;p=p*f-h*k;h=h*g-c*f;c=Math.sqrt(r*r+p*p+h*h);r/=c;p/=c;h/=c;f/=l;g/=l;k/=l;a=4==a?.8660254037844386:-.8660254037844386;
c=.5*f+a*r;e=.5*g+a*p;var q=.5*k+a*h,t=m*f+n*g+d*k;m=m*r+n*p+d*h;b=new StateVector(l*c,l*e,l*q,t*c+m*(.5*r-a*f),t*e+m*(.5*p-a*g),t*q+m*(.5*h-a*k),b.t)}else{r=e/(c+e)*-l;p=c/(c+e)*+l;h=(c+e)/(h*l);if(1===a||2===a)q=c/(c+e)*Math.cbrt(e/(3*c)),c=-c,1==a?(q=1-q,a=+e):(q=1+q,a=-e);else if(3===a)q=(7/12*e-c)/(e+c),c=+c,a=+e;else throw"Invalid Langrage point "+a+". Must be an integer 1..5.";e=l*q-r;do q=e-r,t=e-p,q=(h*e+c/(q*q)+a/(t*t))/(h-2*c/(q*q*q)-2*a/(t*t*t)),e-=q;while(1E-14<Math.abs(q/l));q=(e-r)/
l;b=new StateVector(q*f,q*g,q*k,q*m,q*n,q*d,b.t)}return b}exports.LagrangePointFast=LagrangePointFast;
var GravitySimulator=function(a,b,c){b=MakeTime(b);this.originBody=a;for(var d=$jscomp.makeIterator(c),e=d.next();!e.done;e=d.next())if(e.value.t.tt!==b.tt)throw"Inconsistent times in bodyStates";d=[];e=GravitySimulator.CalcSolarSystem(b);this.curr=new GravSimEndpoint(b,e,d);a=this.InternalBodyState(a);c=$jscomp.makeIterator(c);for(e=c.next();!e.done;e=c.next()){var f=e.value;e=new TerseVector(f.x+a.r.x,f.y+a.r.y,f.z+a.r.z);f=new TerseVector(f.vx+a.v.x,f.vy+a.v.y,f.vz+a.v.z);var g=TerseVector.zero();
d.push(new body_grav_calc_t(b.tt,e,f,g))}this.CalcBodyAccelerations();this.prev=this.Duplicate()};
GravitySimulator.prototype.Update=function(a){a=MakeTime(a);var b=a.tt-this.curr.time.tt;if(0===b)this.prev=this.Duplicate();else{this.Swap();this.curr.time=a;this.curr.gravitators=GravitySimulator.CalcSolarSystem(a);for(var c=0;c<this.curr.bodies.length;++c){var d=this.prev.bodies[c];this.curr.bodies[c].r=UpdatePosition(b,d.r,d.v,d.a)}this.CalcBodyAccelerations();for(c=0;c<this.curr.bodies.length;++c){d=this.prev.bodies[c];var e=this.curr.bodies[c],f=d.a.mean(e.a);e.tt=a.tt;e.r=UpdatePosition(b,
d.r,d.v,f);e.v=UpdateVelocity(b,d.v,f)}this.CalcBodyAccelerations()}b=[];c=this.InternalBodyState(this.originBody);d=$jscomp.makeIterator(this.curr.bodies);for(e=d.next();!e.done;e=d.next())e=e.value,b.push(new StateVector(e.r.x-c.r.x,e.r.y-c.r.y,e.r.z-c.r.z,e.v.x-c.v.x,e.v.y-c.v.y,e.v.z-c.v.z,a));return b};GravitySimulator.prototype.Swap=function(){var a=this.curr;this.curr=this.prev;this.prev=a};
GravitySimulator.prototype.SolarSystemBodyState=function(a){a=this.InternalBodyState(a);var b=this.InternalBodyState(this.originBody);return ExportState(a.sub(b),this.curr.time)};GravitySimulator.prototype.InternalBodyState=function(a){if(a===Body.SSB)return new body_state_t(this.curr.time.tt,TerseVector.zero(),TerseVector.zero());var b=this.curr.gravitators[a];if(b)return b;throw"Invalid body: "+a;};
GravitySimulator.CalcSolarSystem=function(a){var b={},c=new body_state_t(a.tt,TerseVector.zero(),TerseVector.zero());b[Body.Mercury]=AdjustBarycenterPosVel(c,a.tt,Body.Mercury,MERCURY_GM);b[Body.Venus]=AdjustBarycenterPosVel(c,a.tt,Body.Venus,VENUS_GM);b[Body.Earth]=AdjustBarycenterPosVel(c,a.tt,Body.Earth,EARTH_GM+MOON_GM);b[Body.Mars]=AdjustBarycenterPosVel(c,a.tt,Body.Mars,MARS_GM);b[Body.Jupiter]=AdjustBarycenterPosVel(c,a.tt,Body.Jupiter,JUPITER_GM);b[Body.Saturn]=AdjustBarycenterPosVel(c,a.tt,
Body.Saturn,SATURN_GM);b[Body.Uranus]=AdjustBarycenterPosVel(c,a.tt,Body.Uranus,URANUS_GM);b[Body.Neptune]=AdjustBarycenterPosVel(c,a.tt,Body.Neptune,NEPTUNE_GM);for(var d in b)b[d].r.decr(c.r),b[d].v.decr(c.v);b[Body.Sun]=new body_state_t(a.tt,c.r.neg(),c.v.neg());return b};
GravitySimulator.prototype.CalcBodyAccelerations=function(){for(var a=$jscomp.makeIterator(this.curr.bodies),b=a.next();!b.done;b=a.next())b=b.value,b.a=TerseVector.zero(),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Sun].r,SUN_GM),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Mercury].r,MERCURY_GM),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Venus].r,VENUS_GM),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Earth].r,
EARTH_GM+MOON_GM),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Mars].r,MARS_GM),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Jupiter].r,JUPITER_GM),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Saturn].r,SATURN_GM),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Uranus].r,URANUS_GM),GravitySimulator.AddAcceleration(b.a,b.r,this.curr.gravitators[Body.Neptune].r,NEPTUNE_GM)};
GravitySimulator.AddAcceleration=function(a,b,c,d){var e=c.x-b.x,f=c.y-b.y;b=c.z-b.z;c=e*e+f*f+b*b;d/=c*Math.sqrt(c);a.x+=e*d;a.y+=f*d;a.z+=b*d};GravitySimulator.prototype.Duplicate=function(){var a={};for(b in this.curr.gravitators)a[b]=this.curr.gravitators[b].clone();var b=[];for(var c=$jscomp.makeIterator(this.curr.bodies),d=c.next();!d.done;d=c.next())b.push(d.value.clone());return new GravSimEndpoint(this.curr.time,a,b)};
$jscomp.global.Object.defineProperties(GravitySimulator.prototype,{OriginBody:{configurable:!0,enumerable:!0,get:function(){return this.originBody}},Time:{configurable:!0,enumerable:!0,get:function(){return this.curr.time}}});exports.GravitySimulator=GravitySimulator;var GravSimEndpoint=function(a,b,c){this.time=a;this.gravitators=b;this.bodies=c};
