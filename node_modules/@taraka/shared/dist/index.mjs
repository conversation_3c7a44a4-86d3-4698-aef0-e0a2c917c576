// src/types/user.ts
import { z } from "zod";
var BirthDataSchema = z.object({
  date: z.date(),
  time: z.object({
    hour: z.number().min(0).max(23),
    minute: z.number().min(0).max(59)
  }),
  location: z.object({
    name: z.string().min(1),
    coordinates: z.object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180)
    }),
    timezone: z.string()
  })
});
var UserPreferencesSchema = z.object({
  predictionMode: z.enum(["reflective", "traditional"]),
  astrologySystem: z.enum(["western", "vedic", "mayan"]),
  theme: z.enum(["light", "dark", "auto"]),
  notifications: z.object({
    dailyInsights: z.boolean(),
    weeklyReports: z.boolean(),
    moonPhases: z.boolean()
  }),
  privacy: z.object({
    shareCharts: z.boolean(),
    publicProfile: z.boolean()
  })
});

// src/utils/date.ts
import { format, parseISO, isValid } from "date-fns";
var formatDate = (date, formatString = "yyyy-MM-dd") => {
  const dateObj = typeof date === "string" ? parseISO(date) : date;
  return isValid(dateObj) ? format(dateObj, formatString) : "";
};
var formatTime = (date) => {
  return formatDate(date, "HH:mm");
};
var formatDateTime = (date) => {
  return formatDate(date, "yyyy-MM-dd HH:mm");
};
var isValidDate = (date) => {
  return date instanceof Date && isValid(date);
};
var parseDateTime = (dateString) => {
  const parsed = parseISO(dateString);
  return isValid(parsed) ? parsed : null;
};

// src/utils/validation.ts
import { z as z2 } from "zod";
var validateEmail = (email) => {
  const emailSchema = z2.string().email();
  return emailSchema.safeParse(email).success;
};
var validateCoordinates = (lat, lng) => {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
};
var validateTime = (hour, minute) => {
  return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
};
var sanitizeString = (input) => {
  return input.trim().replace(/[<>]/g, "");
};

// src/utils/formatting.ts
var capitalize = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};
var formatDegree = (degree) => {
  const deg = Math.floor(degree);
  const min = Math.floor((degree - deg) * 60);
  return `${deg}\xB0${min}'`;
};
var formatCoordinates = (lat, lng) => {
  const latDir = lat >= 0 ? "N" : "S";
  const lngDir = lng >= 0 ? "E" : "W";
  return `${Math.abs(lat).toFixed(4)}\xB0${latDir}, ${Math.abs(lng).toFixed(4)}\xB0${lngDir}`;
};
var truncateText = (text, maxLength) => {
  if (text.length <= maxLength)
    return text;
  return text.slice(0, maxLength - 3) + "...";
};

// src/constants/astrology.ts
var ZODIAC_SIGNS = [
  "Aries",
  "Taurus",
  "Gemini",
  "Cancer",
  "Leo",
  "Virgo",
  "Libra",
  "Scorpio",
  "Sagittarius",
  "Capricorn",
  "Aquarius",
  "Pisces"
];
var PLANETS = [
  "Sun",
  "Moon",
  "Mercury",
  "Venus",
  "Mars",
  "Jupiter",
  "Saturn",
  "Uranus",
  "Neptune",
  "Pluto"
];
var HOUSES = Array.from({ length: 12 }, (_, i) => i + 1);
var ASPECTS = [
  "conjunction",
  "opposition",
  "trine",
  "square",
  "sextile",
  "quincunx",
  "semisextile",
  "semisquare"
];
var ASTROLOGY_SYSTEMS = ["western", "vedic", "mayan"];
var PREDICTION_MODES = ["reflective", "traditional"];
var CHART_LAYOUTS = ["wheel", "square", "grid"];

// src/constants/app.ts
var APP_NAME = "Taraka";
var APP_VERSION = "0.1.0";
var THEME_MODES = ["light", "dark", "auto"];
var INSIGHT_THEMES = [
  { id: "love", name: "Love & Relationships", description: "Romantic connections and partnerships" },
  { id: "career", name: "Career & Purpose", description: "Professional growth and life direction" },
  { id: "emotions", name: "Emotions & Healing", description: "Emotional well-being and inner work" },
  { id: "growth", name: "Personal Growth", description: "Self-development and transformation" },
  { id: "creativity", name: "Creativity & Expression", description: "Artistic pursuits and self-expression" },
  { id: "challenge", name: "Challenges & Lessons", description: "Obstacles and learning opportunities" }
];
var DEFAULT_PREFERENCES = {
  predictionMode: "reflective",
  astrologySystem: "western",
  theme: "auto",
  notifications: {
    dailyInsights: true,
    weeklyReports: true,
    moonPhases: false
  },
  privacy: {
    shareCharts: false,
    publicProfile: false
  }
};
export {
  APP_NAME,
  APP_VERSION,
  ASPECTS,
  ASTROLOGY_SYSTEMS,
  BirthDataSchema,
  CHART_LAYOUTS,
  DEFAULT_PREFERENCES,
  HOUSES,
  INSIGHT_THEMES,
  PLANETS,
  PREDICTION_MODES,
  THEME_MODES,
  UserPreferencesSchema,
  ZODIAC_SIGNS,
  capitalize,
  formatCoordinates,
  formatDate,
  formatDateTime,
  formatDegree,
  formatTime,
  isValidDate,
  parseDateTime,
  sanitizeString,
  truncateText,
  validateCoordinates,
  validateEmail,
  validateTime
};
