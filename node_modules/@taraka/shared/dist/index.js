"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  APP_NAME: () => APP_NAME,
  APP_VERSION: () => APP_VERSION,
  ASPECTS: () => ASPECTS,
  ASTROLOGY_SYSTEMS: () => ASTROLOGY_SYSTEMS,
  BirthDataSchema: () => BirthDataSchema,
  CHART_LAYOUTS: () => CHART_LAYOUTS,
  DEFAULT_PREFERENCES: () => DEFAULT_PREFERENCES,
  HOUSES: () => HOUSES,
  INSIGHT_THEMES: () => INSIGHT_THEMES,
  PLANETS: () => PLANETS,
  PREDICTION_MODES: () => PREDICTION_MODES,
  THEME_MODES: () => THEME_MODES,
  UserPreferencesSchema: () => UserPreferencesSchema,
  ZODIAC_SIGNS: () => ZODIAC_SIGNS,
  capitalize: () => capitalize,
  formatCoordinates: () => formatCoordinates,
  formatDate: () => formatDate,
  formatDateTime: () => formatDateTime,
  formatDegree: () => formatDegree,
  formatTime: () => formatTime,
  isValidDate: () => isValidDate,
  parseDateTime: () => parseDateTime,
  sanitizeString: () => sanitizeString,
  truncateText: () => truncateText,
  validateCoordinates: () => validateCoordinates,
  validateEmail: () => validateEmail,
  validateTime: () => validateTime
});
module.exports = __toCommonJS(src_exports);

// src/types/user.ts
var import_zod = require("zod");
var BirthDataSchema = import_zod.z.object({
  date: import_zod.z.date(),
  time: import_zod.z.object({
    hour: import_zod.z.number().min(0).max(23),
    minute: import_zod.z.number().min(0).max(59)
  }),
  location: import_zod.z.object({
    name: import_zod.z.string().min(1),
    coordinates: import_zod.z.object({
      latitude: import_zod.z.number().min(-90).max(90),
      longitude: import_zod.z.number().min(-180).max(180)
    }),
    timezone: import_zod.z.string()
  })
});
var UserPreferencesSchema = import_zod.z.object({
  predictionMode: import_zod.z.enum(["reflective", "traditional"]),
  astrologySystem: import_zod.z.enum(["western", "vedic", "mayan"]),
  theme: import_zod.z.enum(["light", "dark", "auto"]),
  notifications: import_zod.z.object({
    dailyInsights: import_zod.z.boolean(),
    weeklyReports: import_zod.z.boolean(),
    moonPhases: import_zod.z.boolean()
  }),
  privacy: import_zod.z.object({
    shareCharts: import_zod.z.boolean(),
    publicProfile: import_zod.z.boolean()
  })
});

// src/utils/date.ts
var import_date_fns = require("date-fns");
var formatDate = (date, formatString = "yyyy-MM-dd") => {
  const dateObj = typeof date === "string" ? (0, import_date_fns.parseISO)(date) : date;
  return (0, import_date_fns.isValid)(dateObj) ? (0, import_date_fns.format)(dateObj, formatString) : "";
};
var formatTime = (date) => {
  return formatDate(date, "HH:mm");
};
var formatDateTime = (date) => {
  return formatDate(date, "yyyy-MM-dd HH:mm");
};
var isValidDate = (date) => {
  return date instanceof Date && (0, import_date_fns.isValid)(date);
};
var parseDateTime = (dateString) => {
  const parsed = (0, import_date_fns.parseISO)(dateString);
  return (0, import_date_fns.isValid)(parsed) ? parsed : null;
};

// src/utils/validation.ts
var import_zod2 = require("zod");
var validateEmail = (email) => {
  const emailSchema = import_zod2.z.string().email();
  return emailSchema.safeParse(email).success;
};
var validateCoordinates = (lat, lng) => {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
};
var validateTime = (hour, minute) => {
  return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
};
var sanitizeString = (input) => {
  return input.trim().replace(/[<>]/g, "");
};

// src/utils/formatting.ts
var capitalize = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};
var formatDegree = (degree) => {
  const deg = Math.floor(degree);
  const min = Math.floor((degree - deg) * 60);
  return `${deg}\xB0${min}'`;
};
var formatCoordinates = (lat, lng) => {
  const latDir = lat >= 0 ? "N" : "S";
  const lngDir = lng >= 0 ? "E" : "W";
  return `${Math.abs(lat).toFixed(4)}\xB0${latDir}, ${Math.abs(lng).toFixed(4)}\xB0${lngDir}`;
};
var truncateText = (text, maxLength) => {
  if (text.length <= maxLength)
    return text;
  return text.slice(0, maxLength - 3) + "...";
};

// src/constants/astrology.ts
var ZODIAC_SIGNS = [
  "Aries",
  "Taurus",
  "Gemini",
  "Cancer",
  "Leo",
  "Virgo",
  "Libra",
  "Scorpio",
  "Sagittarius",
  "Capricorn",
  "Aquarius",
  "Pisces"
];
var PLANETS = [
  "Sun",
  "Moon",
  "Mercury",
  "Venus",
  "Mars",
  "Jupiter",
  "Saturn",
  "Uranus",
  "Neptune",
  "Pluto"
];
var HOUSES = Array.from({ length: 12 }, (_, i) => i + 1);
var ASPECTS = [
  "conjunction",
  "opposition",
  "trine",
  "square",
  "sextile",
  "quincunx",
  "semisextile",
  "semisquare"
];
var ASTROLOGY_SYSTEMS = ["western", "vedic", "mayan"];
var PREDICTION_MODES = ["reflective", "traditional"];
var CHART_LAYOUTS = ["wheel", "square", "grid"];

// src/constants/app.ts
var APP_NAME = "Taraka";
var APP_VERSION = "0.1.0";
var THEME_MODES = ["light", "dark", "auto"];
var INSIGHT_THEMES = [
  { id: "love", name: "Love & Relationships", description: "Romantic connections and partnerships" },
  { id: "career", name: "Career & Purpose", description: "Professional growth and life direction" },
  { id: "emotions", name: "Emotions & Healing", description: "Emotional well-being and inner work" },
  { id: "growth", name: "Personal Growth", description: "Self-development and transformation" },
  { id: "creativity", name: "Creativity & Expression", description: "Artistic pursuits and self-expression" },
  { id: "challenge", name: "Challenges & Lessons", description: "Obstacles and learning opportunities" }
];
var DEFAULT_PREFERENCES = {
  predictionMode: "reflective",
  astrologySystem: "western",
  theme: "auto",
  notifications: {
    dailyInsights: true,
    weeklyReports: true,
    moonPhases: false
  },
  privacy: {
    shareCharts: false,
    publicProfile: false
  }
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  APP_NAME,
  APP_VERSION,
  ASPECTS,
  ASTROLOGY_SYSTEMS,
  BirthDataSchema,
  CHART_LAYOUTS,
  DEFAULT_PREFERENCES,
  HOUSES,
  INSIGHT_THEMES,
  PLANETS,
  PREDICTION_MODES,
  THEME_MODES,
  UserPreferencesSchema,
  ZODIAC_SIGNS,
  capitalize,
  formatCoordinates,
  formatDate,
  formatDateTime,
  formatDegree,
  formatTime,
  isValidDate,
  parseDateTime,
  sanitizeString,
  truncateText,
  validateCoordinates,
  validateEmail,
  validateTime
});
