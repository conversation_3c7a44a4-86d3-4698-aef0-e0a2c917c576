export interface Coordinates {
  latitude: number;
  longitude: number;
}

export interface Location {
  name: string;
  coordinates: Coordinates;
  timezone: string;
}

export interface BirthData {
  date: Date;
  time: {
    hour: number;
    minute: number;
  };
  location: Location;
}

export type PredictionMode = 'reflective' | 'traditional';
export type AstrologySystem = 'western' | 'vedic' | 'mayan';
export type ChartLayout = 'wheel' | 'square' | 'grid';

export interface Theme {
  id: string;
  name: string;
  description: string;
}

export type InsightTheme = 'love' | 'career' | 'emotions' | 'growth' | 'creativity' | 'challenge';
