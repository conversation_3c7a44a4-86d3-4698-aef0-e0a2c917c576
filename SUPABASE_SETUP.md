# 🌌 Supabase Setup for Taraka

This guide will help you set up Supabase for the Taraka astrology app.

## 🚀 Quick Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Click "Start your project"
3. Sign in with GitHub (recommended)
4. Click "New Project"
5. Choose your organization
6. Fill in project details:
   - **Name**: `taraka-astrology`
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your users
7. Click "Create new project"

### 2. Set up Database Schema

1. Go to your Supabase dashboard
2. Click on "SQL Editor" in the sidebar
3. Click "New Query"
4. Copy and paste the contents of `supabase-schema.sql`
5. Click "Run" to execute the schema

### 3. Configure Authentication

1. Go to "Authentication" → "Settings" in your Supabase dashboard
2. Under "Site URL", add your local development URL:
   ```
   http://localhost:3000
   ```
3. Under "Redirect URLs", add:
   ```
   http://localhost:3000/auth/callback
   http://localhost:3000/auth/reset-password
   ```

#### Enable Google OAuth (Optional)

1. Go to "Authentication" → "Providers"
2. Enable "Google"
3. Add your Google OAuth credentials:
   - **Client ID**: From Google Cloud Console
   - **Client Secret**: From Google Cloud Console
4. Add authorized redirect URI in Google Cloud Console:
   ```
   https://your-project-ref.supabase.co/auth/v1/callback
   ```

### 4. Get Your Environment Variables

1. Go to "Settings" → "API" in your Supabase dashboard
2. Copy the following values:
   - **Project URL**: `https://your-project-ref.supabase.co`
   - **Anon Public Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 5. Configure Environment Variables

1. In your web app directory (`apps/web/`), create a `.env.local` file:
   ```bash
   cp .env.example .env.local
   ```

2. Edit `.env.local` and add your Supabase credentials:
   ```env
   VITE_SUPABASE_URL=https://your-project-ref.supabase.co
   VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

### 6. Test the Connection

1. Restart your development server:
   ```bash
   npm run dev
   ```

2. Visit `http://localhost:3000`
3. You should see the authentication form
4. Try creating an account or signing in

## 🔧 Database Tables

The schema creates the following tables:

### `profiles`
- User profile information
- Birth data (date, time, location, coordinates)
- User preferences (prediction mode, astrology system, theme)
- Onboarding status

### `natal_charts`
- Generated natal charts for each user
- Supports multiple astrology systems (Western, Vedic, Mayan)
- Stores complete chart data as JSON

### `daily_insights`
- Daily astrological insights and predictions
- Supports both reflective and traditional modes
- Includes transit information

### `mood_entries`
- User mood tracking entries
- Links to daily insights
- Supports emotional tags and notes

## 🔐 Security Features

- **Row Level Security (RLS)**: Users can only access their own data
- **Authentication**: Email/password and OAuth providers
- **Automatic Profile Creation**: Profiles are created automatically on signup
- **Data Validation**: Database constraints ensure data integrity

## 🛠️ Development Tips

### Viewing Data
- Use the Supabase dashboard "Table Editor" to view and edit data
- Use the "SQL Editor" to run custom queries

### Debugging Auth
- Check the "Authentication" → "Users" section to see registered users
- Use the browser dev tools to inspect auth tokens

### Database Changes
- Always test schema changes in development first
- Use migrations for production deployments
- Backup your database before major changes

## 📚 Next Steps

Once Supabase is set up:

1. **Test Authentication**: Create an account and sign in
2. **Add Birth Data**: Implement birth data input forms
3. **Generate Charts**: Connect chart generation to user profiles
4. **Add Insights**: Implement daily insight generation
5. **Mood Tracking**: Add mood tracking functionality

## 🆘 Troubleshooting

### Common Issues

**"Invalid API key"**
- Check that your environment variables are correct
- Ensure you're using the anon key, not the service role key

**"Row Level Security policy violation"**
- Check that RLS policies are set up correctly
- Ensure users are authenticated before accessing data

**"Function not found"**
- Make sure you've run the complete schema SQL
- Check the Supabase logs for detailed error messages

### Getting Help

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord](https://discord.supabase.com)
- [GitHub Issues](https://github.com/supabase/supabase/issues)

---

🌟 **Ready to connect with the cosmos!** Your Supabase backend is now configured for the Taraka astrology app.
