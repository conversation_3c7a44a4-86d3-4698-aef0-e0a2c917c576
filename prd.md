# 🌌 Astrology App - Product Requirements Document (PRD)

## 🔍 Overview
A cross-platform astrology app using TypeScript (Web) and React Native (iOS) in a Vite monorepo.  
Users enter their birth details to receive personalized, culturally-informed astrological charts and insights.  
Supports Western, Vedic, and Mayan systems. Focuses on reflection over prediction.  
Supabase for backend/auth. Vercel for web deployment.

---

## 🎯 Goals
- Deliver hyper-personalized astrology across global traditions
- Support emotional insight, journaling, and psychological clarity
- Offer both reflective and traditional interpretation styles
- Design a mystical, modern, mobile-first UX

---

## 🖥 Platforms
- **Web**: TypeScript + Vite
- **iOS**: React Native (Expo or CLI)
- **Monorepo**: Vite + Shared Code
- **Backend**: Supabase (Auth + DB)
- **Deployment**: Vercel

---

## 🌐 Cultural Astrology Modes
- ✦ Western (Tropical)
- ✦ Hindu (Vedic/Jyotish)
- ✦ Mayan (Tzolk’in)
- ➕ Future: Chinese, Native American, Islamic, Celtic
- Modes affect chart type, language, logic, and visuals

---

## ✍️ User Input
- Name (optional)
- Birth date/time/location
- Timezone detection or override
- Choose prediction style (Reflective/Traditional)
- Choose cultural astrology mode

---

## 🧠 Astrology Engine
- Modular: culture-based calculation logic
- Uses Swiss Ephemeris, Panchang APIs, Tzolk’in systems
- JSON/CMS for templated interpretations
- Dynamic tone via prediction mode

---

## 🌌 Visual Natal Chart
- `ChartRenderer`: supports wheel, square, grid layouts
- Tap on planets/signs for symbolism + interpretation
- Animations: planetary motion, moon phases
- Styled: minimal, celestial, meditative

---

## 🔮 Prediction Modes
- 🌙 **Reflective Mode** (default): symbolic, supportive, introspective
- 📜 **Traditional Mode**: classical phrasing based on cultural systems
- 🛠 Users can switch modes in settings or view side-by-side

---

## 📆 Prediction Reports
- Daily/weekly insights based on full chart
- Supports Reflective + Traditional tone
- Linked with mood tracker, journaling
- Export/share options

### 🔍 Insight Filters
- Themes: Love, Career, Emotions, Growth, Creativity, Challenge
- Filter predictions and prompts by focus area

### 🧬 Cosmic Archetype (Phase 2+)
- Personality symbol based on rising sign + chart dynamics
- Examples: “The Visionary,” “The Seeker,” “The Grounded Healer”
- Shown on onboarding, home, and shareable profiles

---

## 📚 Learn Mode
- Glossary of houses, signs, aspects, symbols
- Tooltips and onboarding assist
- 🌐 **Comparative View** (Phase 3+): 
  - See chart differences across Western/Vedic/etc.
  - Ex: “Sun in Leo (Western) → Cancer (Vedic)”

---

## ✨ AI-Powered Astrologer (Phase 4+)
- In-app AI assistant for reflective questions and insights
- Context-aware (birth chart + transits)
- Two tone modes:
  - 🌙 Reflective: soft, symbolic prompts
  - 📜 Traditional: system-specific phrasing
- Sample queries:
  - “Why am I emotional today?”
  - “What does Mars in Leo mean for me?”
- Ends responses with journaling prompts
- Data: structured interpretations, tagged themes
- Ethics: no fatalism, clear disclaimers

---

## 🪐 Additional Features
- Mood Tracker: syncs with Moon or personal transits
- Astrology Journal: guided prompts
- Compatibility Charts: synastry with friends/partners
- Lunar Calendar: with phase rituals
- Share/export charts and insights

---

## 🛠 Development Phases

### Phase 1 (MVP)
- Western + Vedic modes
- Reflective + Traditional prediction styles
- ChartRenderer (wheel, square)
- Daily/weekly reports with mood & insight filters

### Phase 2
- Add Mayan system
- Cosmic Archetypes
- Compatibility + journal features

### Phase 3
- Add Chinese, Native American, Islamic systems
- Comparative chart view
- Rituals, social/community tools

### Phase 4+
- AI Astrologer with Reflective + Traditional chat modes
- Transits-aware prompts
- Suggest insights, rituals, journal entries

---

## 💛 Ethics & Experience
- Tone-first: symbolic, introspective over predictive
- Culturally respectful: clearly labeled systems
- Designed for psychological growth, not prophecy
- Mood-first design: quiet UX, meditative flows
- “Why this matters” added to all chart readings

---

## 🔧 Backend Notes
- Store all insights as tagged blocks (love, growth, etc.)
- Ready for AI use: vector search, smart matching
- Modular API for cultural logic + interpretive layers