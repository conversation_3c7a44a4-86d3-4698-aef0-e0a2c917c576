# 🌌 Taraka - Astrology App

A cross-platform astrology app built with TypeScript, <PERSON>act, and React Native in a modern monorepo setup.

## 🏗️ Architecture

This project uses a monorepo structure with shared packages:

```
taraka/
├── apps/
│   ├── web/          # Vite + React + TypeScript web app
│   └── mobile/       # React Native + Expo mobile app
├── packages/
│   ├── shared/       # Shared utilities, types, and constants
│   ├── astrology/    # Astrology calculation engine
│   └── ui/           # Shared UI components
└── docs/
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm 9+

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd taraka
```

2. Install dependencies:
```bash
npm install --legacy-peer-deps
```

3. Build shared packages:
```bash
npm run setup
```

4. Set up Supabase (see [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)):
   - Create a Supabase project
   - Run the database schema
   - Configure environment variables

5. Create environment file:
```bash
cd apps/web
cp .env.example .env.local
# Edit .env.local with your Supabase credentials
```

6. Start the web app:
```bash
npm run dev:web
```

The web app will be available at `http://localhost:3000`

## 📦 Package Scripts

### Root Level
- `npm run dev` - Start both web and mobile apps
- `npm run dev:web` - Start web app only
- `npm run dev:mobile` - Start mobile app only
- `npm run build` - Build all packages
- `npm run setup` - Install and build all packages
- `npm run lint` - Lint all packages
- `npm run type-check` - Type check all packages

### Individual Packages
- `npm run build --workspace=@taraka/shared` - Build shared package
- `npm run build --workspace=@taraka/astrology` - Build astrology package
- `npm run build --workspace=@taraka/ui` - Build UI package

## 🛠️ Tech Stack

### Web App
- **Framework**: Vite + React 19
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Routing**: React Router
- **State**: React Hooks

### Mobile App
- **Framework**: React Native + Expo
- **Language**: TypeScript
- **Navigation**: React Navigation

### Shared Packages
- **Build Tool**: tsup
- **Bundler**: esbuild
- **Linting**: ESLint + Prettier

### Backend & Services
- **Database**: Supabase
- **Authentication**: Supabase Auth
- **Deployment**: Vercel (web), Expo (mobile)

## 🌟 Features

### Current (MVP)
- ✅ Monorepo setup with shared packages
- ✅ Web app with mystical UI design
- ✅ Basic astrology chart generation
- ✅ Western astrology system support
- ✅ Responsive design with Tailwind CSS

### Planned
- 🔄 Vedic astrology system
- 🔄 Mayan astrology system
- 🔄 User authentication
- 🔄 Birth chart input forms
- 🔄 Daily insights and predictions
- 🔄 Mood tracking and journaling
- 🔄 Mobile app implementation

## 🎨 Design System

The app uses a mystical, celestial design theme with:
- Deep cosmic colors (purples, indigos, starlight)
- Gradient backgrounds
- Smooth animations
- Glass morphism effects
- Responsive typography

## 📱 Development

### Adding New Features

1. **Shared Logic**: Add to `packages/shared/src/`
2. **Astrology Calculations**: Add to `packages/astrology/src/`
3. **UI Components**: Add to `packages/ui/src/`
4. **Web Features**: Add to `apps/web/src/`
5. **Mobile Features**: Add to `apps/mobile/src/`

### Building Packages

Always build packages in dependency order:
1. `@taraka/shared`
2. `@taraka/astrology`
3. `@taraka/ui`
4. Apps (`web`, `mobile`)

## 🔧 Troubleshooting

### Common Issues

1. **Dependency conflicts**: Use `--legacy-peer-deps` flag
2. **Build failures**: Ensure packages are built in correct order
3. **Type errors**: Run `npm run type-check` to identify issues

### Clean Install
```bash
npm run clean
npm install --legacy-peer-deps
npm run setup
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

Built with ❤️ and ✨ cosmic energy
