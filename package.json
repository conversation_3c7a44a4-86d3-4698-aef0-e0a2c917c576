{"name": "taraka", "version": "0.1.0", "private": true, "description": "Cross-platform astrology app with TypeScript and React Native", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:web\" \"npm run dev:mobile\"", "dev:web": "npm run dev --workspace=@taraka/web", "dev:mobile": "npm run dev --workspace=@taraka/mobile", "build": "npm run build --workspaces", "build:web": "npm run build --workspace=@taraka/web", "build:mobile": "npm run build --workspace=@taraka/mobile", "test": "npm run test --workspaces", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "clean": "npm run clean --workspaces && rm -rf node_modules", "setup": "npm install && npm run build --workspace=@taraka/shared && npm run build --workspace=@taraka/astrology && npm run build --workspace=@taraka/ui"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.0.0", "typescript": "^5.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/taraka.git"}, "keywords": ["astrology", "typescript", "react", "react-native", "vite", "monorepo", "supabase"], "author": "tareq", "license": "MIT"}